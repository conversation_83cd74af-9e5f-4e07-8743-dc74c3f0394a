<?php

use bqp\Utils\StringUtils;
use wws\Product\ProductConst;
use wws\Product\ProductExtraServices;
use wws\Product\ProductRepositoryLegacy;
use wws\Shipment\ShipmentRepository;

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'Shop',
];

$config['product_shop.shop_template'] = [
    'label' => 'Template',
    'type' => 'enum',
    'enums' => fn() => ProductRepositoryLegacy::getShopTemplates(1),
    'required' => true
];

$config['product_shop.vk_typ'] = [
    'label' => 'Preis-Typ',
    'type' => 'enum',
    'enums' => [
        ProductConst::VK_TYPE_FORMEL => 'Formel',
        ProductConst::VK_TYPE_BRUTTO => 'Brutto',
        ProductConst::VK_TYPE_NETTO => 'Netto',
        ProductConst::VK_TYPE_SPANNE => 'Spanne'
    ]
];


$config['product_shop.lieferbaranzeige'] = [
    'label' => 'Lieferbarkeit',
    'type' => 'enum',
    'enums' => fn() => ProductRepositoryLegacy::getLieferbaranzeigeNames(),
    'order' => true,
    'as_filter' => [
        'multiple' => true,
        'size' => 5,
    ]
];

$config['product_shop.versand_id'] = [
    'label' => 'Versandart',
    'type' => 'enum',
    'enums' => fn () => ShipmentRepository::getVersandarten(),
    'as_filter' => [
        'multiple' => true,
        'required' => false,
    ]
];

$config['product_shop.extra_services'] = [
    'label' => 'Zusatzleistung',
    'type' => 'string',
    'as_filter_single' => [
        'type' => 'enum',
        'multiple' => false,
        'enums' => fn() => ProductExtraServices::getExtraServicesSimple(),
        'required' => false,
    ]
];

$config['MAKRO.product_shop.vk_brutto'] = [
    'label' => 'VK',
    'type' => 'callback',
    'order' => 'product_shop.vk_brutto',
    'sql' => ['product_shop.vk_brutto', 'product_shop.profit_percentage', 'product_shop.profit_classification'],
    'callback' => function (array $row) {
        $color = \wws\Product\ProductMargin\ProductProfit::getColor($row['profit_classification']);

        return [
            'value' => '<span title="bereinigte Spanne ' . round($row['profit_percentage'] * 100, 2) . ' %">' . StringUtils::formatPrice($row['vk_brutto']) . '</span>',
            'style' => 'background-color: ' . $color . ' ! important; text-align: right;',
            'csv_value' => StringUtils::formatPrice($row['vk_brutto'])
        ];
    }
];

$config['MAKRO.product_shop.park_reason'] = [
    'label' => 'Park Grund',
    'sql' => ['product_shop.park_reason_id'],
    'order' => 'product_shop.park_reason_id',
    'type' => 'callback',
    'callback' => function(array $row) {
        return \wws\Product\ProductRepositoryLegacy::getParkReasonName($row['park_reason_id']);
    }
];

$config['product_shop.vk_brutto'] = [
    'label' => 'VK Brutto',
    'type' => 'currency',
    'order' => true
];


$config['MAKRO.product_shop.versandart'] = [
    'label' => 'Versandart',
    'sql' => ['product_shop.versand_id'],
    'type' => 'callback',
    'alias' => 'versand_id',
    'order' => 'product_shop.versand_id',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_versandart']
];

$config['MAKRO.product_shop.lieferbaranzeige_graphic'] = [
    'label' => 'Verf.',
    'type' => 'string',
    'sql' => ['product_shop.lieferbaranzeige'],
    'order' => 'product_shop.lieferbaranzeige',
    'template' => '<img src="/res/lieferbaranzeige/verf_{{$lieferbaranzeige}}.png" alt="">'
];

return $config;
