<?php

use wws\MarketView\MarketViewFactory;

$config = [];

$config['market_view_history.date_added'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['market_view_history.vk_netto'] = [
    'label' => 'Preis',
    'type' => 'currency'
];

$config['market_view_history.availability_id'] = [
    'label' => 'Verfügbarkeit',
    'order' => true,
    'type' => 'callback',
    'callback' => [['factory' => MarketViewFactory::class, 'MarketViewOutputHelper'], 'tableHelper_availability']
];

$config['MAKRO.market_view_history.price_diff'] = [
    'sql' => ['market_view_product.vk_netto-market_view_history.vk_netto AS price_diff'],
    'label' => 'Preisdifferenz',
    'order' => 'price_diff',
    'type' => 'callback',
    'callback' => function ($daten) {
        if ($daten['history_vk_netto'] === null) {
            return '';
        }

        if ($daten['price_diff'] > 0) {
            $return = '<b style="color:red">' . output::formatPrice($daten['price_diff']) . '</b>';
        } else {
            $return = '<b style="color:green">' . output::formatPrice($daten['price_diff']) . '</b>';
        }

        return $return;
    }
];

$config['MAKRO.market_view_history.price'] = [
    'sql' => ['DATE_FORMAT(market_view_history.date_added,\'%d.%m.%Y\') AS history_date_added', 'market_view_history.vk_netto AS history_vk_netto'],
    'label' => 'alter Preis',
    'order' => 'market_view_history.vk_netto',
    'type' => 'callback',
    'callback' => function ($daten) {
        if ($daten['history_vk_netto'] === null) {
            return '';
        }

        return output::formatPrice($daten['history_vk_netto']) . '<br /><small>' . $daten['history_date_added'] . '</small>';
    }
];

return $config;
