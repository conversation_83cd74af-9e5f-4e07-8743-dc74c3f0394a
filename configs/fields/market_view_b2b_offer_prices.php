<?php

$config = [];

$config['market_view_b2b_offer_prices.vk_netto'] = [
    'label' => 'Preis',
    'type' => 'currency',
    'align' => 'right',
    'order' => true,
    'template' => '<nobr>__value__</nobr>'
];


$config['MAKRO.market_view_b2b_offer_prices.vk'] = [
    'sql' => ['market_view_b2b_offer_prices.vk_netto', 'market_view_b2b_offer_prices.vk_netto_best'],
    'label' => 'EK',
    'order' => 'market_view_b2b_offers.vk_netto',
    'type' => 'callback',
    'callback' => function ($daten) {
        $return = '';

        $return .= output::formatPrice($daten['vk_netto']);

        if ($daten['vk_netto_best'] != $daten['vk_netto'] && $daten['vk_netto_best']) {
            //if(!in_array($daten['availability_id'], array(10,20,25))) {
            $return .= '<br /><small>(Best ' . output::formatPrice($daten['vk_netto_best']) . ')</small>';
            //}
        }

        return $return;
    }
];

return $config;
