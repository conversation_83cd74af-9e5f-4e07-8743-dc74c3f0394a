<?php

use wws\Product\ProductRepositoryLegacy;


$config['market_view_master_product.mv_master_product_id'] = [
    'label' => 'Nr.',
    'type' => 'string',
    'order' => true
];

$config['market_view_master_product.product_name'] = [
    'label' => 'Produkt',
    'type' => 'text',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => function(array $row) {
            return '<a href="/ax/market_view/master_product/overview/?mv_master_product_id=' . $row['mv_master_product_id'] . '" onclick="popup_large(event)">' . $row['product_name'] . '</a>';
        }
    ]
];

$config['market_view_master_product.ean'] = [
    'label' => 'EAN',
    'type' => 'string',
    'order' => true
];

$config['market_view_master_product.mpn'] = [
    'label' => 'Hst-Nr.',
    'type' => 'string',
    'order' => true
];

$config['market_view_master_cat.cat_name'] = [
    'label' => 'Kategorie',
    'type' => 'string',
    'order' => true
];

$config['market_view_master_product.product_type'] = [
    'label' => 'Produktgruppe',
    'type' => 'enum',
    'enums' => [],
    'order' => true,
    'as_table' => [
        'label' => '<span title="Produktgruppe">Typ</span>',
        'type' => 'callback',
        'align' => 'center',
        'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_type']
    ]
];

$config['MAKRO.market_view_master_product.other_sources'] = [
    'sql' => ['market_view_master_product.sources_online', 'market_view_master_product.sources_available'],
    'label' => '<span title="Angebote Online/Bestand">Gänigkeit</span>',
    'order' => 'market_view_master_product.sources_online',
    'template' => '{{$sources_online}}/{{$sources_available}}',
    'type' => 'string',
    'link' => '<a href="/ax/market_view/product/market/?mvsrc_id={{$mvsrc_id}}&mvsrc_product_id={{$mvsrc_product_id|urlencode}}" onclick="popup_large(event)" class="inline">__value__</a>',
    'align' => 'center'
];

$config['MAKRO.market_view_master_product.local_product_info'] = [ //muss extra sein zu MAKRO.market_view_local_product_cache.local_product_info... die product_id kann nicht aus dem cache genommen werden, das ist ein left join
    'sql' => ['market_view_master_product.product_id', 'market_view_local_product_cache.product_tags'],
    'label' => 'Produkt ID',
    'order' => 'market_view_master_product.product_id',
    'type' => 'callback',
    'callback' => [wws\MarketView\MarketViewLocalProductCache::class, 'tableHelper_local_product_info']
];

return $config;
