<?php

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'Export-Archiv',
];

$config['buchhaltung_export_archive.export_id'] = [
    'label' => 'Id',
    'order' => true,
    'type' => 'integer',
];

$config['buchhaltung_export_archive.filename'] = [
    'label' => 'Dateiname',
    'order' => true,
    'type' => 'string',
];

$config['buchhaltung_export_archive.date_added'] = [
    'label' => 'Exportdatum',
    'order' => true,
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s'
];

$config['buchhaltung_export_archive.description'] = [
    'label' => 'Beschreibung',
    'order' => true,
    'type' => 'string',
];

$config['buchhaltung_export_archive.downloaded'] = [
    'label' => 'Heruntergeladen',
    'type' => 'boolean',
    'as_table' => [
        'type' => 'callback',
        'callback' => function($daten, $key) {
            return $daten[$key] ? 'ja' : '';
        }
    ],
];

$config['buchhaltung_export_archive.export_type'] = [
    'label' => 'Typ',
    'type' => 'string',
    'as_table' => [
        'type' => 'callback',
        'callback' => [\wws\buchhaltung\ExportArchiveRepository::class, 'table_helper_type']
    ],
];

return $config;
