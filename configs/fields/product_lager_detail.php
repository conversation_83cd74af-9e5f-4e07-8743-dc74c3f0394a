<?php

use bqp\Utils\StringUtils;
use wws\Product\ProductWarenkorbTypesRepository;

$config = [];

$config['product_lager_detail.lager_frei'] = [
    'label' => 'freier Bestand',
    'type' => 'integer',
    'order' => true,
    'align' => 'center'
];

$config['product_lager_detail.lager_real'] = [
    'label' => 'Bestand (x)',
    'type' => 'integer',
    'order' => true,
    'align' => 'center'
];

$config['product_lager_detail.lager_bestand'] = [
    'label' => 'Bestand',
    'type' => 'integer',
    'order' => true
];

$config['product_lager_detail.lager_abgang'] = [
    'label' => 'Abgang',
    'type' => 'integer',
    'order' => true
];


$config['product_lager_detail.lager_zugang'] = [
    'label' => 'Zugang',
    'type' => 'integer',
    'order' => true
];


$config['product_lager_detail.lager_max'] = [
    'label' => 'max. Bestand',
    'callback' => function($daten) {
        return $daten['lager_max'] >= 0 ? $daten['lager_max'] : '-';
    },
    'type' => 'callback',
    'order' => true
];

$config['MAKRO.product_lager_detail.lager_ek_sum'] = [
    'label' => 'Lagerwert <small>(Lager-EK - Netto)</small>',
    'order' => 'product_lager_detail.lager_real * product_lager_detail.lager_ek',
    'align' => 'right',
    'sql' => ['product_lager_detail.lager_real', 'product_lager_detail.lager_ek', 'product.product_warenkorb_typ'],
    'type' => 'callback',
    'callback' => function (array $row) {
         if (ProductWarenkorbTypesRepository::isBestandCount($row['product_warenkorb_typ'])) {
            return StringUtils::formatPrice($row['lager_ek'] * $row['lager_real']);
         }

         return '-';
    },
    'rollup_complete' =>  function (array $result) {
        $sum = 0;
        foreach ($result as $row) {
            if (ProductWarenkorbTypesRepository::isBestandCount($row['product_warenkorb_typ'])) {
                $sum += $row['lager_ek'] * $row['lager_real'];
            }
        }
        return StringUtils::formatPrice($sum);
    }
];

$config['MAKRO.product_lager_detail.fav_ek_sum'] = [
    'label' => 'Lagerwert <small>(Fav EK - Netto)</small>',
    'order' => 'product_lager_detail.lager_real * product_ek.ek_netto',
    'align' => 'right',
    'sql' => ['product_lager_detail.lager_real', 'product_ek.ek_netto', 'product.product_warenkorb_typ'],
    'type' => 'callback',
    'callback' => function (array $row) {
         if (ProductWarenkorbTypesRepository::isBestandCount($row['product_warenkorb_typ'])) {
            return StringUtils::formatPrice($row['ek_netto'] * $row['lager_real']);
         }

         return '-';
    },
    'rollup_complete' =>  function (array $result) {
        $sum = 0;
        foreach ($result as $row) {
            if (ProductWarenkorbTypesRepository::isBestandCount($row['product_warenkorb_typ'])) {
                $sum += $row['ek_netto'] * $row['lager_real'];
            }
        }
        return StringUtils::formatPrice($sum);
    }
];

return $config;