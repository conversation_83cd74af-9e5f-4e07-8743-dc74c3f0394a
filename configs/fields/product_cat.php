<?php

$config = [];

$config['protokoll'] = [
    'table' => null,
    'entity_id_field' => 'cat_id'
];

$config['table_object_field_select'] = [
    'group_label' => 'Kategorie',
];


$config['product_cat.cat_name'] = [
    'label' => 'Kategorie',
    'type' => 'string',
    'order' => true
];

$config['product_cat.cat_path_text'] = [
    'label' => 'kompletter Pfad',
    'type' => 'text',
];


$config['MAKRO.product_cat.cat_with_parent'] = [
    'label' => 'Kategorie',
    'type' => 'callback',
    'order' => 'product.cat_id',
    'sql' => ['product.cat_id'],
    'callback' => function($daten) {
        static $cache = [];
        $cat_id = $daten['cat_id'];

        if(!isset($cache[$cat_id])) {
            $cache[$cat_id] = db::getInstance()->fieldQuery("
                SELECT
                    CONCAT(IF(t2.cat_name IS NOT NULL, CONCAT(t2.cat_name, ' - '), ''), '<b>', t1.cat_name, '</b>')
                FROM
                    product_cat AS t1 LEFT JOIN
                    product_cat AS t2 ON (t1.parent_cat_id = t2.cat_id)
                WHERE
                    t1.cat_id = '" . $cat_id . "'
            ");

            if(!$cache[$cat_id]) {
                $cache[$cat_id] = 'nix';
            }
        }

        return $cache[$cat_id];
    }
];

$config['MAKRO.product_cat.product_cat'] = [
    'label' => 'Kategorie',
    'order' => 'product_cat.cat_name',
    'sql' => ['product_cat.cat_id', 'product_cat.cat_name'],
    'type' => 'callback',
    'callback' => function (array $row) {
        return '<a href="/ax/artikel/product_list/list/?cat_id=' . $row['cat_id'] . '" target="inline">' . $row['cat_name'] . '</a>';
    }
];

return $config;
