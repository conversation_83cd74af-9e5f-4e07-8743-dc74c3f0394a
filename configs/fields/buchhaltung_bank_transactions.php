<?php

$config = [];

$config['protokoll'] = [
    'table' => 'protokoll_buchhaltung_bank_transactions',
    'entity_id_field' => 'transaction_id',
    'field_modus' => 'id'
];

$config['table_object_field_select'] = [
    'group_label' => 'Transaktionen',
];

$config['buchhaltung_bank_transactions.transaction_id'] = [
    'label' => 'Transaktions-ID',
    'type' => 'int',
    'order' => true,
    'link' => '<a href="/ax/buchhaltung/konto_transaction/transaction/?transaction_id=__VALUE__">__VALUE__</a>'
];

$config['buchhaltung_bank_transactions.transaction_type'] = [
    'label' => 'Typ',
    'type' => 'enum',
    'enums' => service_loader::getDiContainer()->get(\wws\BankAccount\BankTransactionRepository::class)->getTransactionTypes(),
    'order' => true,
    'as_filter' => [
        'multiple' => true,
        'autosize' => true
    ]
];

$config['buchhaltung_bank_transactions.transaction_type_extern'] = [
    'label' => 'Typ (extern)',
    'type' => 'std_string',
    'order' => true
];

$config['buchhaltung_bank_transactions.transaction_date'] = [
    'label' => 'Transaktionsdatum',
    'type' => 'date',
    'order' => true
];

$config['buchhaltung_bank_transactions.accounting_date'] = [
    'label' => 'Buchungsdatum',
    'type' => 'date',
    'order' => true
];

$config['buchhaltung_bank_transactions.date_added'] = [
    'label' => 'Erfassungsdatum',
    'type' => 'date',
    'order' => true
];

$config['buchhaltung_bank_transactions.extern_transaction_id'] = [
    'label' => 'Transaktionsnummer',
    'type' => 'std_string',
];
$config['buchhaltung_bank_transactions.amount'] = [
    'label' => 'Betrag',
    'type' => 'currency',
    'as_table' => [
        'type' => 'currency_high',
        'rollup_complete' => 'SUM'
    ]
];

//@todo achtung währung... macht es sinn das in buchhaltung_bank_transactions.amount zu integrieren? siehe product.versand_id
$config['MAKRO.buchhaltung_bank_transactions.amount'] = [
    'type' => 'currency_high',
    'currency_code' => 'currency_code',
    'label' => 'Betrag',
    'sql' => ['buchhaltung_bank_transactions.amount', 'buchhaltung_bank_transactions.currency_code'],
    'order' => 'buchhaltung_bank_transactions.amount'
];

$config['buchhaltung_bank_transactions.sender_name'] = [
    'label' => 'Absender',
    'type' => 'std_string',
    'order' => true
];

$config['buchhaltung_bank_transactions.text_reference_1'] = [
    'label' => 'Referenz 1',
    'type' => 'std_string',
    'order' => true
];
$config['buchhaltung_bank_transactions.text_reference_2'] = [
    'label' => 'Referenz 2',
    'type' => 'std_string',
    'order' => true
];

$config['buchhaltung_bank_transactions.transaction_status'] = [
    'label' => 'Status',
    'type' => 'enum',
    'enums' => \wws\BankAccount\bank_repository::getTransactionStatusNames(),
    'order' => true,
    'as_filter' => [
        'multiple' => true,
        'autosize' => true
    ]
];


$config['buchhaltung_bank_transactions.settlement_id'] = [
    'label' => 'Abrechnung',
    'type' => 'std_string',
    'template' => '<a href="/ax/buchhaltung/konto_settlement/settlement/?settlement_id=__VALUE__" target="inline">__VALUE__</a>'
];


$config['buchhaltung_bank_transactions.person_konto_nr'] = [
    'label' => 'Datev-Konto',
    'type' => 'std_string',
    'order' => true
];

$config['buchhaltung_bank_transactions.classificator_name'] = [
    'label' => 'Klassifikator',
    'type' => 'std_string',
    'order' => true
];

$config['buchhaltung_bank_transactions.notice'] = [
    'label' => 'Bemerkung',
    'type' => 'memo'
];


$config['buchhaltung_bank_transactions.booking_text'] = [
    'label' => 'Buchungstext',
    'type' => 'string',
    'length_max' => 60,
    'hint' => 'Wird als Buchungstext an die Buchhaltung übergeben. Wenn leer wird dieser automatisch beim Export aus den Buchungsdaten erzeugt.'
];

return $config;
