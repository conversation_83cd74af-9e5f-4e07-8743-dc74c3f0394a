<?php

$config = [];


$config['market_view_cat.mv_cat_id'] = [
    'label' => 'MV-Cat-Id',
    'type' => 'integer',
    'order' => 'true'
];

$config['market_view_cat.mvsrc_cat_name'] = [
    'label' => 'Kategorie',
    'order' => true,
    'type' => 'string'
];

$config['market_view_cat.count_devices'] = [
    'label' => 'Anzahl Geräte',
    'type' => 'integer',
    'order' => true,
    'as_table' => [
        'help' => 'Werte sind nicht live und müssen ggf. von IT aktualisiert werden!',
        'link' => '<a href="/ax/market_view/cat_quick/devices/?mv_cat_id={{$mv_cat_id}}" onclick="popup_large(event)">__VALUE__</a>'
    ],
    'as_filter' => [
        'required' => false
    ]
];

$config['market_view_cat.count_products'] = [
    'label' => 'Anzahl Produkte',
    'type' => 'integer',
    'order' => true,
    'as_table' => [
        'help' => 'Werte sind nicht live und müssen ggf. von IT aktualisiert werden!',
        'link' => '<a href="/ax/market_view/cat_quick/?mv_cat_id={{$mv_cat_id}}" onclick="popup_large(event)">__VALUE__</a>'
    ],
    'as_filter' => [
        'required' => false
    ]
];

return $config;