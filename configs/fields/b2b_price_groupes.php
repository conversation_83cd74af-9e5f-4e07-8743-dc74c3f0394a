<?php

$config = [];

$config['b2b_price_groupes.b2b_price_group_name'] = [
    'label' => 'B2B Preisgruppe',
    'type' => 'string',
    'order' => true
];

$config['b2b_price_groupes.price_10'] = [
    'label' => 'bis 10 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_20'] = [
    'label' => 'bis 20 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_50'] = [
    'label' => 'bis 50 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];
$config['b2b_price_groupes.price_75'] = [
    'label' => 'bis 75 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_100'] = [
    'label' => 'bis 100 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_250'] = [
    'label' => 'bis 250 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_500'] = [
    'label' => 'bis 500 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_750'] = [
    'label' => 'bis 750 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_1000'] = [
    'label' => 'bis 1000 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_2000'] = [
    'label' => 'bis 2000 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

$config['b2b_price_groupes.price_gt_2000'] = [
    'label' => 'ab 2000 €',
    'type' => 'integer',
    'order' => true,
    'template' => '__VALUE__%'
];

return $config;
