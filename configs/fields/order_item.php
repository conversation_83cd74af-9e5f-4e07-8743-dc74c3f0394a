<?php

use bqp\Date\DateUtils;
use wws\Payment\PaymentRepository;
use wws\Product\ProductRepositoryLegacy;
use wws\Shipment\ShipmentRepository;

$config = [];

$config['order_item.quantity'] = [
    'label' => 'Anzahl',
    'type' => 'int',
    'order' => true,
];

$config['order_item.auftnr'] = [
    'label' => 'Auftnr.',
    'type' => 'string',
    'order' => true,
    'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=__value__" onclick="popup_large(event)" class="inline">__value__</a>'
];

$config['order_item.product_id'] = [
    'label' => 'Produkt ID',
    'order' => true,
    'type' => 'callback',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_id']
];

$config['order_item.shop_id'] =  [
    'label' => 'Shop',
    'order' => true,
    'type' => 'callback',
    'callback' => function ($daten, $key) {
        static $shops = null;
        if ($shops === null) {
            $shops = \wws\business_structure\business_structure_factory::getRepository()->getShopNames();
        }
        return $shops[$daten[$key]];
    }
];

$config['order_item.zahlungsart'] = [
    'label' => 'Zahlungsart',
    'type' => 'string',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => [PaymentRepository::class, 'tableHelper_zahlungsart']
    ]
];

$config['order_item.last_date'] = [
    'label' => 'letzte Bearbeitung',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['order_item.added'] = [
    'label' => 'Bestelldatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['MAKRO.order_item.year'] = [
    'label' => 'Bestelljahr',
    'sql' => ['order_item.added AS year'],
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'Y',
    'order' => 'order_item.added'
];


$config['order_item.product'] = [
    'label' => 'Produkt',
    'order' => true,
    'type' => 'string',
    'template' => '<a href="/ax/artikel/product/product/?back=close&product_id={{$product_id}}" onclick="popup_large(event, {name:\'productwin\'})">{{$product}}</a>',
];

$config['order_item.status'] = [
    'label' => 'Status',
    'type' => 'string',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => [order_repository::class, 'tableHelper_status']
    ]
];

$config['order_item.gros_memo'] = [
    'label' => 'Bemerkung',
    'type' => 'string',
];


$config['order_item.ekpreis'] = [
    'label' => 'EK Brutto',
    'order' => true,
    'type' => 'currency'
];

$config['order_item.preis'] = [
    'label' => 'VK Brutto',
    'order' => true,
    'type' => 'currency'
];

$config['MAKRO.order_item.preis_sum'] = [
    'label' => 'VK Brutto (Summe)',
    'sql' => ['order_item.quantity*order_item.preis AS preis_sum'],
    'order' => 'order_item.quantity*order_item.preis',
    'type' => 'currency'
];

$config['MAKRO.order_item.ekpreis_sum'] = [
    'label' => 'EK Brutto (Summe)',
    'sql' => ['order_item.quantity*order_item.ekpreis AS ekpreis_sum'],
    'order' => 'order_item.quantity*order_item.ekpreis',
    'type' => 'currency'
];


$config['MAKRO.order_item.ek_revised_sum'] = [
    'label' => 'EK Brutto bereinigt (Summe)',
    'sql' => ['order_item.quantity*order_item.ek_revised_brutto AS ek_revised_sum'],
    'order' => 'order_item.quantity*order_item.ek_revised_brutto',
    'type' => 'currency'
];

$config['MAKRO.order_item.ertrag_sum'] = [
    'label' => 'Ertrag Brutto (Summe)',
    'sql' => ['order_item.quantity*(order_item.preis-order_item.ekpreis) AS ertrag_sum'],
    'order' => 'order_item.quantity*(order_item.preis-order_item.ekpreis)',
    'type' => 'currency_high'
];

$config['MAKRO.order_item.ertrag_revised_sum'] = [
    'label' => 'Ertrag Brutto bereinigt (Summe)',
    'sql' => ['order_item.quantity*(order_item.preis-order_item.ek_revised_brutto) AS ertrag_revised_sum'],
    'order' => 'order_item.quantity*(order_item.preis-order_item.ek_revised_brutto)',
    'type' => 'currency_high'
];

$config['MAKRO.order_item.group_sum_quantity'] = [
    'label' => 'Einheiten',
    'type' => 'integer',
    'sql' => ['SUM(order_item.quantity) AS group_sum_quantity'],
    'order' => 'SUM(order_item.quantity)',
];

$config['MAKRO.order_item.group_sum_order_count'] = [
    'label' => 'Bestellungen',
    'type' => 'integer',
    'sql' => ['COUNT(DISTINCT order_item.order_id) AS group_sum_order_count'],
    'order' => 'COUNT(DISTINCT order_item.order_id)',
];

$config['MAKRO.order_item.group_sum_preis_sum'] = [
    'label' => 'VK Brutto (Summe)',
    'sql' => ['SUM(order_item.quantity*order_item.preis) AS group_sum_preis_sum'],
    'order' => 'SUM(order_item.quantity*order_item.preis)',
    'type' => 'currency'
];

$config['MAKRO.order_item.group_sum_ekpreis_sum'] = [
    'label' => 'EK Brutto (Summe)',
    'sql' => ['SUM(order_item.quantity*order_item.ekpreis) AS group_sum_ekpreis_sum'],
    'order' => 'SUM(order_item.quantity*order_item.ekpreis)',
    'type' => 'currency'
];


$config['MAKRO.order_item.group_sum_ek_revised_sum'] = [
    'label' => 'EK Brutto bereinigt (Summe)',
    'sql' => ['SUM(order_item.quantity*order_item.ek_revised_brutto) AS group_sum_ek_revised_sum'],
    'order' => 'SUM(order_item.quantity*order_item.ek_revised_brutto)',
    'type' => 'currency'
];

$config['MAKRO.order_item.group_sum_ertrag_sum'] = [
    'label' => 'Ertrag Brutto (Summe)',
    'sql' => ['SUM(order_item.quantity*(order_item.preis-order_item.ekpreis)) AS group_sum_ertrag_sum'],
    'order' => 'SUM(order_item.quantity*(order_item.preis-order_item.ekpreis))',
    'type' => 'currency_high'
];

$config['MAKRO.order_item.group_sum_ertrag_revised_sum'] = [
    'label' => 'Ertrag Brutto bereinigt (Summe)',
    'sql' => ['SUM(order_item.quantity*(order_item.preis-order_item.ek_revised_brutto)) AS group_sum_ertrag_revised_sum'],
    'order' => 'SUM(order_item.quantity*(order_item.preis-order_item.ek_revised_brutto))',
    'type' => 'currency_high'
];

$config['order_item.lieferbaranzeige'] = [
    'label' => 'Lieferzeit',
    'order' => true,
    'type' => 'callback',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_lieferbaranzeige']
];

$config['MAKRO.order_item.product'] = [
    'label' => 'Produkt',
    'type' => 'string',
    'sql' => ['order_item.product_id', 'order_item.product'],
    'order' => 'order_item.product',
    'template' => '<a href="/ax/artikel/product/product/?back=close&product_id={{$product_id}}" onclick="popup_large(event, {name:\'productwin\'})">{{$product}}</a>',
];

$config['MAKRO.order_item.warenkorb_summe'] = [
    'sql' => ['SUM(order_item.preis * order_item.quantity) AS warenkorb_summe'],
    'label' => 'Auftragssumme',
    'order' => 'warenkorb_summe',
    'type' => 'currency'
];

$config['MAKRO.order_item.estimated_delivery'] = [
    'sql' => ['MIN(order_item.estimated_delivery_date) AS min_estimated_delivery_date', 'MAX(order_item.estimated_delivery_date) AS max_estimated_delivery_date'],
    'label' => 'Liefertermin',
    'order' => 'order_item.estimated_delivery_date',
    'type' => 'callback',
    'callback' => function($daten) {
        if($daten['min_estimated_delivery_date'] == $daten['max_estimated_delivery_date']) {
            return DateUtils::convertMysqlDateToDate($daten['min_estimated_delivery_date']);
        }

        return DateUtils::convertMysqlDateToDate($daten['min_estimated_delivery_date']) . ' bis ' . DateUtils::convertMysqlDateToDate($daten['max_estimated_delivery_date']);
    }
];

$config['MAKRO.order_item.estimated_delivery_days_min'] = [
    'sql' => ['(TO_DAYS(NOW()) - TO_DAYS(MIN(order_item.estimated_delivery_date))) AS estimated_delivery_days'],
    'label' => 'Tage überfällig',
    'type' => 'string',
    'order' => 'order_item.estimated_delivery_date',
    'template' => '{{$estimated_delivery_days}}'
];

$config['MAKRO.order_item.estimated_delivery_days'] = [
    'sql' => ['(TO_DAYS(NOW()) - TO_DAYS(order_item.estimated_delivery_date)) AS estimated_delivery_days'],
    'label' => 'Tage überfällig',
    'type' => 'string',
    'order' => 'order_item.estimated_delivery_date',
    'template' => '{{$estimated_delivery_days}}'
];

$config['MAKRO.order_item.estimated_delivery_customer_difference_days'] = [
    'sql' => ['(TO_DAYS(order_item.estimated_delivery_date) - TO_DAYS(order_item.estimated_delivery_date_customer)) AS estimated_delivery_days_diff'],
    'label' => 'Tage',
    'type' => 'string',
    'order' => 'order_item.estimated_delivery_date',
    'template' => '{{$estimated_delivery_days_diff}}'
];

$config['order_item.estimated_delivery_date_customer'] = [
    'label' => 'Liefertermin Kunde',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['order_item.estimated_delivery_date'] = [
    'label' => 'Liefertermin',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];


$config['MAKRO.order_item.grouped_products'] = [
    'label' => 'Produkte',
    'sql' => ["GROUP_CONCAT(CONCAT(order_item.product_id,'||',order_item.quantity,'||',order_item.product) SEPARATOR '|||') AS grouped_products"],
    'order' => 'order_item.product_name',
    'type' => 'callback',
    'callback' => function($daten, $key) {
        $return = '';
        if(!$daten[$key]) {
            return;
        }

        $products = explode('|||', $daten[$key]);

        foreach($products AS $product_temp) {
            [$product_id, $quantity, $product_name] = explode('||', $product_temp);

            $return .= $quantity . ' x ' . '<a href="/ax/artikel/product/product/?back=close&product_id=' . $product_id . '" onclick="popup_large(event, {name:\'productwin\'})">' . $product_name . '</a><br>';
        }

        return $return;
    }
];

$config['order_item.versand_status'] = [
    'label' => '<img src="/res/images/icons/package_go.png" alt="Versandstatus" />',
    'type' => 'callback',
    'order' => true,
    'align' => 'center',
    'callback' => function($daten, $key) {
        return $daten[$key] ? '<img src="/res/images/icons/package_go.png" alt="Versandbereit" />' : '';
    }
];

$config['MAKRO.order_item.sped_logo'] = [
    'sql' => ['order_item.sped_id'],
    'type' => 'callback',
    'label' => 'Sped',
    'order' => 'order_item.sped_id',
    'align' => 'center',
    'callback' => function($daten, $key) {
        return ShipmentRepository::getSpedLogoAsHtml($daten['sped_id']);
    }
];

$config['MAKRO.order_item.min_status'] = [
    'sql' => ['MIN(order_item.status) AS min_status'],
    'label' => 'Status',
    'order' => 'min_status',
    'type' => 'callback',
    'callback' => [order_repository::class, 'tableHelper_status']
];

return $config;
