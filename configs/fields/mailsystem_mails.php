<?php

$config = [];

$config['defaults'] = [
    'mailsystem_mails.mail_id' => null,
    'mailsystem_mails.mailbox_id' => null,
    'mailsystem_mails.folder_id' => -1,
    'mailsystem_mails.prev_mail_id' => 0,
    'mailsystem_mails.mail_note' => '',
    'mailsystem_mails.mailmodus' => 'text',
    'mailsystem_mails.betreff' => '',
    'mailsystem_mails.sender' => '',
    'mailsystem_mails.sender_email' => '',
    'mailsystem_mails.empfaenger' => '',
    'mailsystem_mails.empfaenger_email' => '',
    'mailsystem_mails.date_mail' => null,
    'mailsystem_mails.date_receive' => null,
    'mailsystem_mails.status' => 'unread',
    'mailsystem_mails.body' => '',
    'mailsystem_mails.anlagen' => '',
    'mailsystem_mails.header' => '',
    'mailsystem_mails.mail_tags' => '',
    'mailsystem_mails.customer_id' => 0,
    'mailsystem_mails.order_id' => 0,
    'mailsystem_mails.user_id' => 0,
];


$config['mailsystem_mails.status'] = [
    'label' => 'Status',
    'type' => 'enum',
    'enums' => [
        'unread' => 'ungelesen',
        'read' => 'gelesen',
        'forward' => 'weitergeleitet',
        'answerd' => 'geantwortet'
    ]
];

$config['mailsystem_mails.sender'] = [
    'label' => 'Absender',
    'type' => 'text',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => function(array $row, string $key) {
            $sender = $row[$key];
            $sender = htmlentities($sender);
            $sender = preg_replace('~&lt;(.*)&gt;~', '<span style="color: #3275b0; font-size: 11px;">(\\1)</span>', $sender);

            return $sender;
        }
    ]
];

$config['mailsystem_mails.empfaenger'] = [
    'label' => 'Empfänger',
    'type' => 'text',
    'order' => true
];
$config['mailsystem_mails.betreff'] = [
    'label' => 'Betreff',
    'type' => 'text',
    'order' => true
];

$config['mailsystem_mails.date_mail'] = [
    'label' => 'Datum',
    'type' => 'datetime',
    'order' => true
];

$config['MAKRO.mailsystem_mails.has_attachments'] = [
    'label' => '',
    'type' => 'callback',
    'sql' => ['IF(mailsystem_mails.anlagen != "", 1, 0) AS has_attachments'],
    'order' => 'mailsystem_mails.anlagen',
    'callback' => function($daten) {
        return $daten['has_attachments'] ? '<img src="/res/images/mailsystem/anlage.gif" alt="Anlage">' : '';
    }
];

return $config;
