<?php

$config = [];


$config['supplier_contacts.supplier_contact_id'] = [
    'label' => 'ID',
    'type' => 'string'
];

$config['supplier_contacts.supplier_id'] = [
    'label' => 'Grossisten ID',
    'type' => 'string'
];

$config['supplier_contacts.salutation'] = [
    'label' => 'Anrede',
    'type' => 'string'
];

$config['supplier_contacts.name'] = [
    'label' => 'Name',
    'type' => 'string'
];

$config['supplier_contacts.first_name'] = [
    'label' => 'Vorname',
    'type' => 'string'
];

$config['supplier_contacts.role'] = [
    'label' => 'Funktion',
    'type' => 'string'
];

$config['supplier_contacts.email'] = [
    'label' => 'E-Mail',
    'type' => 'string'
];

$config['supplier_contacts.phone'] = [
    'label' => 'Telefonnummer',
    'type' => 'string'
];

$config['supplier_contacts.mobile_phone'] = [
    'label' => 'Handynummer',
    'type' => 'string'
];

$config['supplier_contacts.fax'] = [
    'label' => 'Fax',
    'type' => 'string'
];

$config['supplier_contacts.comment'] = [
    'label' => 'Bemerkung',
    'type' => 'textarea'
];

$config['supplier_contacts.is_primary'] = [
    'label' => 'Primärer Kontakt',
    'type' => 'enum',
    'order' => true,
    'enums' => [
        0 => 'Nein',
        1 => 'Ja'
    ]
];

$config['MAKRO.supplier_contacts.contact_person'] = [
    'label' => 'Ansprechpartner',
    'type' => 'string',
    'sql' => ['concat(if( supplier_contacts.first_name != \'\',concat(supplier_contacts.first_name,\' \'),\'\') ,supplier_contacts.name) AS contact_person']
];

return $config;
