<?php

$config = [];

$config['buchhaltung_konto_transaktionen.valuta'] = [
    'label' => 'Valuta',
    'type' => 'string',
    'order' => true
];

$config['buchhaltung_konto_transaktionen.buchungsdatum'] = [
    'label' => 'Buchungsdatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['buchhaltung_konto_transaktionen.betrag'] = [
    'label' => 'Betrag',
    'type' => 'currency_high',
    'order' => true
];

$config['buchhaltung_konto_transaktionen.gvc'] = [
    'label' => 'GVC',
    'type' => 'string'
];

$config['buchhaltung_konto_transaktionen.buchungstext'] = [
    'label' => 'BUCHUNGSTEXT',
    'type' => 'string'
];

$config['MAKRO.buchhaltung_konto_transaktionen.verwendungszweck'] = [
    'label' => 'Verwendungszweck',
    'sql' => ['buchhaltung_konto_transaktionen.verwendungszweck_1', 'buchhaltung_konto_transaktionen.verwendungszweck_2'],
    'type' => 'callback',
    'callback' => [wws\buchhaltung\sta_kontoauszuege::class, 'tableHelper_verwendungszweck']
    //'template' => '{{$verwendungszweck_1}}<br />{{$verwendungszweck_2}}'
];

$config['buchhaltung_konto_transaktionen.blz'] = [
    'label' => 'BLZ',
    'type' => 'string'
];

$config['buchhaltung_konto_transaktionen.ktonr'] = [
    'label' => 'Konto',
    'type' => 'string'
];

$config['buchhaltung_konto_transaktionen.name'] = [
    'label' => 'label',
    'type' => 'string'
];

$config['buchhaltung_konto_transaktionen.vorkassen_import_status'] = [
    'label' => 'Status',
    'type' => 'string'
];


return $config;
