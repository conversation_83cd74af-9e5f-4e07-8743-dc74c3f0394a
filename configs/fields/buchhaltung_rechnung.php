<?php

use wws\Order\OrderConst;

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'Belege',
];

$config['buchhaltung_rechnung.rechnungs_nr'] = [
    'label' => 'Belegnr.',
    'type' => 'string',
    'order' => true,
    'template' => '<a href="javascript:invoiceByBelegId({{$rechnungs_id}})">{{$rechnungs_nr}}</a>',
];

$config['buchhaltung_rechnung.rechnungs_datum'] = [
    'label' => 'Belegdatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];


$config['buchhaltung_rechnung.rechnungs_betrag_netto'] = [
    'label' => 'Betrag (Netto)',
    'type' => 'currency_high',
    'order' => true,
    'rollup_complete' => 'SUM'
];

$config['buchhaltung_rechnung.rechnungs_betrag_brutto'] = [
    'label' => 'Betrag (Brutto)',
    'type' => 'currency_high',
    'order' => true,
    'rollup_complete' => 'SUM'
];

$config['buchhaltung_rechnung.rechnungs_type'] = [
    'label' => 'Belegart',
    'order' => true,
    'type' => 'enum',
    'enums' => [
        \wws\buchhaltung\Invoice\InvoiceDocument::INVOICE_TYPE_INVOICE => 'Rechnung',
        \wws\buchhaltung\Invoice\InvoiceDocument::INVOICE_TYPE_GUTSCHRIFT => 'Gutschrift'
    ],
];

$config['buchhaltung_rechnung.tax_status'] = [
    'label' => 'Mwst',
    'order' => true,
    'type' => 'callback',
    'callback' => function($daten, $field) { return $daten[$field] == OrderConst::TAX_STATUS_NORMAL ? 'Ja' : 'Nein'; }
];

return $config;
