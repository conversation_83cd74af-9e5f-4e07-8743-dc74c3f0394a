<?php

use wws\Lager\LagerRepository;
use wws\Supplier\SupplierRepository;

$config['protokoll'] = [
    'table' => 'supplier',
    'entity_id_field' => 'supplier_id'
];

$config['table_object_field_select'] = [
    'group_label' => 'Lieferant',
];

$config['supplier.supplier_id'] = [
    'label' => 'Gros-ID',
    'type' => 'int',
    'order' => true,
    'align' => 'center'
];

$config['supplier.supplier_name'] = [
    'label' => 'Kurzname',
    'type' => 'string',
    'order' => true,
    'required' => true,
    'as_table' => [
        'label' => 'Grossist'
    ],
    'as_filter' => [
        'label' => 'Grossist'
    ]
];

$config['supplier.firma'] = [
    'label' => 'Firma',
    'type' => 'string',
    'order' => true,
    'required' => true
];


$config['supplier.name'] = [
    'label' => 'Name',
    'type' => 'string',
];

$config['supplier.vorname'] = [
    'label' => 'Vorname',
    'type' => 'string',
];


$config['supplier.strasse'] = [
    'label' => 'Strasse',
    'type' => 'string'
];

$config['supplier.plz'] = [
    'label' => 'PLZ',
    'type' => 'string',
];

$config['supplier.ort'] = [
    'label' => 'Ort',
    'type' => 'string',
];

$config['supplier.country_id'] = [
    'label' => 'Land',
    'type' => \bqp\form\FormElementCountry::class,
];

$config['supplier.handler'] = [
    'label' => 'Handler',
    'type' => 'enum',
    'enums' => [6 => 'benutzt GS nicht'],
    'required' => true
];

$config['supplier.lieferanten_artnr'] = [
    'label' => 'Lieferanten-Artnr. auf Bestellschein',
    'type' => 'checkbox',
    'order' => true
];

$config['supplier.bezug'] = [
    'label' => 'Bezug',
    'type' => 'checkbox',
    'order' => true,
    'hint' => 'Voreinstellung für Bezug'
];


$config['supplier.lager_id'] = [
    'label' => 'Lager',
    'type' => 'enum',
    'enums' => function () {
        $temp = new \bqp\form\form_element_laeger();

        $options = [
            0 => 'nicht eingebunden'
        ];
        $options = array_merge($options, $temp->getOptions());

        return $options;
    },
    'empty_option' => false
];

$config['supplier.anlieferung_lager_ids'] = [
    'label' => 'Anlieferung',
    'type' => \bqp\form\form_element_laeger::class,
    'multiple' => true,
    'autosize' => true
];

$config['supplier.min_order_value'] = [
    'label' => 'Mindestbestellwert',
    'type' => 'currency',
    'required' => false
];

$config['supplier.min_order_info'] = [
    'label' => 'Mindestbestellwert Info',
    'type' => 'memo',
];

$config['supplier.processing_time_default'] = [
    'label' => 'Bearbeitungszeit - Standard',
    'type' => 'integer_positiv',
    'order' => true,
    'hint' => 'Legt fest wie lange eine Bestellung i.d.R. über diesen Lieferanten braucht bis Sie in den Versand an den Endkunden geht.<br>' .
              'Bei Lieferanten, die an unser Lager liefern, muss die Lieferung mit einkalkuliert werden.<br>' .
              'Dieser Wert dient aktuell nur zur Kalkulation der Lieferzeit für Amazon, eBay und Kaufland.de.'
];

$config['supplier.processing_time_parcel'] = [
    'label' => 'Bearbeitungszeit - Paket',
    'type' => 'integer_positiv',
    'required' => false,
    'order' => true,
    'hint' => 'Wenn leer wird "Bearbeitungszeit - Standard" genutzt.'
];

$config['supplier.processing_time_freight'] = [
    'label' => 'Bearbeitungszeit - Spedition',
    'type' => 'integer_positiv',
    'required' => false,
    'order' => true,
    'hint' => 'Wenn leer wird "Bearbeitungszeit - Standard" genutzt.'
];

$config['supplier.processing_time_warehouse'] = [
    'label' => 'Bearbeitungszeit - an Lager in Tagen',
    'type' => 'integer_positiv',
    'required' => false,
    'order' => true,
    'hint' => 'Wenn leer, keine Kalkulation der Lagerreichweite möglich.'
];


$config['supplier.ext_url'] = [
    'label' => 'URL',
    'type' => 'string',
];

$config['supplier.ext_username'] = [
    'label' => 'Benutzer',
    'type' => 'string',
];


$config['supplier.ext_other_login'] = [
    'label' => '2ter Benutzer',
    'type' => 'string',
];


$config['supplier.ext_password'] = [
    'label' => 'Passwort',
    'type' => 'string',
];


$config['supplier.jahresbonus'] = [
    'label' => 'Jahresbonus',
    'type' => 'numeric',
];

$config['supplier.skonto'] = [
    'label' => 'Skonto',
    'type' => 'numeric',
];


$config['supplier.zahlungsart'] = [
    'label' => 'Zahlungsart',
    'type' => 'enum',
    'enums' => function() {
        return SupplierRepository::getGrossistZahlungsarten();
    }
];

$config['supplier.zahlungsziel'] = [
    'label' => 'Zahlungsziel',
    'type' => 'string',
    'hint' => 'z.B.:
    <ul>
        <li>Zahlungsziel 14 Tage: <b>TAG=RECHNUNGS_TAG+14</b></li>
        <li>Zahlungsziel 30 Tage: <b>TAG=RECHNUNGS_TAG+30</b></li>
        <li>Zahlungsziel 20ter des Folgemonats: <b>TAG=20;MONAT=RECHNUNGS_MONAT+1</b></li>
    </ul>'
];

$config['supplier.lieferbar_id_sofort'] = [
    'label' => 'Lieferbaranzeige wenn verfügbar',
    'type' => 'enum',
    'enums' => function() {
        return \wws\Product\ProductRepositoryLegacy::getLieferbaranzeigeNames(1);
    },
    'hint' => 'Lieferbaranzeige wenn beim Lieferanten verfügbar.'
];


$config['MAKRO.supplier.processing_time'] = [
    'label' => '<span title="Paket/Spedition">Bearbeitungszeit</span>',
    'type' => 'callback',
    'sql' => ['supplier.processing_time_default', 'supplier.processing_time_parcel', 'supplier.processing_time_freight'],
    'callback' => function (array $row) {
        if (!$row['processing_time_parcel']) {
            $row['processing_time_parcel'] = $row['processing_time_default'];
        }
        if (!$row['processing_time_freight']) {
            $row['processing_time_freight'] = $row['processing_time_default'];
        }

        if ($row['processing_time_parcel'] == $row['processing_time_freight']) {
            return $row['processing_time_parcel'];
        }

        return $row['processing_time_parcel'] . '/' . $row['processing_time_freight'];
    }
];

return $config;
