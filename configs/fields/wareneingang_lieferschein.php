<?php

$config = [];

$config['wareneingang_lieferschein.lieferschein_id'] = [
    'label' => 'Lieferschein ID',
    'type' => 'text',
    'order' => true,
];

$config['wareneingang_lieferschein.lager_id'] = [
    'label' => 'Lager',
    'type' => 'text',
    'order' => true,
];

$config['wareneingang_lieferschein.bemerkung'] = [
    'label' => 'Bemerkung',
    'type' => 'text',
];


$config['wareneingang_lieferschein.lieferscheinnr'] = [
    'label' => 'Lieferscheinnr.',
    'order' => true,
    'type' => 'text',
];

$config['wareneingang_lieferschein.datum'] = [
    'label' => 'Datum',
    'order' => true,
    'type' => 'date',
    'format_output' => \bqp\Date\DateObj::DE_DATETIME
];

$config['wareneingang_lieferschein.status'] = [
    'label' => 'Status',
    'order' => true,
    'type' => 'text',
    'as_table' => [
        'type' => 'callback',
        'callback' => [\wws\Lager\WareneingangLieferscheinRepository::class, 'table_helper_status'],
    ],
];

$config['wareneingang_lieferschein_items.anzahl'] = [
    'label' => 'Anzahl',
    'order' => true,
    'type' => 'integer'
];

return $config;
