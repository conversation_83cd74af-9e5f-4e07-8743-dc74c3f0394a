<?php

use wws\ProductFilterList\ProductFilterList;

$config = [];


$config['protokoll'] = [
    'table' => 'protokoll_filter_lists',
    'entity_id_field' => 'filter_list_id',
    'field_modus' => 'string'
];

$config['product_filter_lists.filter_list_id'] = [
    'label' => 'Filterlisten-Nr.',
    'type' => 'integer',
    'required' => true,
    'order' => true
];

$config['product_filter_lists.base_filter_list_id'] = [
    'label' => 'Basis-Filterlisten-ID',
    'type' => 'integer',
    'required' => false,
    'default_value' => 0,
    'order' => true
];

$config['product_filter_lists.type'] = [
    'label' => 'Typ',
    'type' => 'enum',
    'enums' => [
        ProductFilterList::FILTER_LIST_BLACKLIST => 'Blacklist',
        ProductFilterList::FILTER_LIST_WHITELIST => 'Whitelist'
    ],
    'required' => true,
    'order' => true,
];

$config['product_filter_lists.name'] = [
    'label' => 'Filterliste',
    'type' => 'std_string',
    'required' => true,
    'order' => true,
    'length_max' => 80
];
$config['product_filter_lists.beschreibung'] = [
    'label' => 'Filterliste-ID',
    'type' => 'memo',
    'default_value' => '',
    'required' => false
];

$config['product_filter_lists.price_group_id'] = [
    'label' => 'Preisgruppe',
    'type' => 'integer',
    'required' => true
];

return $config;