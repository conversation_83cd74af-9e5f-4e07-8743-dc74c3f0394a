<?php

use wws\MarketView\MarketViewFactory;

$config = [];


$config['market_view_b2b_offers.availability_id'] = [
    'label' => 'Verfügbarkeit',
    'order' => true,
    'type' => 'callback',
    'callback' => [['factory' => MarketViewFactory::class, 'MarketViewOutputHelper'], 'tableHelper_availability_b2b']
];

$config['market_view_b2b_offers.vk_netto'] = [
    'label' => 'Preis',
    'type' => 'currency',
    'align' => 'right',
    'order' => true,
    'template' => '<nobr>__value__</nobr>'
];

$config['market_view_b2b_offers.vk_netto_best'] = [
    'label' => 'Best Preis',
    'type' => 'currency',
    'align' => 'right',
    'order' => true,
    'template' => '<nobr>__value__</nobr>'
];

$config['MAKRO.market_view_b2b_offers.vk'] = [
    'sql' => ['market_view_b2b_offers.vk_netto', 'market_view_b2b_offers.vk_netto_best'],
    'label' => 'VK',
    'order' => 'market_view_b2b_offers.vk_netto',
    'type' => 'callback',
    'callback' => function ($daten) {
        $return = '';

        $return .= output::formatPrice($daten['vk_netto']);

        if ($daten['vk_netto_best']) {
            if (!in_array($daten['availability_id'], [10, 20, 25])) {
                $return .= '<br /><small>(Best ' . output::formatPrice($daten['vk_netto_best']) . ')</small>';
            }
        }

        return $return;
    }
];

return $config;
