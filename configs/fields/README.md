# Fields-Konfiguration

Siehe auch: `utils/system/fields_check_types.php` (Validierung/Typprüfung).

Diese Konfiguration beschreibt die „Felder“ (Modelleigenschaften) im WWS und wird u. a. dafür genutzt:

- Change-Log/Protokoll-Einstellungen pro Entität zu definieren
- Default-Werte pro Entität festzulegen
- aus Felddefinitionen Formularfelder inkl. Validierung zu erzeugen (Erfassen und Filtern)
- aus Felddefinitionen Tabellenspalten abzuleiten
- auswählbare (und ggf. virtuelle) Tabellenspalten zu definieren (Makros)

## Grundstruktur eines Feldes

Ein Feld ist ein Array mit den folgenden (typischen) Schlüsseln:

- `label`: Bezeichnung des Feldes
- `type`: Datentyp des Feldes
- `order`: `true`/`false` oder ein <PERSON>-<PERSON>nippet (`string`) für die Sortierung in Tabellen (Default: `false`)
- `required`: <PERSON><PERSON><PERSON><PERSON><PERSON> (`true`/`false`)
- `hint`: erwei<PERSON><PERSON> Beschreibung (z. B. für Tooltips)
- `length_min`: minimale Länge (optional)
- `length_max`: maximale Länge (optional)

## Kontextabhängige Overrides (`as_<kontext>`)

Für bestimmte Kontexte kann die Feldkonfiguration überschrieben/angepasst werden:

- `as_<kontext>` enthält ein Array mit Änderungen für diesen Kontext
- Beim Laden werden die Werte aus `as_<kontext>` über die Basiskonfiguration gelegt (überschreiben gleichnamige Schlüssel)
- Standardkontexte sind `table` und `filter`

Beispiel:

```php
[
    'type' => 'date',
    'label' => 'Erstellt am',
    'order' => true,
    'as_table' => [
        'label' => 'Erstellt vor',
        'type' => 'callback',
        'callback' => function (array $row, string $key) {
            return DateTimeUtils::relativeDate($row[$key]);
        },
    ],
]
```

Wird im Kontext `table` zu:

```php
[
    'type' => 'callback',
    'label' => 'Erstellt vor',
    'order' => true,
    'callback' => function (array $row, string $key) {
        return DateTimeUtils::relativeDate($row[$key]);
    },
]
```

## Tabellenspezifische Optionen

- `align`: `center`, `left`, `right`
- `table_type`: expliziter Spaltentyp für Tabellen; wird ansonsten aus `type` abgeleitet
- `help`: zeigt ein Fragezeichen im Tabellenkopf mit Tooltip an (TODO: umbenennen/mit `hint` vereinheitlichen?)
- `rollup_complete`: Rollup für die komplette Tabelle inkl. Aggregationsmethode
- `rollup`: wie `rollup_complete`, aber nur für die aktuelle Seite

## Typen (Auszug)

Die vollständige, maßgebliche Typ- und Strukturprüfung findet in `utils/system/fields_check_types.php` statt.

- `enum` (Auswahlfeld)
  - `enums`: Array mit Optionen oder Callback, der ein Array zurückgibt
  - `multiple`: Mehrfachauswahl möglich
  - `empty_option`: leere Auswahlmöglichkeit hinzufügen (`true`/`false`)
  - `size`: Anzahl der sichtbaren Optionen (Default: `1` bei Single, `5` bei Multiple)
  - `autosize`: `true`/`false`, skaliert die Höhe des Auswahlfeldes (Default: `5`)
- `callback` (nur für Tabellen sinnvoll)
  - `callback`: Callback zur Darstellung (z. B. Formatierung einer Zelle)
