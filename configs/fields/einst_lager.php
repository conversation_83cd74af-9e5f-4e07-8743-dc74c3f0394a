<?php

use wws\Lager\LagerRepository;

$config = [];

$config['einst_lager.lager_id'] = [
    'label' => 'Lager-Id',
    'type' => 'int',
    'order' => true
];

$config['einst_lager.lager_pos'] = [
    'label' => 'Sortierung',
    'type' => 'int',
    'order' => true
];

$config['einst_lager.lager_name'] = [
    'label' => 'Lager',
    'order' => true,
    'type' => 'string'
];

$config['einst_lager.lager_typ'] = [
    'label' => 'type',
    'order' => true,
    'type' => 'enum',
    'enums' => fn() => LagerRepository::getLagerTypeNames(),
];

$config['einst_lager.wareneingang'] = [
    'label' => 'Wareneingang',
    'type' => 'boolean',
    'as_table' => [
        'type' => 'callback',
        'callback' => function($daten, $key) {
            return $daten[$key] ? 'ja' : '';
        }
    ],
];

$config['einst_lager.warenausgang'] = [
    'label' => 'Warenausgang',
    'type' => 'boolean',
    'as_table' => [
        'type' => 'callback',
        'callback' => function($daten, $key) {
            return $daten[$key] ? 'ja' : '';
        }
    ],
];

$config['einst_lager.versand_handler'] = [
    'label' => 'Versand',
    'type' => 'string'
];

return $config;
