<?php

use wws\MarketViewWwsConnector\MarketViewAvailabilityServiceConnector;

$config = [];


$config['supplier_order_items.ek_netto'] = [
    'label' => 'EK (Netto)',
    'type' => 'currency',
    'can_empty' => true,
    'order' => true
];

$config['supplier_order_items.quantity'] = [
    'label' => 'Anzahl',
    'type' => 'integer_positiv',
    'allow_zero' => true,
    'required' => true
];

$config['supplier_order_items.anzahl']  = $config['supplier_order_items.quantity'];


$config['supplier_order_items.gros_product_id'] = [
    'label' => 'Lieferanten Artnr.',
    'type' => 'string',
    'order' => true
];

//hm... dafür brauch ich eigentliche in marko, damit die felder in einem Feld zusammengefasst werden können.
//mvsrc_availability_id u	availability_info


$config['supplier_order_items.delivery_date'] = [
    'label' => 'erwartetes Lieferdatum',
    'type' => 'date'
];

$config['supplier_order_items.anzahl_ist'] = [
    'label' => 'geliefert',
    'order' => true,
    'type' => 'integer',
    'as_table' => [
        'type' => 'callback',
        'callback' => function (array $row) {
            if (isset($row['anzahl']) && $row['anzahl'] == $row['anzahl_ist']) {
                return '<span style="background-color: #0e0; display: inline-block; padding: 4px 10px;">' . $row['anzahl_ist'] . '</span>';
            }

            return '<span style="display: inline-block; padding: 4px 10px;">' . $row['anzahl_ist'] . '</span>';
        }
    ]
];

$config['supplier_order_items.product_name'] = [
    'label' => 'Produkt',
    'order' => true,
    'type' => 'string',
    'template' => '<a href="/ax/artikel/product/product/?back=close&product_id={{$product_id}}" onclick="popup_large(event, {name:\'productwin\'})">{{$product_name}}</a>',
];

//es gibt hier im naaming kollisionen wenn product_ek und grossist_bestellung_items gleichzeitg gebraucht werden -> daher etwas umständlichere namen
$config['MAKRO.supplier_order_items.availability_item'] = [
    'label' => 'Verfügbarkeit<br><smal>zur Bestellung</smal>',
    'order' => 'supplier_order_items.mvsrc_availability_id',
    'type' => 'callback',
    'sql' => ['supplier_order_items.mvsrc_availability_id AS supplier_order_item_mvsrc_availability_id', 'supplier_order_items.availability_info AS supplier_order_item_availability_info'],
    'align' => 'center',
    'callback' => function (array $row) {
        static $market_view_availability_service = null;
        if ($market_view_availability_service === null) {
            $market_view_availability_service = service_loader::get(MarketViewAvailabilityServiceConnector::class);
        }

        if ($row['supplier_order_item_mvsrc_availability_id']) {
            return $market_view_availability_service->getAvailabilityAsHtml($row['supplier_order_item_mvsrc_availability_id'], $row['supplier_order_item_availability_info']);
        }

        return '-';
    }
];

return $config;
