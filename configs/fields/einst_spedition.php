<?php

use wws\Shipment\ShipmentRepository;

$config = [];

$config['einst_spedition.sped_name'] = [
    'label' => 'Spedition',
    'type' => 'std_string',
    'required' => true,
    'length_max' => 30,
    'order' => true
];

$config['einst_spedition.sped_name_public'] = [
    'label' => 'Spedition (Public)',
    'type' => 'std_string',
    'required' => true,
    'length_max' => 30,
    'order' => true
];

$config['einst_spedition.sped_type'] = [
    'label' => 'Typ',
    'type' => 'enum',
    'enums' => [
        'paket' => 'Paketdienst',
        'sped' => 'Spedition',
        'direkt' => 'Direkt',
        'abh' => 'Abholung'
    ],
    'required' => true,
    'default_value' => 'paket',
    'order' => true
];


$config['einst_spedition.sped_prio'] = [
    'label' => 'Priorität',
    'type' => 'integer',
    'default_value' => 0,
    'order' => true
];


$config['einst_spedition.kunr'] = [
    'label' => 'Kundennr.',
    'type' => 'std_string',
    'length_max' => 20
];


$config['einst_spedition.firma'] = [
    'label' => 'Firma',
    'type' => 'std_string',
    'required' => true,
    'length_max' => 30
];

$config['einst_spedition.strasse'] = [
    'label' => 'Strasse',
    'type' => 'std_string',
    'length_max' => 40
];

$config['einst_spedition.plz'] = [
    'label' => 'PLZ',
    'type' => 'std_string',
    'length_max' => 8
];

$config['einst_spedition.stadt'] = [
    'label' => 'Stadt',
    'type' => 'std_string',
    'length_max' => 20
];

$config['einst_spedition.versandmail_mail_id'] = [
    'label' => 'Versandmail ID',
    'type' => 'std_string',
    'length_max' => 50
];

$config['einst_spedition.address_fitter'] = [
    'label' => 'Address-Fitter (class)',
    'type' => 'std_string',
    'length_max' => 200
];

$config['einst_spedition.ability_shipment_group_ids'] = [
    'label' => 'Versandgruppe',
    'type' => 'enum',
    'enums' => function() {
        return ShipmentRepository::getShipmentGroupNames();
    },
    'multiple' => true
];

$config['einst_spedition.ability_packstation'] = [
    'label' => 'DHL-Packstation/Filale',
    'type' => 'bool'
];

$config['einst_spedition.ability_zahlungs_ids_blacklist'] = [
    'label' => 'nicht unterstützte Zahlungsarten',
    'type' => 'zahlungsart',
    'multiple' => true
];

$config['einst_spedition.ability_extra_services'] = [
    'label' => 'unterstützte Serviceleistungen',
    'type' => 'enum',
    'enums' => function() {
        return db::getInstance()->query("
            SELECT
                product_extra_services.service_id,
                product_extra_services.service_name_intern
            FROM
                product_extra_services
            ORDER BY
                product_extra_services.service_name_intern
        ")->asSingleArray('service_id');
    },
    'multiple' => true
];

$config['einst_spedition.bemerkung'] = [
    'label' => 'Bemerkung',
    'type' => 'memo',
];

return $config;