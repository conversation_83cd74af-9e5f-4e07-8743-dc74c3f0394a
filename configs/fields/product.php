<?php

use bqp\form\form_element_category_picker;
use bqp\form\FormElementBrand;
use bqp\form\FormElementProductTags;
use bqp\Utils\StringUtils;
use wws\Product\ProductConst;
use wws\Product\ProductRepositoryLegacy;
use wws\Product\ProductWarenkorbTypesRepository;
use wws\Shipment\ShipmentRepository;

$config = [];


$config['protokoll'] = [
    'table' => 'protokoll_product',
    'entity_id_field' => 'product_id',
    'field_modus' => 'id'
];

$config['table_object_field_select'] = [
    'group_label' => 'Produkt',
];

$config['product.product_id'] = [
    'label' => 'Produkt ID',
    'order' => true,
    'type' => 'callback',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_id']
];

$config['product.product_name'] = [
    'label' => 'Produktname',
    'type' => 'text',
    'required' => true,
    'length_max' => 80,
    'as_table' => [
        'label' => 'Produkt',
        'order' => true,
        'template' => '<a href="/ax/artikel/product/product/?back=close&product_id={{$product_id}}" onclick="popup_large(event, {name:\'productwin\'})">__VALUE__</a>',
    ],
    'as_filter' => [
        'required' => false
    ]
];

$config['product.product_type'] = [
    'label' => 'Warengruppe',
    'type' => 'product_type',
    'required' => true,
    'as_filter' => [
        'required' => false
    ],
    'as_filter_multi' => [
        'multiple' => true,
        'required' => false
    ],
    'as_table' => [
        'label' => 'Typ',
        'type' => 'callback',
        'align' => 'center',
        'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_type']
    ]
];


$config['product.gewicht'] = [
    'label' => 'Gewicht',
    'type' => 'float',
];

$config['product.size_h'] = [
    'label' => 'H',
    'type' => 'float',
];

$config['product.size_b'] = [
    'label' => 'B',
    'type' => 'float',
];

$config['product.size_t'] = [
    'label' => 'T',
    'type' => 'float',
];

$config['product.versand_id'] = [
    'label' => 'geschätzte Versandkosten',
    'type' => 'enum',
    'enums' => function () {
        return ShipmentRepository::getRealShipmentTypes();
    },
    'order' => true,
    'as_table' => [
        //@todo kann das überhaupt funktionieren? -> Jain, mit dem ProductListHeaderProfile funktioniert das, wenn
        //die Spalte in den query gepatcht wird, mit der normalen WwsDatasoruceSql kann das nicht funktionieren
        'required_sql' => 'product.versand_id AS versand_id_estimated', //gibt ansonsten u.U. Kollision mit product_shop.versand_id
        'alias' => 'versand_id_estimated'
    ],
    'as_filter' => [
        'required' => false,
        'size' => 5,
        'multiple' => true,
    ],
];


$config['product.brand_id'] = [
    'label' => 'Marke',
    'type' => FormElementBrand::class,
    'required' => true,
    'as_filter' => [
        'required' => false
    ]
];

$config['product.cat_id'] = [
    'label' => 'Kategorie',
    'type' => form_element_category_picker::class,
    'required' => true,
    'as_filter' => [
        'required' => false
    ]
];

$config['product.product_tags'] = [
    'label' => 'Tags',
    'type' => FormElementProductTags::class,
    'multiple' => true
];


$config['product.genuine_part'] = [
    'label' => 'Originalteil',
    'type' => 'text',
    'order' => true,
    'as_table' => [
        'label' => '<span title="Originalteil/Alternativteil">OT/AT</span>',
        'type' => 'callback',
        'align' => 'center',
        'callback' => function ($daten, $key) {
            $value = $daten[$key];
            if ($value === null) {
                return '';
            }
            return $value ? '<b style="color:green;">OT</b>' : '<b style="color:red;">AT</b>';
        }
    ]
];


$config['product.check24_fee_id'] = [
    'label' => 'Check 24 Gebühren',
    'type' => 'int',
    'order' => true
];

$config['product.mpn'] = [
    'label' => 'Hersteller-Art.-Nr.',
    'type' => 'text',
    'order' => true
];

$config['product.ean'] = [
    'label' => 'EAN',
    'type' => 'text',
    'order' => true
];

$config['product.model_name'] = [
    'label' => 'Modellname',
    'type' => 'text',
    'required' => false,
    'length_max' => 80,
    'hint' => 'Der Produktname des Herstellers ohne Marke, ohne Kategorisierung, ohne Marketing.',
];

$config['product.product_nr'] = [
    'label' => 'Artikelnummer',
    'order' => true,
    'type' => 'string',
];

$config['product.product_status'] = [
    'type' => 'string',
    'label' => 'Status',
    'as_filter' => [
        'type' => 'enum',
        'autosize' => true,
        'multiple' => true,
        'enums' => ProductRepositoryLegacy::getProductStatuse()
    ]
];

$config['product.date_create'] = [
    'type' => 'date',
    'label' => 'Datum angelegt',
    'as_filter' => [
        'type' => 'date_range_picker',
        'required' => false
    ],
];

$config['product.product_warenkorb_typ'] = [
    'type' => 'enum',
    'label' => 'Warenkorb-Typ',
    'enums' => fn () => ProductWarenkorbTypesRepository::getWarenkorbTypeNames(),
    'required' => true,
    'as_filter' => [
        'required' => false,
        'size' => 3,
        'multiple' => true,
    ],
];

$config['product.features_status'] = [
    'type' => 'enum',
    'label' => 'Feature-Status',
    'enums' => [
        'pflicht' => 'pflicht',
        'unvollstaendig' => 'unvollstaendig',
        'vollstaendig' => 'vollstaendig',
    ]
];

$config['product.lager_qualifikation'] = [
    'type' => 'enum',
    'label' => 'Lager-Qualifikation',
    'multiple' => true,
    'size' => 9,
    'enums' => fn() => ProductRepositoryLegacy::getLagerQualifikationsSimple(),
    'order' => true,
    'as_table' => [
        'label' => 'Lager-Quali',
        'type' =>  'callback',
        'callback' => function (array $row, string $key) {
            $quali = ProductRepositoryLegacy::getLagerQualifikationInfo($row[$key]);

            $result =  '<div style="padding: 3px; text-align: center; font-size: 14px; font-weight: bold; background-color: ' . $quali['color'] . '" title="' . $quali['beschreibung'] . '">';
            $result .= $quali['code'];
            $result .= '</div->';

            return $result;
        }
    ]
];

$config['product.paketfaehig'] = [
    'type' => 'enum',
    'enums' => ProductRepositoryLegacy::$paketfaehig,
    'label' => 'Paketfähig'
];

$config['MAKRO.product.grundpreis'] = [
    'label' => 'Grundpreis',
    'sql' => ['product.grundpreis_aktiv', 'product.grundpreis_einheit', 'product.grundpreis_faktor'],
    'type' => 'callback',
    'callback' => function (array $row) {
        if ($row['grundpreis_aktiv'] != 1) {
            return '-';
        }
        return $row['grundpreis_faktor'] . ' / ' . $row['grundpreis_einheit'];
    }
];

$config['MAKRO.product.status_symbols'] = [
    'label' => '',
    'type' => 'callback',
    'sql' => ['product_shop.park', 'product.product_status'],
    'callback' => function($row) {
        $result = [
            'value' => ''
        ];

        if (array_key_exists('product_filter_list_result_status', $row)) {
            if (!$row['product_filter_list_result_status']) {
                $result['style'] = 'background-color: #FF8080 ! important;';
            }
        }

        if($row['product_status'] == ProductConst::PRODUCT_STATUS_DEL) {
            $result['value'] = '<img src="/res/images/icons/bin_closed.png" alt="Produkt gelöscht" />';
        }

        if($row['park'] == 1) {
            $result['value'] = '<img src="/res/images/parken.gif" alt="Produkt geparkt" />';
        }

        return $result;
    }
];


$config['MAKRO.product.status_symbols2'] = [
    'label' => 'Status',
    'type' => 'callback',
    'sql' => ['product.product_tags', 'product_shop.katwerbung', 'product_shop.hauptwerbung', 'product_shop.sonderwerbung', 'product.product_warenkorb_typ', 'product.cat_id'],
    'callback' => function(array $row) {
        $return = '';
        if($row['katwerbung'] == 1) $return .= ' <img src="/res/images/status_uwerbung.gif" border="0" alt="Kategoriewerbung" />';
        if($row['hauptwerbung'] == 1) $return .= ' <img src="/res/images/status_hwerbung.gif" border="0" alt="Hauptwerbung" />';
        if($row['sonderwerbung'] == 1) $return .= ' <img src="/res/images/status_sonderwerbung.gif" border="0" alt="Sonderwerbung" />';
        if($row['product_warenkorb_typ'] === 'set') $return .= ' SET';

        $return .= ProductRepositoryLegacy::getProductTagsAsHtml($row);

        /*if(product_repository::isFlag('wackler', $daten)) {
            $return .= 'wackler';
        }*/

         if (isset($row['mapping_cat_id'])) {
            if ($row['mapping_cat_id'] != $row['cat_id']) {
                $return .= ' <span title="Sekundär-Kategorie!">SEC</span>';
            }
        }

        return $return;
    }
];



$config['MAKRO.product.features_status_symbols'] = [
    'label' => '',
    'type' => 'callback',
    'sql' => ['product.features_status'],
    'callback' => function($daten) {
        switch($daten['features_status']) {
            case \wws\Product\actions\ProductFeatureStatus::FEATURES_MANDATORY:
                return '<img src="/res/images/icons/script_red.png" alt="Es sind nicht alle Pflichtfelder ausgefüllt!" />';
            case \wws\Product\actions\ProductFeatureStatus::FEATURES_INCOMPLETE:
                return '<img src="/res/images/icons/script_orange.png" alt="Es sind nicht alle Felder ausgefüllt." />';
            case \wws\Product\actions\ProductFeatureStatus::FEATURES_COMPLETE:
                return '<img src="/res/images/icons/script_green.png" alt="Alle Daten vorhanden." />';
        }
    }
];

$config['MAKRO.product.product_tags'] = [
    'label' => 'Tags',
    'type' => 'callback',
    'sql' => ['product.product_tags'],
    'order' => false,
    'callback' => [ProductRepositoryLegacy::class, 'getProductTagsAsHtml']
];



$config['MAKRO.product.product_name_inline'] = [
    'label' => 'Produkt',
    'sql' => ['product.product_name'],
    'order' => 'product.product_name',
    'link' => '<a href="/ax/artikel/product/product/?product_id={{$product_id}}&filter_id={{$this.filter_id}}" target="inline">__VALUE__</a>',
    'template' => '{{$product_name}}',
    'type' => 'string',
];

$config['product.anzulegen_prioritaet'] = [
    'label' => 'Priorität',
    'order' => true,
    'type' => 'callback',
    'callback' => function($daten) { return $daten['anzulegen_prioritaet'] < 10000 ? 'normal' : 'hoch'; }
];


$config['product.vip'] = [
    'label' => 'VIP',
    'type' => 'callback',
    'align' => 'center',
    'order' => 'product.vip',
    'callback' => function(array $row, string $key) {
        $img = $row[$key] == 1 ? 'chkbox_on' : 'chkbox_off';
        return '<a href="javascript:alert(\'nicht implementiert\');"><img src="/res/images/' . $img . '.gif" border="0"></a>';
    }
];
$config['MAKRO.product.never_tagged_no_stock_longterm'] = [
    'label' => 'Nie getaggt no_stock_longterm',
    'type' => 'callback',
    'align' => 'center',
    'order' => 'never_tagded_no_stock_longterm',
    'sql' => [
        'product.product_tags',
        "FIND_IN_SET('" . ProductConst::TAG_NEVER_TAGGED_NO_STOCK_LONGTERM . "', product.product_tags)!=0 AS never_tagded_no_stock_longterm",
    ],
    'callback' => function(array $row) {
        $img = $row['never_tagded_no_stock_longterm'] ? 'chkbox_on' : 'chkbox_off';

        return '<img src="/res/images/' . $img . '.gif" border="0">';
    }
];

$config['MAKRO.product.preview_picture'] = [
    'label' => 'Bild',
    'type' => 'string',
    'sql' => ['product.product_id'],
    'template' => '<img src="/getimage.php?product_id={{$product_id}}&size=wws_thumb" alt="" width="100" height="100">'
];

//@todo falsche tabelle
$config['MAKRO.product.bestellt'] = [
    'label' => 'Bestellt<br><small>ges./bez.</samll>',
    'align' => 'center',
    'type' => 'string',
    'sql' => ['product_lager_detail_1.orders', 'product_lager_detail_1.orders_unverbindlich'],
    'order' => 'product_lager_detail_1.orders',
    'template' => '<a href="/ax/artikel/product_order_history/?product_id={{$product_id}}" onclick="popup_large(event)" target="zpop" class="link_no_underline">{{$orders}}/{{$orders_unverbindlich}}'
];

$config['MAKRO.product.multi_select'] = [
    'label' => '<input type="checkbox" onclick="wws.checkboxSelectAll(this.checked,\'product_ids[]\')" />',
    'type' => 'string',
    'template' => '<input type="checkbox" name="product_ids[]" value="{{$product_id}}" class="multi_select" />',
    'align' => 'center',
    'sql' => null,
    'header_style' => 'text-align: center ! important;'

    //@todo eigentlich müsste das auf table_object_field_checkbox gestellt werden -> hier gibt es ansonsten ein init problem des multi_selects
];


return $config;
