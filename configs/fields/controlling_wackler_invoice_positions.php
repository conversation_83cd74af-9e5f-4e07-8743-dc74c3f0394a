<?php

$config = [];

$config['controlling_wackler_invoice_positions.auftnr'] = [
    'label' => 'Auftnr.',
    'type' => 'string',
    'order' => true,
    'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=__value__" onclick="popup_large(event)" class="inline">__value__</a>'
];

$config['controlling_wackler_invoice_positions.datum'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['controlling_wackler_invoice_positions.invoice_nr'] = [
    'label' => 'Rechnungsnr.',
    'type' => 'string',
    'order' => true
];

$config['controlling_wackler_invoice_positions.invoice_pos_nr'] = [
    'label' => 'Sendung',
    'type' => 'string',
    'order' => true,
    'template' => '<a href="/ax/buchhaltung/wackler_controlling/position/?invoice_nr={{$invoice_nr}}&invoice_pos_nr={{$invoice_pos_nr}}" onclick="popup_standard(event)">{{$invoice_pos_nr}}</a>'
];

$config['controlling_wackler_invoice_positions.amount_netto'] = [
    'label' => 'Betrag (Netto)',
    'type' => 'currency',
    'order' => true
];

$config['MAKRO.controlling_wackler_invoice_positions.versand_dif'] = [
    'label' => 'Ertrag',
    'order' => 'versand_dif',
    'type' => 'currency_high',
    //achtung netto gegen brutto betrag
    'sql' => ['IF(orders.versandkosten IS NULL, 0, orders.versandkosten)-controlling_wackler_invoice_positions.amount_netto AS versand_dif']
];

$config['controlling_wackler_invoice_positions.sender'] = [
    'label' => 'Empfänger/Absender',
    'type' => 'string'
];

return $config;
