<?php

$config = [];


$config['customer_mail_archive.date_mail'] = [
    'label' => 'Datum',
    'type' => 'datetime',
    'order' => true
];


$config['customer_mail_archive.email_sender'] = [
    'label' => 'Absender',
    'type' => 'string',
    'order' => true
];

$config['customer_mail_archive.email_reciver'] = [
    'label' => 'Empfänger',
    'type' => 'string',
    'order' => true
];

$config['customer_mail_archive.subject'] = [
    'label' => 'Betreff',
    'type' => 'string',
    'order' => true
];

$config['customer_mail_archive.user_id'] = [
    'label' => 'User',
    'type' => 'integer',
    'as_table' => [
        'type' => 'callback',
        'callback' => [wws\Users\UserRepository::class, 'tableHelperUsername'],
    ],
];

$config['MAKRO.customer_mail_archive.has_attachments'] = [
    'label' => 'Anlagen',
    'sql' => ['IF(customer_mail_archive.attachments != "", 1, 0) AS has_attachments'],
    'order' => 'customer_mail_archive.attachments',
    'type' => 'callback',
    'callback' => function($daten) {
        return $daten['has_attachments'] ? 'Ja' : '';
    }
];

return $config;
