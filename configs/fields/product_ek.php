<?php

use wws\Product\ProductRepositoryLegacy;

$config = [];

$config['product_ek.ek_snp'] = [
    'label' => 'SNP (Netto)',
    'type' => 'currency',
];

$config['product_ek.ek_rnp'] = [
    'label' => 'RNP (Netto)',
    'type' => 'currency',
];

$config['product_ek.mvsrc_availability_id'] = [
    'label' => 'Verfügbarkeit',
    'type' => 'text',
];

$config['product_ek.gros_product_id'] = [
    'label' => 'ext. Produkt-Nr.',
    'type' => 'string',
    'order' => true
];

$config['product_ek.supplier_id'] = [
    'label' => 'Grossist',
    'type' => 'supplier',
    'required' => true,
    'empty_option' => true,
    'order' => true,
    'dynamic_suffix_template' => '\'<a href="/av/gs/index.php?page=gs&modul=bearbeitung_lager&supplier_id=\'+value+\'&show=&lager_id=1" target="_blank">Anzeigen</a>\'',
    'as_filter' => [
        'required' => false,
        'empty_option' => false
    ],
    'as_multi_filter' => [
        'multiple' => true,
        'required' => false,
        'empty_option' => false
    ]
];

$config['product_ek.ek_fulfill_versand_source'] = [
    'type' => 'enum',
    'label' => 'Fulfillment-Versandkosten',
    'enums' => fn() => ProductRepositoryLegacy::getEkFufillVersandSourceNames()
];


$config['product_ek.ek_netto'] = [
    'label' => 'netto EK',
    'type' => 'currency',
    'order' => true
];

$config['MAKRO.product_ek.ek_brutto'] = [
    'label' => 'EK Brutto',
    'sql' => ['product_ek.ek_netto * (product_shop.mwst_satz/100+1) AS ek_brutto'],
    'type' => 'currency',
    'order' => 'product_ek.ek_netto',
    'align' => 'center',
];

//@todo über zwei Tabellen!
$config['MAKRO.product_ek.supplier_name'] = [
    'label' => 'Grossist',
    'type' => 'string',
    'sql' => ['product_ek.supplier_id', 'supplier.supplier_name'],
    'alias' => 'supplier_name',
    'order' => 'supplier.supplier_name'
];

$config['MAKRO.product_ek.availability'] = [
    'label' => '<span title="Verfügbarkeit">V</span>',
    'align' => 'center',
    'sql' => ['product_ek.mvsrc_availability_id', 'product_ek.availability_info', 'product_ek.mvsrc_availability_id_virt', 'product_ek.mvsrc_availability_id_last_change'],
    'order' => 'product_ek.mvsrc_availability_id',
    'type' => 'callback',
    'callback' => function(array $row) {
        static $market_view_availability_service = null;

        if ($market_view_availability_service === null) {
            $market_view_availability_service = service_loader::getDiContainer()->get(\wws\MarketViewWwsConnector\MarketViewAvailabilityServiceConnector::class);
        }

        $availability_info = $row['availability_info'];

        $date = new \bqp\Date\DateObj($row['mvsrc_availability_id_last_change']);
        if ($date->isValid()) {
            if ($availability_info !== '') {
                $availability_info .= ' ';
            }
            $availability_info .= 'letzte Änderung: ';
            $availability_info .= $date->format($date::DE_DATETIME);
        }

        $bestand = $market_view_availability_service->getAvailabilityAsHtml($row['mvsrc_availability_id'], $availability_info);

        if ($row['mvsrc_availability_id_virt']) {
            $bestand .= ' ';
            $bestand .= $market_view_availability_service->getAvailabilityAsHtml($row['mvsrc_availability_id_virt'], 'virtuell');
        }

        return $bestand;
    }
];

return $config;
