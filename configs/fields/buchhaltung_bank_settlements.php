<?php

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'Abrechnungen',
];

$config['buchhaltung_bank_settlements.settlement_id'] = [
    'label' => 'Abrechnungs-Id',
    'type' => 'int',
    'order' => true,
    'template' => '<a href="/ax/buchhaltung/konto_settlement/settlement/?settlement_id=__VALUE__{{$this.wfid_url}}">__VALUE__</a>'
];

$config['buchhaltung_bank_settlements.account_id'] = [
    'label' => 'Konto',
    'type' => 'int',
    'as_filter' => [
        'type' => 'enum',
        'multiple' => true,
        'autosize' => true,
        'enums' => function () {
            return \wws\BankAccount\bank_repository::getAccountNames();
        }
    ]
];

$config['buchhaltung_bank_settlements.settlement_date_added'] = [
    'label' => 'Datum e<PERSON>',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true,
    'as_filter' => [
        'type' => 'date_range_picker'
        /*'multiple' => true,
        'autosize' => true,
        'enums' => function() {
            return \wws\buchhaltung\bank_repository::getAccountNames();
        }*/
    ]
];

$config['buchhaltung_bank_settlements.settlement_reference'] = [
    'label' => 'externe Referenz',
    'type' => 'std_string',
];
$config['buchhaltung_bank_settlements.settlement_name'] = [
    'label' => 'Abrechnungsname',
    'type' => 'std_string',
    'template' => '<a href="/ax/buchhaltung/konto_settlement/settlement/?settlement_id={{$settlement_id}}{{$this.wfid_url}}" target="inline">__VALUE__</a>'
];
$config['buchhaltung_bank_settlements.settlement_date'] = [
    'label' => 'Abrechnungsdatum',
    'type' => 'date',
    'order' => true
];

$config['buchhaltung_bank_settlements.settlement_attachments'] = [
    'label' => 'Anhänge',
    'type' => 'std_string',
    'table_type' => 'FileAttachments',
];

$config['buchhaltung_bank_settlements.amount'] = [
    'label' => 'Betrag',
    'type' => 'currency',
    'order' => true,
    'as_table' => [
        'type' => 'currency'
    ]
];
$config['buchhaltung_bank_settlements.quantity'] = [
    'label' => 'Buchungen',
    'type' => 'int',
];

$config['buchhaltung_bank_settlements.quantity_processed'] = [
    'label' => 'bearbeitet',
    'type' => 'int',
    'order' => true
];

$config['buchhaltung_bank_settlements.quantity_booked'] = [
    'label' => 'gebucht',
    'type' => 'int',
    'order' => true
];

$config['buchhaltung_bank_settlements.settlement_exported'] = [
    'label' => 'exportiert',
    'type' => 'enum',
    'order' => true,
    'enums' => [
        0 => 'nein',
        1 => 'ja'
    ]
];

$config['MAKRO.buchhaltung_bank_settlements.quantity_to_process'] = [
    'label' => 'zu buchen',
    'sql' => ['(buchhaltung_bank_settlements.quantity - buchhaltung_bank_settlements.quantity_processed) AS quantity_to_process'],
    'order' => 'quantity_to_process',
    'type' => 'callback',
    'callback' => function($row) {
        $value = (int)$row['quantity_to_process'];

        if ($value === 0) {
            return '<span style="color: #ccc">0</span>';
        }

        return '<b style="color: red">' . $value . '</b>';
    }
];

return $config;
