<?php

use wws\Payment\PaymentRepository;

$config = [];

$config['protokoll_zahlung.bardatum'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i',
    'order' => true
];

$config['protokoll_zahlung.zahlungsart'] = [
    'label' => 'Zahlungsart',
    'type' => 'integer',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => [PaymentRepository::class, 'tableHelper_zahlungsart'],
    ],
];

$config['protokoll_zahlung.barbetrag'] = [
    'label' => 'Betrag',
    'order' => true,
    'type' => 'currency'
];

return $config;
