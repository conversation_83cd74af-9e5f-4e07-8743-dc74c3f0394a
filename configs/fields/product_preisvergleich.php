<?php

$config = [];

$config['product_preisvergleich.url'] = [
    'label' => 'URL',
    'type' => 'string',
    'order' => true
];

$config['product_preisvergleich.datum_akt'] = [
    'label' => 'letzte Aktualisierung',
    'type' => 'datetime',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => function(array $row) {
            static $now = null;

            if ($row['datum_akt'] === null) {
                return '';
            }

            if ($now === null) {
                 $now = new \bqp\Date\DateObj();
            }

            $date = new \bqp\Date\DateObj($row['datum_akt']);

            $age = $date->diffSimple($now, 'hour');

            $img = '<img src="/res/images/status_0.png" alt="innerhalb der letzten 24 Stunden" />';
            if ($age > 48) {
                $img = '<img src="/res/images/status_2.png" alt="vor mehr als 48 Stunden" />';
            } elseif ($age > 24) {
                $img = '<img src="/res/images/status_1.png" alt="innerhalb der letzten 48 Stunden" />';
            }

            return $img . ' ' . $date->format('d.m.Y H:i');
        }
    ]
];

$config['product_preisvergleich.preis'] = [
    'label' => 'akt. PSM Preis',
    'type' => 'currency',
    'order' => true
];


$config['MAKRO.product_preisvergleich.url_indication'] = [
    'label' => 'PSM URL',
    'align' => 'center',
    'sql' => ['product_preisvergleich.url AS idealo_url'],
    'type' => 'callback',
    'callback' => function (array $row) {
        if (isset($row['preisautomatik_aktiv']) && $row['preisautomatik_aktiv'] && !$row['idealo_url']) {
            return '<img src="/res/images/preisvergleich_missing_url.gif" alt="" title="Preisautomatik aktiv ohne URL">';
        }
        if ($row['idealo_url']) {
            return '<img src="/res/images/preisvergleich_on.gif" alt="">';
        }
        return '<img src="/res/images/preisvergleich_off.gif" alt="" title="keine URL gepflegt">';
    },
    'order' => 'product_preisvergleich.url'
];

return $config;
