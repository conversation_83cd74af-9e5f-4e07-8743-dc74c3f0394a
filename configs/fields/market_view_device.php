<?php

$config = [];

$config['market_view_device.mvsrc_id'] = [
    'label' => 'Mvsrc-ID',
    'type' => 'integer',
    'as_filter' => [
        'label' => 'Quelle',
        'type' => 'enum',
        'enums' => function() {
            $factory = \wws\MarketView\MarketViewFactory::getInstance();
            return $factory->getMarketViewRepository()->getMvsrcNamesWithDevicesEnabled();
        }
    ]
];

$config['market_view_device.mvsrc_device_id'] = [
    'label' => 'ext. Geräte-ID',
    'type' => 'string',
    'as_table' => [
        'template' => '<a href="/ax/market_view/device/?mvsrc_id={{$mvsrc_id}}&mvsrc_device_id={{$mvsrc_device_id}}" onclick="popup_large(event)" class="inline">__VALUE__</a>'
    ]
];

$config['market_view_device.model_name'] = [
    'label' => 'Modellname',
    'type' => 'string',
    'as_table' => [
        'template' => '<a href="/ax/market_view/device/?mvsrc_id={{$mvsrc_id}}&mvsrc_device_id={{$mvsrc_device_id}}" onclick="popup_large(event)" class="inline">__VALUE__</a>'
    ]
];

$config['market_view_device.code1'] = [
    'label' => 'Code 1',
    'type' => 'string'
];

$config['market_view_device.code2'] = [
    'label' => 'Code 2',
    'type' => 'string'
];

$config['market_view_device.code3'] = [
    'label' => 'Code 3',
    'type' => 'string'
];

$config['market_view_device.code4'] = [
    'label' => 'Code 4',
    'type' => 'string'
];

$config['market_view_device.code5'] = [
    'label' => 'Code 5',
    'type' => 'string'
];


$config['MAKRO.market_view_device.device_id'] = [
    'sql' => ['market_view_device.device_id'],
    'label' => 'angelegt',
    'order' => 'market_view_device.device_id',
    'type' => 'callback',
    'callback' => function (array $row) {
        if ($row['device_id'] > 0) {
            return '<a href="/ax/artikel/product_device/?device_id=' . $row['device_id'] . '" onclick="popup_large(event)"><i class="fa fa-check"></i></a>';

        }
        if ($row['device_id'] < 0) {
            return '<i class="fa fa-ban" title="Gerät ignoriert"></i>';
        }
        return '';
    }
];

return $config;
