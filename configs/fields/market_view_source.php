<?php

use wws\MarketView\MarketViewFactory;

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'MarketView - Quelle',
];

$config['market_view_source.mvsrc_name'] = [
    'label' => 'Quelle',
    'type' => 'text',
    'required' => true,
    'order' => true,
];

$config['market_view_source.typ'] = [
    'label' => 'Typ',
    'type' => 'select',
    'as_table' => [
        'type' => 'callback',
        'callback' => [['factory' => MarketViewFactory::class, 'MarketViewOutputHelper'], 'tableHelper_mvsrc_typ']
    ],
];

$config['market_view_source.status'] = [
    'label' => 'Aktiv',
    'type' => 'checkbox',
];

$config['market_view_source.supplier_id'] = [
    'label' => 'Supplier-ID',
    'type' => 'select',
];

$config['market_view_source.price_typ'] = [
    'label' => 'Price-Typ',
    'type' => 'select',
];

$config['market_view_source.inventory_typ'] = [
    'label' => 'Inventory-Typ',
    'type' => 'select',
];

$config['market_view_source.source_product_validity'] = [
    'label' => 'Gültigkeit Produkte',
    'hint' => 'in Stunden',
    'type' => 'integer',
];

$config['market_view_source.source_availability_validity'] = [
    'label' => 'Gültigkeit Verfügbarkeiten',
    'hint' => 'in Stunden',
    'type' => 'integer',
];

$config['market_view_source.source_price_validity'] = [
    'label' => 'Gültigkeit Preise',
    'hint' => 'in Stunden',
    'type' => 'integer',
];

$config['market_view_source.product_creator'] = [
    'label' => 'Product-Creator',
    'type' => 'std_string',
];

$config['market_view_source.source_std_priority'] = [
    'label' => 'Priorität Produktdaten',
    'type' => 'integer',
];

$config['market_view_source.cache_media'] = [
    'label' => 'Cache-Media',
    'type' => 'checkbox',
];

$config['market_view_source.ean_edit'] = [
    'label' => 'EANs bearbeitbar',
    'type' => 'checkbox',
];

$config['market_view_source.image_data_source_id'] = [
    'label' => 'Image-Data-Source-ID',
    'type' => 'select',
];

$config['market_view_source.text_data_source_id'] = [
    'label' => 'Text-Data-Source-ID',
    'type' => 'select',
];

$config['market_view_source.remove_matching_on_ean_change'] = [
    'label' => 'Matching löschen bei EAN Änderung',
    'type' => 'checkbox',
];

$config['market_view_source.matching_mpn'] = [
    'label' => 'Matching über Herstellerartikelnummer',
    'type' => 'checkbox',
];

$config['market_view_source.matching_extra_class'] = [
    'label' => 'Matching-Extra-Class',
    'type' => 'text',
];

$config['market_view_source.use_devices'] = [
    'label' => 'benutzt Geräte',
    'type' => 'checkbox',
];

$config['market_view_source.metric_product_availabilities_enabled'] = [
    'label' => 'Metric-Product-Availabilties-Enabled',
    'type' => 'checkbox',
];

$config['market_view_source.autocreate_type'] = [
    'label' => 'Autocreate-Type',
    'type' => 'text',
];


return $config;
