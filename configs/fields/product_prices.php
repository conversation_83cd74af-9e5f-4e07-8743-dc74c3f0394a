<?php

use wws\Product\ProductConst;

$config = [];

$config['product_prices.vk_typ'] = [
    'label' => 'Preis-Typ',
    'type' => 'enum',
    'enums' => [
        ProductConst::VK_TYPE_FORMEL => 'Formel',
        ProductConst::VK_TYPE_BRUTTO => 'Brutto',
        ProductConst::VK_TYPE_NETTO => 'Netto',
        ProductConst::VK_TYPE_SPANNE => 'Spanne'
    ]
];

$config['product_prices.preisautomatik_platz'] = [
    'label' => 'Ziel-Platz',
    'align' => 'center',
    'type' => 'int',
    'order' => true
];

$config['product_prices.preisautomatik_formel'] = [
    'label' => 'Mindestpreis (Formel)',
    'align' => 'right',
    'type' => 'string',
    'order' => true
];

$config['product_prices.preisautomatik_shipping_cost_handling'] = [
    'label' => 'Versandkosten-Differenz',
    'hint' => '
        Legt fest wie mit Differenzen zwischen unseren Versandkosten und den Kunden-Versandkosten umgegangen wird.<br>
        <ul>
            <li>"automatisch": bei einfachen Formeln z.B. "5%", "5€", "5%+5€" wird die Differenz einberechnet (+VERSDIFF).<br>
                Bei "komplexeren" Formeln wird die Differenz NICHT automatisch einberechnet.
            </li>
            <li>"einberechnen" schlägt die Differenz zwischen unseren Versandkosten und den Kunden-Versandkosten auf.</li>
            <li>"nur Unterdeckung einberechnen" schlägt die Differenz nur auf, wenn diese größer 0€ ist.</li>
            <li>"nicht einberechnen"</li>
        </ul>
        Achtung: Das ist eine Komfortfunktion, die bei der Ausführung die min. Preis (Formel) blind erweitert.
    ',
    'type' => 'enum',
    'enums' => fn () => \wws\Product\ProductAutomatik::getPreisautomatikCostHandling(),
    'required' => true,
];

$config['product_prices.preisautomatik_versand'] = [
    'label' => 'Versandkosten berücksichtigen',
    'type' => 'bool',
];


$config['MAKRO.product_prices.preisautomatik'] = [
    'label' => 'PSM',
    'sql' => ['product_prices.preisautomatik_aktiv', 'product_prices.preisautomatik_platz', 'product_prices.preisautomatik_formel', 'product_prices.preisautomatik_platz_behind'],
    'align' => 'center',
    'type' => 'callback',
    'callback' => function (array $row) {
        if ($row['preisautomatik_aktiv']) {
            $title = 'Preisautomatik aktiv - ';

            if ($row['preisautomatik_platz_behind']) {
                $title .= 'hinter ';
            }

            $title .= 'Platz ' . $row['preisautomatik_platz'];
            $title .= ' mit mindestens ' . $row['preisautomatik_formel'] . ' Spanne';

            return '<img src="/res/images/preisvergleich_on.gif" alt="' . $title . '" title="' . $title . '">';
        }
        return '<img src="/res/images/preisvergleich_off.gif" alt="nicht aktiv">';
    }
];


return $config;
