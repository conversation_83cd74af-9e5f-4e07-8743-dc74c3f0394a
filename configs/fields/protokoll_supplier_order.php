<?php

$config = [];

$config['protokoll_supplier_order.date_added'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i',
    'order' => true
];


$config['MAKRO.protokoll_supplier_order.user'] = [
    'label' => 'User',
    'type' => 'callback',
    'sql' => ['protokoll_supplier_order.user_id'],
    'order' => 'protokoll_supplier_order.user_id',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];


$config['protokoll_supplier_order.type'] = [
    'label' => 'Type',
    'type' => 'string',
];
$config['protokoll_supplier_order.type_id'] = [
    'label' => 'Type Id',
    'type' => 'string',
];
$config['protokoll_supplier_order.field'] = [
    'label' => 'Feld',
    'type' => 'string',
];

$config['protokoll_supplier_order.new_value'] = [
    'label' => 'neuer Wert',
    'type' => 'string',
];

return $config;
