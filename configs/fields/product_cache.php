<?php

use bqp\Utils\StringUtils;

$config = [];

$config['product_cache.statistik_anz_verkauefe'] = [
    'label' => '<small>Verkäufe<br>90 Tage</small>',
    'type' => 'integer',
    'align' => 'center',
    'order' => true
];

$config['product_cache.statistik_orders_total'] = [
    'label' => '<small>Verkäufe<br>Total</small>',
    'type' => 'integer',
    'align' => 'center',
    'order' => true
];

$config['product_cache.statistik_reklamation_total'] = [
    'label' => '<small>Reklamation<br>Total</small>',
    'type' => 'integer',
    'align' => 'center',
    'order' => true
];

$config['product_cache.statistik_widerruf_total'] = [
    'label' => '<small>Widerruf<br>Total</small>',
    'type' => 'integer',
    'align' => 'center',
    'order' => true
];

$config['MAKRO.product_cache.klickkosten'] = [
    'label' => 'Klickkosten',
    'type' => 'callback',
    'sql' => ['product_cache.coast_per_buy_1', 'product_cache.statistik_anz_verkauefe'],
    'align' => 'right',
    'callback' => function($daten) {
        $ck = abs($daten['coast_per_buy_1']);
        $bg = '#80FF80';
        if($ck > 10) $bg = '#FF8080';
        elseif($ck > 5) $bg = '#FEBC63';
        $add = $daten['coast_per_buy_1'] < 0 ? '*' : '';

        return ['value' => $add . StringUtils::formatPrice($ck), 'style' => 'background-color: ' . $bg . ' ! important; text-align: right;'];
    }
];

return $config;
