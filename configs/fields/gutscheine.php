<?php

use bqp\Currency\CurrencyUtils;

$config = [];

$config['gutscheine.gutschein_code'] = [
    'label' => 'Gutscheincode',
    'type' => 'string'
];

$config['gutscheine.date_create'] = [
    'label' => 'Datum erstellt',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['gutscheine.expire'] = [
    'label' => 'abgelaufen',
    'type' => 'string',
    'order' => true
];

$config['gutscheine.expire_date'] = [
    'label' => 'Verfallsdatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['gutscheine.expire_count'] = [
    'label' => 'maximale Einlösungen',
    'type' => 'string',
    'order' => true
];

$config['gutscheine.gutscheinwert'] = [
    'label' => 'Gutscheinwert',
    'type' => 'currency',
    'order' => true
];

$config['MAKRO.gutscheine.gutscheinwert'] = [
    'label' => 'Gutscheinwert',
    'order' => 'gutscheine.gutscheinwert',
    'sql' => ['gutscheine.gutscheinwert', 'gutscheine.gutscheinwert_art'],
    'type' => 'callback',
    'callback' => function ($daten) {
        switch($daten['gutscheinwert_art']) {
            case 'euro':
                return CurrencyUtils::format($daten['gutscheinwert']);
            case 'prozent':
                return $daten['gutscheinwert'] . ' %';
        }
    }
];

$config['gutscheine.min_warenwert'] = [
    'label' => 'mindest Warenwert',
    'type' => 'currency',
    'order' => true
];

$config['gutscheine.neukunde'] = [
    'label' => 'nur Nekunden',
    'type' => 'string'
];

$config['gutscheine.beschreibung'] = [
    'label' => 'Gutscheintext',
    'type' => 'string'
];

$config['gutscheine.count_eingeloest'] = [
    'label' => 'eingelöst',
    'type' => 'string'
];

return $config;
