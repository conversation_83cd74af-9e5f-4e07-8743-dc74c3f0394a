<?php

$config = [];


$config['protokoll_supplier_order_transfer.transfer_id'] = [
    'label' => 'Transfer-ID',
    'type' => 'integer',
    'required' => true,
    'order' => true
];

$config['protokoll_supplier_order_transfer.transfer_status'] = [
    'label' => 'Übertragungsstatus',
    'type' => 'string',
    'required' => true
];


/*    protokoll_supplier_order_transfer.user_id,*/

$config['protokoll_supplier_order_transfer.date_send'] = [
    'label' => 'Datum',
    'type' => 'string',
    'required' => true
];

$config['protokoll_supplier_order_transfer.transfer_type'] = [
    'label' => 'Übertragungsart',
    'type' => 'string',
    'required' => true
];

$config['protokoll_supplier_order_transfer.extra'] = [
    'label' => 'Extra',
    'type' => 'string',
    'required' => true
];

$config['MAKRO.protokoll_supplier_order_transfer.user'] = [
    'label' => 'User',
    'type' => 'callback',
    'sql' => ['protokoll_supplier_order_transfer.user_id'],
    'order' => 'protokoll_supplier_order_transfer.user_id',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

return $config;
