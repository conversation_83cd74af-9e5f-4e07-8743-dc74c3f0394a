<?php

use wws\Product\ProductRepositoryLegacy;

$config = [];

$config['protokoll_product.id'] = [
    'label' => 'Id',
    'type' => 'int',
];

$config['protokoll_product.product_id'] = [
    'label' => 'Produkt ID',
    'order' => true,
    'type' => 'callback',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_id']
];

$config['protokoll_product.date_added'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true,
];

$config['protokoll_product.user_id'] = [
    'label' => 'UserId',
    'type' => 'string',
];

$config['protokoll_product.new_value'] = [
    'label' => 'neuer Wert',
    'type' => 'string',
    'style' => 'word-wrap: break-word; word-break: break-all;',
    'as_table' => [
        'type' => 'html',
    ]
];

$config['protokoll_product.field'] = [
    'label' => 'Feld',
    'type' => 'string',
];

$config['MAKRO.protokoll_product.user'] = [
    'label' => 'User',
    'type' => 'callback',
    'sql' => ['protokoll_product.user_id'],
    'order' => 'protokoll_product.user_id',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

$config['MAKRO.protokoll_product.typ'] = [
    'label' => 'type',
    'type' => 'callback',
    'sql' => ['protokoll_product.type', 'protokoll_product.type_id'],
    'order' => 'protokoll_product.type',
    'callback' => [['di' => wws\Product\ModelProtocolTranslatorProduct::class], 'tableHelperTranslateType']
];

return $config;
