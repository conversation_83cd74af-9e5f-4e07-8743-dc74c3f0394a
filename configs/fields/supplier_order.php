<?php

use bqp\form\form_element_select;
use bqp\Utils\StringUtils;
use wws\Lager\LagerRepository;
use wws\Supplier\SupplierOrder;
use wws\Supplier\SupplierOrderRepository;
use wws\Supplier\SupplierOrderTagRepository;
use wws\Supplier\SupplierRepository;

$config = [];


$config['protokoll'] = [
    'table' => 'protokoll_supplier_order',
    'entity_id_field' => 'supplier_order_id'
];

$config['table_object_field_select'] = [
    'group_label' => 'Lieferanten-Bestellung',
];

$config['supplier_order.supplier_id'] = [
    'label' => 'Grossist',
    'type' => 'supplier',
    'required' => true,
    'empty_option' => true,
    'order' => true,
    'dynamic_suffix_template' => '\'<a href="/av/gs/index.php?page=gs&modul=bearbeitung_lager&supplier_id=\'+value+\'&show=&lager_id=1" target="_blank">Anzeigen</a>\'',
    'as_filter' => [
        'required' => false,
        'empty_option' => false
    ],
    'as_multi_filter' => [
        'multiple' => true,
        'required' => false,
        'empty_option' => false
    ]
];

$config['supplier_order.bestell_nr'] = [
    'label' => 'Bestell-Nr.',
    'type' => 'string',
    'length_max' => 20,
    'required' => true,
    'order' => true,
    'template' => '<a href="/ax/gs/bestellung/?supplier_order_id={{$supplier_order_id}}" onclick="popup_large(event)">__VALUE__</a>'
];

$config['supplier_order.kundennr'] = [
    'label' => 'Kundennr.',
    'type' => 'select',
    'input_callback' => function (form_element_select $element) {
        //die optionen werden erst beim anzeigen des formulars nachgeladen, standard filter muss raus
        foreach ($element->getPreFilter() as $filter) {
            $element->removePreFilter($filter);
        }
    }
];

$config['supplier_order.status'] = [
    'label' => 'Status',
    'type' => 'enum',
    'enums' => function () {
        return SupplierRepository::getSupplierOrderStatuses();
    },
    'as_table' => [
        'type' => 'callback',
        'callback' => function (array $row) {
            if ($row['status'] === SupplierOrder::STATUS_STORNIERT) {
                return '<span style="background-color: #d7d7d7; display: inline-block; padding: 3px;">' . $row['status'] . '</span>';
            }

            if ($row['status'] === SupplierOrder::STATUS_ERLEDIGT) {
                return '<span style="background-color: #adff59; display: inline-block; padding: 3px;">' . $row['status'] . '</span>';
            }

            return $row['status'];
        }
    ]
];


$config['supplier_order.delivery_date'] = [
    'label' => 'erwartetes Lieferdatum',
    'type' => 'date',
    'required' => false,
    'order' => true
];

$config['supplier_order.delivery_date_min'] = [
    'label' => 'erwartetes Lieferdatum',
    'type' => 'date',
    'required' => false,
    'order' => true
];

$config['supplier_order.bemerkung'] = [
    'label' => 'Bemerkung für Lieferant',
    'type' => 'memo'
];

$config['supplier_order.externe_referenz_1'] = [
    'label' => 'ext. Referenz 1',
    'type' => 'text'
];

$config['supplier_order.externe_referenz_2'] = [
    'label' => 'ext. Referenz 2',
    'type' => 'text'
];

$config['supplier_order.shipping_costs_net_expected'] = [
    'label' => 'erwartete Versandkosten (Netto)',
    'type' => 'currency',
    'required' => false,
    'can_empty' => true
];

$config['supplier_order.supplier_order_id'] = [
    'label' => 'Bestellnr.',
    'type' => 'string',
    'order' => true,
    'template' => '<a href="/ax/gs/bestellung/?supplier_order_id={{$supplier_order_id}}" target="inline">B{{$supplier_order_id}}</a>'
];

$config['supplier_order.datum_anfrage'] = [
    'label' => 'Datum angefragt',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i',
    'order' => true
];

$config['supplier_order.datum_bestellt'] = [
    'label' => 'Datum bestellt',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i',
    'order' => true
];

$config['MAKRO.supplier_order.ueberfaellig'] = [
    'label' => 'Überfällig',
    'order' => 'ueberfaellig',
    'type' => 'callback',
    'sql' => [
        "IF (supplier_order.status IN ('" . SupplierOrder::STATUS_BESTELLT . "', '" . SupplierOrder::STATUS_UNTERWEGS . "'), (TO_DAYS(NOW())-TO_DAYS(supplier_order.delivery_date_min)), NULL) AS ueberfaellig",
        'supplier_order.status',
    ],
    'align' => 'center',
    'callback' => function (array $row) {
        $status = $row['status'];

        if ($status === SupplierOrder::STATUS_BESTELLT || $status === SupplierOrder::STATUS_UNTERWEGS) {
            $color = SupplierOrderRepository::getOverdueColor($row['ueberfaellig']);

            return ['value' => $row['ueberfaellig'], 'style' => 'background-color: ' . $color . ';'];
        }

        return '';
    }
];

$config['MAKRO.supplier_order.tracking_state'] = [
    'label' => 'Tracking',
    'order' => 'supplier_order.datum_tracking',
    'sql' => ['supplier_order.datum_tracking'],
    'type' => 'callback',
    'callback' => function (array $row) {
        if ($row['datum_tracking']) {
            $tracking_date = DateTime::createFromFormat('Y-m-d', $row['datum_tracking']);
            return '<i class="fa fa-truck" title="Tracking-Datum: ' . $tracking_date->format('d.m.Y') . '" style="scale: 1.5; color: #005992;"></i>';
        }
        return "";
    }
];

$config['MAKRO.supplier_order.supplier_order_tags'] = [
    'label' => 'Tags',
    'order' => 'supplier_order.supplier_order_tags',
    'sql' => ['supplier_order.supplier_order_tags'],
    'type' => 'callback',
    'callback' => function (array $row) {
        if ($row['supplier_order_tags']) {
            $supplier_order_tag_repository = service_loader::getDiContainer()->get(SupplierOrderTagRepository::class);
            return $supplier_order_tag_repository->formatSupplierOrderTagsAsShortHtml($row['supplier_order_tags']);
        }
        return "";
    }
];

$config['MAKRO.supplier_order.lager'] = [
    'label' => 'Ziel',
    'type' => 'callback',
    'sql' => ['supplier_order.lager_id', 'supplier_order.order_id'],
    'callback' => function (array $row) {
        if ($row['lager_id'] == 0) {
            return '<span style="color: #aaa;">Kunde</span>';
        }

        return LagerRepository::getLagerName($row['lager_id']);
    }
];

$config['MAKRO.supplier_order.bemerkung_small'] = [
    'label' => 'Bemerkung',
    'type' => 'callback',
    'sql' => ['supplier_order.bemerkung'],
    'callback' => function (array $row) {
        $bemerkung = $row['bemerkung'] ?? '';

        if (strlen($bemerkung) > 30) {
            return '<span title="' . StringUtils::htmlentities($bemerkung) . '">' . StringUtils::limit($bemerkung, 30) . '...</span>';
        }

        return $bemerkung;
    }
];

$config['MAKRO.supplier_order.user'] = [
    'label' => 'User',
    'type' => 'callback',
    'sql' => ['supplier_order.user_id'],
    'order' => 'supplier_order.user_id',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

return $config;
