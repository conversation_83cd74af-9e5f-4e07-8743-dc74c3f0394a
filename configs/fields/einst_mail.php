<?php

$config = [];


$config['einst_mail.mail_id'] = [
    'label' => 'Mail-ID',
    'hint' => 'wird ggf. automatisch vergeben',
    'type' => 'std_string',
    'order' => true
];

$config['einst_mail.mail_kat_id'] = [
    'label' => 'Kategorie',
    'type' => 'enum',
    'required' => true,
    'enums' => function() {
        $db = db::getInstance();

        $cats_raw = $db->query("
            SELECT
                einst_mail_kats.mail_parent_kat_id,
                einst_mail_kats.mail_kat_id,
                einst_mail_kats.kat_name
            FROM
                einst_mail_kats
            ORDER BY
                einst_mail_kats.kat_name
        ")->asMultiArray('mail_parent_kat_id');

        $result = [];

        foreach($cats_raw[0] AS $cat) {
            $result[$cat['mail_kat_id']] = $cat['kat_name'];

            $cat_id = $cat['mail_kat_id'];
            if(isset($cats_raw[$cat['mail_kat_id']])) {
                foreach($cats_raw[$cat_id] AS $cat) {
                    $result[$cat['mail_kat_id']] = '-' . $cat['kat_name'];
                }
            }
        }

        return $result;
    }
];



$config['einst_mail.beschreibung'] = [
    'label' => 'Beschreibung',
    'type' => 'std_string',
    'order' => true,
    'required' => true
];

$config['einst_mail.system_mail'] = [
    'label' => 'System-Mail',
    'type' => 'bool',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => function($daten) { return $daten['system_mail'] ? 'Ja' : ''; }
    ]
];

$config['einst_mail.absender_name'] = [
    'label' => 'Absendername',
    'type' => 'std_string'
];

$config['einst_mail.absender_mail'] = [
    'label' => 'Absender-Email',
    'type' => 'std_string'
];

$config['einst_mail.betreff'] = [
    'label' => 'Betreff',
    'type' => 'std_string'
];

$config['einst_mail.text_mail'] = [
    'label' => 'Mail',
    'type' => 'std_memo_xlarge'
];

$config['einst_mail.text_customer_memo'] = [
    'label' => 'Bemerkung zum Kundenmemofeld',
    'type' => 'std_memo'
];

$config['MAKRO.einst_mail.has_attachments'] = [
    'label' => 'Anlagen',
    'sql' => ['IF(einst_mail.attachments != "", 1, 0) AS has_attachments'],
    'order' => 'einst_mail.attachments',
    'type' => 'callback',
    'callback' => function($daten) {
        return $daten['has_attachments'] ? 'Ja' : '';
    }
];

return $config;
