<?php

use wws\Customer\CustomerRepository;

$config = [];

$config['protokoll'] = [
    'table' => 'protokoll_customer',
    'entity_id_field' => 'customer_id',
    'field_modus' => 'string'
];

$config['table_object_field_select'] = [
    'group_label' => 'Kunde',
];

$config['customers.debtor_account'] = [
    'label' => 'Debitorenkonto',
    'type' => 'numeric'
];



$config['customers.customer_id'] = [
    'label' => 'Kundennr.',
    'order' => true,
    'type' => 'string',
    'as_table' => [
        'type' => 'callback',
        'callback' => [CustomerRepository::class, 'tableHelper_customer_nr']
    ]
];

$config['customers.firma'] = [
    'label' => 'Firma',
    'type' => 'string',
    'order' => true
];

$config['customers.name'] = [
    'label' => 'Name',
    'type' => 'string',
    'order' => true
];

$config['customers.vorname'] = [
    'label' => 'Vorname',
    'type' => 'string',
    'order' => true
];

$config['customers.plz'] = [
    'label' => 'PLZ',
    'type' => 'string',
    'order' => true
];
$config['customers.ort'] = [
    'label' => 'Ort',
    'type' => 'string',
    'order' => true
];


$config['MAKRO.customers.customer_name'] = [
    'sql' => ['customers.vorname', 'customers.name'],
    'label' => 'Name',
    'order' => 'customers.name',
    'type' => 'string',
    'template' => '{{$vorname}} {{$name}}',
];

$config['MAKRO.customers.customer_name_firma'] = [
    'sql' => ['customers.name', 'customers.vorname', 'customers.firma'],
    'label' => 'Kunde',
    'order' => 'customers.name',
    'type' => 'callback',
    'callback' => function ($daten) {
        $return = '';
        if($daten['firma']) $return .= $daten['firma'] . '<br />';
        return $return . $daten['vorname'] . ' ' . $daten['name'];
    }
];

$config['MAKRO.customers.customer_name_or_firma'] = [
    'sql' => ['customers.name', 'customers.vorname', 'customers.firma'],
    'label' => 'Kunde',
    'order' => 'customers.name',
    'type' => 'callback',
    'callback' => function ($daten) {
        $return = '';
        if(isset($daten['customer_id'])) {
            $return .= '<a href="/ax/customer/?customer_id=' . $daten['customer_id'] . '" target="inline">';
        }

        if($daten['firma']) $return .= $daten['firma'];
        else $return .= $daten['vorname'] . ' ' . $daten['name'];

        if(isset($daten['customer_id'])) {
            $return .= '</a>';
        }

        return $return;
    }
];

$config['MAKRO.customers.customer_nr'] = [
    'sql' => ['customers.customer_id', 'customers.customer_nr'],
    'type' => 'string',
    'label' => 'Kundennr.',
    'order' => 'customers.customer_id',
    'template' => '<a href="/ax/customer/default/?customer_id={{$customer_id}}" onclick="popup_large(event)">__value__</a>'
];

$config['customers.customer_type'] = [
    'label' => 'Kundentyp',
    'type' => 'string',
    'order' => true
];


return $config;
