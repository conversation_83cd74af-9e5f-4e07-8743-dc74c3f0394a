<?php

$config = [];

$config['product_lager_meta.lager_bestand_1'] = [
    'label' => 'ecom<br>Lager',
    'type' => 'string',
    'align' => 'center',
    'order' => true
];

$config['MAKRO.product_lager_meta.lager_bestand_1'] = [
    'sql' => ['product_lager_meta.lager_bestand_1', 'product_lager_meta.lager_frei_1'],
    'type' => 'string',
    'label' => '<span title="Frei/Bestand"><nobr>Bestand 1</nobr></span>',
    'order' => 'product_lager_meta.lager_frei_1',
    'template' => '<span title="Frei/Bestand">{{$lager_frei_1}}/{{$lager_bestand_1}}</span>',
    'align' => 'center'
];



$config['product_lager_meta.lager_frei_1'] = [
    'label' => 'ecom<br>Frei',
    'type' => 'callback',
    'align' => 'center',
    'order' => true,
    'callback' => function($daten, $key) {
        $free_stock = $daten[$key];

        if ($free_stock == 0) {
            return $free_stock;
        }

        if ($free_stock < 0) {
            return '<b style="color: red;">' . $free_stock . '</b>';
        }

        if ($free_stock > 0) {
            return '<b style="color: green">' . $free_stock . '</b>';
        }
    }
];

$config['product_lager_meta.lager_frei_63'] = [
    'label' => 'AMZ<br>Frei',
    'type' => 'callback',
    'align' => 'center',
    'order' => true,
    'callback' => function($daten) {
        if($daten['lager_frei_63'] == 0) {
            return $daten['lager_frei_63'];
        }

        if($daten['lager_frei_63'] < 0) {
            return '<b style="color: red;">' . $daten['lager_frei_63'] . '</b>';
        }

        if($daten['lager_frei_63'] > 0) {
            return '<b style="color: green">' . $daten['lager_frei_63'] . '</b>';
        }
    }
];

$config['product_lager_meta.lager_frei_47'] = [
    'label' => 'FBA<br>Frei',
    'type' => 'callback',
    'align' => 'center',
    'order' => true,
    'callback' => function($daten) {
        if($daten['lager_frei_47'] == 0) {
            return $daten['lager_frei_47'];
        }

        if($daten['lager_frei_47'] < 0) {
            return '<b style="color: red;">' . $daten['lager_frei_47'] . '</b>';
        }

        if($daten['lager_frei_47'] > 0) {
            return '<b style="color: green">' . $daten['lager_frei_47'] . '</b>';
        }
    }
];

$config['product_lager_meta.lager_frei_35'] = [
    'label' => 'Krempl<br>Frei',
    'type' => 'callback',
    'align' => 'center',
    'order' => true,
    'callback' => function($daten) {
        if($daten['lager_frei_35'] == 0) {
            return $daten['lager_frei_35'];
        }

        if($daten['lager_frei_35'] < 0) {
            return '<b style="color: red;">' . $daten['lager_frei_35'] . '</b>';
        }

        if($daten['lager_frei_35'] > 0) {
            return '<b style="color: green">' . $daten['lager_frei_35'] . '</b>';
        }
    }
];

$config['product_lager_meta.lager_frei_44'] = [
    'label' => 'k11<br>Frei',
    'type' => 'callback',
    'align' => 'center',
    'order' => true,
    'callback' => function($daten) {
        if($daten['lager_frei_44'] == 0) {
            return $daten['lager_frei_44'];
        }

        if($daten['lager_frei_44'] < 0) {
            return '<b style="color: red;">' . $daten['lager_frei_44'] . '</b>';
        }

        if($daten['lager_frei_44'] > 0) {
            return '<b style="color: green">' . $daten['lager_frei_44'] . '</b>';
        }
    }
];


$config['product_lager_meta.lager_bestand_63'] = [
    'label' => 'AMZ<br>Lager',
    'type' => 'string',
    'align' => 'center',
    'order' => true
];


$config['product_lager_meta.lager_bestand_47'] = [
    'label' => 'FBA<br>Lager',
    'type' => 'string',
    'align' => 'center',
    'order' => true
];

$config['product_lager_meta.lager_bestand_35'] = [
    'label' => 'Krempl<br>Lager',
    'type' => 'string',
    'align' => 'center',
    'order' => true
];

$config['product_lager_meta.lager_bestand_44'] = [
    'label' => 'k11<br>Lager',
    'type' => 'string',
    'align' => 'center',
    'order' => true
];


return $config;
