<?php

use bqp\form\FormElementCountry;
use wws\Product\ProductBrand\ProductBrand;
use wws\Product\ProductRepositoryLegacy;

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'Marke',
];

$config['product_brand.brand_name'] = [
    'label' => 'Marke',
    'type' => 'string',
    'order' => true
];

$config['product_brand.brand_id'] = [
    'label' => 'ID',
    'type' => 'string',
    'order' => true
];

$config['product_brand.hersteller_url'] = [
    'label' => 'Hersteller Url',
    'type' => 'string',
    'order' => true,
    'as_table' => [
        'type' => 'short_url'
    ]
];

$config['product_brand.default_product_type'] = [
    'label' => 'Produkttyp',
    'type' => 'string',
    'order' => true,
    'as_table' => [
        'type' => 'callback',
        'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_type']
    ]
];


$config['product_brand.market_view_autocreate'] = [
    'label' => 'Autocreate',
    'type' => 'checkbox',
    'order' => true,
    'align' => 'center',
];


$config['product_brand.gpsr_status'] = [
    'label' => 'GPSR Status',
    'type' => 'enum',
    'order' => true,
    'enums' => [ProductBrand::GPSR_STATUS_VALID => 'gültig', ProductBrand::GPSR_STATUS_INVALID => 'ungültig'],
    'as_table' => [
        'type' => 'callback',
        'callback' => function (array $row, string $key) {
            return $row[$key] === ProductBrand::GPSR_STATUS_VALID ? '<i style="background-color: lime">gültig</i>' : '<i style="color: #ccc">ungültig</i>';
        }
    ]
];

$config['product_brand.gpsr_name_1'] = [
    'label' => 'Name 1',
    'hint' => 'z.B. Firma',
    'type' => 'string',
];

$config['product_brand.gpsr_name_2'] = [
    'label' => 'Name 2',
    'hint' => 'z.B. Ansprechpartner oder Abteilung',
    'type' => 'string',
];

$config['product_brand.gpsr_street'] = [
    'label' => 'Straße',
    'type' => 'string',
];

$config['product_brand.gpsr_postal_code'] = [
    'label' => 'Postleitzahl',
    'type' => 'string',
];
$config['product_brand.gpsr_city'] = [
    'label' => 'Stadt',
    'type' => 'string',
];

$config['product_brand.gpsr_state'] = [
    'label' => 'Bundesland',
    'type' => 'string',
];
$config['product_brand.gpsr_country_id'] = [
    'label' => 'Land',
    'type' => FormElementCountry::class,
];

$config['product_brand.gpsr_email'] = [
    'label' => 'Email',
    'type' => 'string',
];

$config['product_brand.gpsr_web'] = [
    'label' => 'Web',
    'type' => 'string',
];

$config['product_brand.gpsr_phone'] = [
    'label' => 'Telefon',
    'type' => 'string',
];



$config['MAKRO.product_brand.logo'] = [
    'label' => 'Logo',
    'sql' => ['IF(product_brand.brand_logo != "",1,0) AS logo'],
    'order' => 'product_brand.brand_logo',
    'type' => 'callback',
    'callback' => function($daten) {
        if($daten['logo']) {
            return '<img src="/res/images/chkbox_on.gif" alt="an">';
        } else {
            return '<img src="/res/images/chkbox_off.gif" alt="aus">';
        }
    }
];

$config['MAKRO.product_brand.picuse'] = [
    'label' => 'Bilder',
    'sql' => ['IF(product_brand.picuse_1!="",1,0) AS picuse'],
    'order' => 'product_brand.picuse_1',
    'type' => 'callback',
    'callback' => function($daten) {
        if($daten['picuse']) {
            return '<img src="/res/images/chkbox_on.gif" alt="an">';
        } else {
            return '<img src="/res/images/chkbox_off.gif" alt="aus">';
        }
    }
];

return $config;
