<?php

use wws\MarketView\MarketViewConst;
use wws\MarketView\MarketViewFactory;
use wws\Product\ProductRepositoryLegacy;

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'MarketView - Angebot',
];


$config['market_view_product.product_name'] = [
    'label' => 'Produkt',
    'order' => true,
    'type' => 'string',
    'template' => '<a href="/ax/market_view/product/?mvsrc_id={{$mvsrc_id}}&mvsrc_product_id={{$mvsrc_product_id|urlencode}}" onclick="popup_large(event)" class="inline">__value__</a>'
];

$config['market_view_product.genuine_part'] = [
    'label' => 'Orginalteil',
    'type' => 'text',
    'as_table' => [
        'label' => '<span title="Orginalteil/Alternativteil">OT/AT</span>',
        'type' => 'callback',
        'align' => 'center',
        'callback' => function($daten, $key) {
            $value = $daten[$key];
            if($value === null) {
                return '';
            }
            return $value ? '<b style="color:green;">OT</b>' : '<b style="color:red;">AT</b>';
        }
    ]
];

$config['market_view_product.product_type'] = [
    'label' => 'Produktgruppe',
    'type' => 'enum',
    'enums' => [],
    'order' => true,
    'as_table' => [
        'label' => '<span title="Produktgruppe">Typ</span>',
        'type' => 'callback',
        'align' => 'center',
        'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_type']
    ]
];

$config['market_view_product.versand_netto'] = [
    'label' => 'Versand',
    'type' => 'currency',
    'order' => true
];

$config['market_view_product.ean'] = [
    'label' => 'EAN',
    'order' => true,
    'type' => 'string',
];

$config['market_view_product.beschreibung'] = [
    'label' => 'Beschreibung',
    'type' => 'string',
];

$config['market_view_product.lieferumfang'] = [
    'label' => 'Lieferumfang',
    'type' => 'string',
];

$config['market_view_product.mpn'] = [
    'label' => 'Hst-Nr.',
    'order' => true,
    'type' => 'string',
];

$config['market_view_product.mvsrc_product_id'] = [
    'label' => 'ext. Artnr.',
    'order' => true,
    'type' => 'string',
];

$config['market_view_product.mv_availability'] = [
    'label' => 'Verfügbarkeit Quelle',
    'order' => true,
    'type' => 'string',
];

$config['market_view_product.availability_id'] = [
    'label' => 'Verfügbarkeit',
    'order' => true,
    'type' => 'callback',
    'callback' => [['factory' => MarketViewFactory::class, 'MarketViewOutputHelper'], 'tableHelper_availability']
];

$config['market_view_product.date_added'] = [
    'label' => 'Angelegt',
    'order' => true,
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
];

$config['MAKRO.market_view_product.availability'] = [
    'sql' => ['market_view_product.availability_id', 'market_view_product.mv_availability'],
    'label' => 'Verfügbarkeit',
    'order' => 'market_view_product.availability_id',
    'type' => 'callback',
    'callback' => [['factory' => MarketViewFactory::class, 'MarketViewOutputHelper'], 'tableHelper_availability']
];

$config['MAKRO.market_view_product.inventory'] = [
    'sql' => ['market_view_product.inventory'],
    'label' => 'Bestand',
    'order' => 'market_view_product.inventory',
    'type' => 'callback',
    'callback' => function ($daten, $field) {
        return $daten[$field] ? $daten[$field] : '';
    }
];

$config['MAKRO.market_view_product.other_sources'] = [
    'sql' => ['market_view_product.other_sources_online', 'market_view_product.other_sources_available'],
    'label' => '<span title="Angebote Online/Bestand">Gänigkeit</span>',
    'type' => 'string',
    'order' => 'market_view_product.other_sources_online',
    'template' => '{{$other_sources_online}}/{{$other_sources_available}}',
    'link' => '<a href="/ax/market_view/product/market/?mvsrc_id={{$mvsrc_id}}&mvsrc_product_id={{$mvsrc_product_id|urlencode}}" onclick="popup_large(event)" class="inline">__value__</a>',
    'align' => 'center'
];

$config['market_view_product.product_id'] = [
    'label' => 'Produkt ID',
    'order' => true,
    'type' => 'callback',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_id']
];


$config['market_view_product.vk_netto'] = [
    'label' => 'Preis',
    'type' => 'currency',
    'align' => 'right',
    'order' => true,
    'template' => '<nobr>__value__</nobr>'
];

$config['MAKRO.market_view_product.vk_netto'] = [
    'sql' => ['market_view_product.vk_netto', 'market_view_product.vk_netto_per_quantity', 'market_view_product.vk_netto_info', 'market_view_product.vk_netto_max', 'market_view_product.url'],
    'label' => 'Preis',
    'order' => 'market_view_product.vk_netto',
    'type' => 'callback',
    'callback' => function ($daten) {
        if ($daten['vk_netto_max']) {
            $vk = '<b>' . output::formatPrice($daten['vk_netto']) . '</b> - ' . output::formatPrice($daten['vk_netto_max']);
        } else {
            $vk = output::formatPrice($daten['vk_netto']);
        }

        $vk = '<nobr>' . $vk . '</nobr>';

        $info = '';

        if ($daten['vk_netto_info']) {
            if ($daten['mvsrc_id'] == MarketViewConst::MVSRC_ID_4 || $daten['mvsrc_id'] == MarketViewConst::MVSRC_ID_36) {
                $info = '<a href="' . url_gateway::create($daten['url']) . '" target="_blank">' . $daten['vk_netto_info'] . '</a>';
            } else {
                $info = $daten['vk_netto_info'];
            }
        }

        if ($daten['vk_netto_per_quantity'] > 1) {
            if ($info) {
                $info .= ' | ';
            }
            $info .= 'Preis pro ' . $daten['vk_netto_per_quantity'] . ' Stk';
        }

        if ($info) {
            $vk .= '<br><small>' . $info . '</small>';
        }

        return [
            'value' => $vk,
            'csv_value' => output::formatPrice($daten['vk_netto'])
        ];
    }
];

return $config;
