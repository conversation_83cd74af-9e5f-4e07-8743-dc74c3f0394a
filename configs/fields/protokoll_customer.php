<?php

$config = [];

$config['protokoll_customer.user_id'] = [
    'label' => 'UserId',
    'type' => 'string'
];


$config['protokoll_customer.date_added'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['protokoll_customer.new_value'] = [
    'label' => 'neuer Wert',
    'type' => 'string'
];

$config['protokoll_customer.field'] = [
    'label' => 'Feld',
    'type' => 'string'
];

$config['MAKRO.protokoll_customer.user'] = [
    'label' => 'User',
    'sql' => ['protokoll_customer.user_id'],
    'order' => 'protokoll_customer.user_id',
    'type' => 'callback',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

return $config;
