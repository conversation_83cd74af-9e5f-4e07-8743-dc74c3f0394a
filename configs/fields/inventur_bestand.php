<?php

$config = [];



$config['inventur_bestand.bestand_ist'] = [
    'label' => 'Bestand ist',
    'order' => true,
    'type' => 'string'
];

$config['inventur_bestand.bestand_soll'] = [
    'label' => 'Bestand soll',
    'order' => true,
    'type' => 'string'
];

$config['inventur_bestand.ek_netto'] = [
    'label' => 'einzel E<PERSON>',
    'order' => true,
    'type' => 'currency'
];


$config['inventur.inventur_id'] = [
    'label' => 'Inventur ID',
    'type' => 'string',
    'order' => true
];

$config['inventur.description'] = [
    'label' => 'Beschreibung',
    'type' => 'string'
];

$config['inventur.inventur_start'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['inventur.inventur_end'] = [
    'label' => 'Enddatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y',
    'order' => true
];

$config['inventur.inventur_status'] = [
    'label' => 'Status',
    'type' => 'string',
    'order' => true
];

$config['MAKRO.inventur.bestand_abweichung'] = [
    'label' => 'Bestandsabweichung',
    'sql' => ['inventur_bestand.bestand_ist-inventur_bestand.bestand_soll AS bestand_abweichung'],
    'order' => 'bestand_abweichung',
    'type' => 'callback',
    'callback' => function($daten) {
        if($daten['bestand_abweichung'] > 0) {
            return ['style' => 'background-color: #5EFF00;', 'value' => '<b>' . $daten['bestand_abweichung'] . '</b>'];
        } elseif($daten['bestand_abweichung'] < 0) {
            return ['style' => 'background-color: red;', 'value' => '<b>' . $daten['bestand_abweichung'] . '</b>'];
        }

        return $daten['bestand_abweichung'];
    },
    'rollup' => 'SUM',
    'rollup_complete' => 'SUM'
];

$config['MAKRO.inventur_bestand.bestand_wert_soll'] = [
    'label' => 'Wert soll',
    'sql' => ['inventur_bestand.bestand_soll*inventur_bestand.ek_netto AS bestand_wert_soll'],
    'order' => 'bestand_wert_soll',
    'type' => 'currency',
    'rollup' => 'SUM',
    'rollup_complete' => 'SUM'
];

$config['MAKRO.inventur_bestand.bestand_wert_ist'] = [
    'label' => 'Wert ist',
    'sql' => ['inventur_bestand.bestand_ist*inventur_bestand.ek_netto AS bestand_wert_ist'],
    'order' => 'bestand_wert_ist',
    'type' => 'currency',
    'rollup' => 'SUM',
    'rollup_complete' => 'SUM'
];

return $config;
