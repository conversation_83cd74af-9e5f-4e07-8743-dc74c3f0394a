<?php

$config = [];


$config['market_view_hersteller.hersteller_name'] = [
    'label' => 'Hersteller',
    'order' => true,
    'type' => 'string'
];

$config['MAKRO.market_view_hersteller.hersteller'] = [
    'sql' => ['market_view_hersteller.hersteller_name', 'market_view_hersteller.brand_id', 'market_view_hersteller.mv_hersteller_id'],
    'label' => 'Hersteller',
    'order' => 'market_view_hersteller.hersteller_name',
    'type' => 'callback',
    'callback' => function (array $row) {
        if ($row['brand_id']) {
            return '<span title="brand_id: '. $row['brand_id'] .'">' . $row['hersteller_name'] . '</span>';
        } else {
            return '<a href="/ax/market_view/matching_brand/matching/?mv_hersteller_id=' . $row['mv_hersteller_id'] . '" onclick="popup_standard(event)" style="color: #999; text-decoration: none;">' . $row['hersteller_name'] . '</a>';
        }
    }
];

return $config;
