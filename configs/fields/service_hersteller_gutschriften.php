<?php

$config = [];


$config['service_hersteller_gutschriften.hersteller_gutschrifts_id'] = [
    'label' => 'Nr.',
    'prefix' => 'HG-',
    'type' => 'string',
    'order' => true
];

$config['service_hersteller_gutschriften.datum'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format_output' => \bqp\Date\DateObj::DE_DATETIME,
    'order' => true
];

$config['service_hersteller_gutschriften.status'] = [
    'label' => 'Status',
    'order' => true,
    'type' => 'string',
];

$config['service_hersteller_gutschriften.betrag'] = [
    'label' => 'Betrag',
    'order' => true,
    'type' => 'currency'
];

$config['MAKRO.service_hersteller_gutschriften.frist'] = [
    'label' => 'Frist',
    'sql' => [
        "DATE_FORMAT(service_hersteller_gutschriften.datum_frist,'%d.%m.%Y') AS datum_frist",
        "TO_DAYS(NOW())-TO_DAYS(service_hersteller_gutschriften.datum_frist) AS datum_frist_tage",
    ],
    'order' => 'service_hersteller_gutschriften.datum_frist',
    'type' => 'callback',
    'callback' => function ($daten) {
        $bgcolor = '#FF2424';

        if($daten['datum_frist_tage'] < 0) {
            $bgcolor = '#79FF62';
        } elseif($daten['datum_frist_tage'] < 7) {
            $bgcolor = '#FFFF80';
        }

        $value = '';

        if($daten['status'] === 'angefordert' && $daten['datum_frist_tage']) {
            $value .= $daten['datum_frist'];
            $value .= '<br />';
            $value .= '(' . $daten['datum_frist_tage'] . ')';
        } else {
            $value .= '-';
        }

        return [
            'style' => 'background-color: ' . $bgcolor,
            'value' => $value
        ];
    }
];

$config['MAKRO.service_hersteller_gutschriften.angefordert_von'] = [
    'label' => 'Angefordert von',
    'sql' => [
        'service_hersteller_gutschriften.angefordert_von',
        'service_hersteller_gutschriften.angefordert_von_typ',
    ],
    'order' => 'service_hersteller_gutschriften.angefordert_von',
    'type' => 'callback',
    'callback' => function ($daten) {
        $return = '';
        $return .= $daten['angefordert_von'];
        $return .= '<br />';
        $return .= '(';
            switch ($daten['angefordert_von_typ']) {
                case 'grossist':
                    $return .= 'Grossist';
                    break;
                case 'hersteller':
                    $return .= 'Hersteller';
                    break;
                case 'kundenservice':
                    $return .= 'Kundenservice';
                    break;
                case 'sonstiges':
                    $return .= 'Sonstiges';
                    break;
            }
            $return .= ')';

        return $return;
    }
];

$config['service_hersteller_gutschriften.grund'] = [
    'label' => 'Grund',
    'type' => 'string'
];

$config['service_hersteller_gutschriften.bemerkung'] = [
    'label' => 'Bemerkung',
    'type' => 'string'
];

$config['MAKRO.service_hersteller_gutschriften.bemerkung_edit'] = [
    'label' => 'Bemerkung',
    'type' => 'string',
    'sql' => ['service_hersteller_gutschriften.bemerkung'],
    'template' => '<textarea onchange="saveHGSBemerkung({{$hersteller_gutschrifts_id}},this.value)">{{$bemerkung}}</textarea>'
];


$config['service_hersteller_gutschriften.user_id'] = [
    'label' => 'User',
    'type' => 'string',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

return $config;
