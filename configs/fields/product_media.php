<?php

use wws\Product\ProductRepositoryLegacy;

$config = [];

$config['product_media.product_id'] = [
    'label' => 'Produkt ID',
    'order' => true,
    'type' => 'callback',
    'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_id']
];

$config['product_media.mv_media_id'] = [
    'label' => 'mv_media_id',
    'order' => true,
    'type' => 'integer',
    'callback' => function (array $row) {
        return '<a href="/ax/market_view/media/quick_view/?mv_media_id=' . $row['mv_media_id'] . '" onclick="popup_standard(event)" class="inline">' . $row['mv_media_id'] . '</a>';
    }
];

return $config;
