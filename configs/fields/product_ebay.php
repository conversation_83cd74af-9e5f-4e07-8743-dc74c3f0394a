<?php

use bqp\Model\EntityChanges;

$config = [];

$config['protokoll'] = [
    'table' => 'protokoll_product_ebay',
    'entity_id_field' => 'product_id',
    'field_modus' => EntityChanges::FIELD_MODUS_ID,
    'type_id_modus' => EntityChanges::TYPE_ID_MODUS_STRING,
    'limit' => 256,
    'save_filter_callback' => function (array $entry): bool {
        return $entry['field'] !== 'ebay_specifics';
    },
];

$config['table_object_field_select'] = [
    'group_label' => 'Ebay',
];

return $config;
