<?php

use wws\Product\ProductLagerProtokoll;

$config = [];

$config['protokoll_lager.id'] = [
    'label' => 'ID',
    'type' => 'int'
];

$config['protokoll_lager.grund'] = [
    'label' => 'Grund',
    'type' => 'string'
];

$config['protokoll_lager.datum'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['protokoll_lager.lager_alt'] = [
    'label' => 'Bestand alt',
    'type' => 'int',
    'align' => 'center'
];

$config['protokoll_lager.lager_neu'] = [
    'label' => 'Bestand neu',
    'type' => 'int',
    'align' => 'center'
];


$config['protokoll_lager.user_id'] = [
    'label' => 'User',
    'type' => 'int',
    'as_table' => [
        'type' => 'callback',
        'callback' => [wws\Users\UserRepository::class, 'tableHelper_username'],
    ],
];

$config['MAKRO.protokoll_lager.grund'] = [
    'label' => 'Grund',
    'type' => 'callback',
    'sql' => ['protokoll_lager.grund'],
    'callback' => function($values) { return ProductLagerProtokoll::translateReason($values['grund']); }
];

return $config;
