<?php

use bqp\Utils\StringUtils;

$config = [];

$config['protokoll_system.protokoll_kategorie'] = [
    'label' => 'Protokoll',
    'type' => 'string',
];

$config['protokoll_system.status'] = [
    'label' => 'Status',
    'type' => 'string',
];


$config['protokoll_system.datum'] = [
    'label' => 'Datum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s.u',
];


$config['protokoll_system.text'] = [
    'label' => 'Info',
    'type' => 'string',
    'template' => '<div style="white-space: pre;">__value__</div>',
    'order' => true
];

$config['protokoll_system.details'] = [
    'label' => 'Details',
    'type' => 'string',
    'as_table' => [
        'type' => 'callback',
        'callback' => function($daten, $key) {
            if($daten[$key]) {
                return '<pre style="max-height: 200px; max-width: 1000px;">' . StringUtils::htmlentities($daten[$key]) . '</pre>' . number_format(strlen($daten[$key]), 0, '', '.');
            }

            return '';
        }
    ]
];

$config['protokoll_system.ext_id'] = [
    'label' => 'ext. Id',
    'type' => 'string',
    'order' => true
];

return $config;
