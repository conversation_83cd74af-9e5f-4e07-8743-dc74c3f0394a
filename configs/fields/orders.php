<?php

use bqp\Exceptions\FatalException;
use bqp\Model\EntityChanges;
use wws\Payment\PaymentRepository;

$config = [];

$config['protokoll'] = [
    'table' => 'protokoll_order',
    'entity_id_field' => 'order_id',
    'field_modus' => EntityChanges::FIELD_MODUS_ID,
];


$config['table_object_field_select'] = [
    'group_label' => 'Bestellung',
];

$config['orders.auftnr'] = [
    'label' => 'Auftnr.',
    'type' => 'string',
    'as_table' => [
        'order' => true,
        'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=__value__" onclick="popup_large(event)" class="inline">__value__</a>'
    ]
];

$config['orders.shop_id'] = [
    'label' => 'Shop',
    'order' => true,
    'type' => 'callback',
    'callback' => function($daten, $key) {
        static $shops = null;
        if($shops === null) {
            $shops = \wws\business_structure\business_structure_factory::getRepository()->getShopNames();
        }
        return $shops[$daten[$key]];
    }
];

$config['orders.versand_memo'] = [
    'label' => 'Bemerkung für Logistik',
    'type' => 'memo',
    'as_table' => [
        'type' => 'callback',
        'callback' => function ($daten, $key) {
            return nl2br($daten[$key] ?? '');
        }
    ]
];

$config['orders.zahlungs_id'] = [
    'label' => 'Zahlungsart',
    'required' => true,
    'type' => 'enum',
    'enums' => function () {
        return PaymentRepository::getAllZahlungsarten();
    },
    'as_filter' => [
        'multiple' => true
    ]
    //    'as_table' => [
    //        'type' => 'callback',
    //        'callback' => [PaymentRepository::class, 'tableHelper_zahlungsart']
    //    ],
];


$config['orders.order_amount_gross'] = [
    'label' => 'Betrag',
    'type' => 'currency'
];


$config['orders.payment_referenz'] = [
    'label' => 'Zahlungsreferenz',
    'type' => 'string',
    'order' => true,
];



$config['MAKRO.orders.customer_nr'] = [
    'sql' => ['orders.customer_id', 'orders.shop_id'],
    'label' => 'Kundennr.',

    'order' => 'orders.customer_id',
    'type' => 'callback',
    'callback' => [\wws\Customer\CustomerRepository::class, 'tableHelper_customer_nr'],
    'template' => '<a href="/ax/customer/default/?customer_id={{$customer_id}}" onclick="popup_large(event)">__value__</a>'
];

$config['MAKRO.orders.auftnr_customer_nr'] = [
    'sql' => ['orders.auftnr', 'orders.customer_id', 'orders.shop_id'],
    'label' => 'Auftnr.<br>Kundennr.',
    'order' => 'orders.customer_id',
    'type' => 'callback',
    'callback' => [\wws\Customer\CustomerRepository::class, 'tableHelper_customer_nr'],
    'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr={{$auftnr}}" onclick="popup_large(event)">{{$auftnr}}</a><br><a href="/ax/customer/default/?customer_id={{$customer_id}}" onclick="popup_large(event)" class="inline">__VALUE__</a>'
];


$config['orders.added'] = [
    'label' => 'Bestelldatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['orders.versandkosten'] = [
    'label' => 'Versandkosten',
    'type' => 'currency',
    'order' => true
];

$config['MAKRO.orders.rechnungs_betrag_brutto'] = [
    'sql' => ['SUM(order_item.quantity*order_item.preis) AS betrag'],
    'alias' => 'betrag',
    'label' => 'Betrag',
    'type' => 'currency',
    'order' => false,
    'rollup_complete' => 'SUM'
];

$config['orders.order_origin_id'] = [
    'label' => 'Herkunft',
    'type' => 'enum',
    'enums' => function () {
        return order_repository::getOrderOriginNames();
    },
    'as_filter' => [
        'multiple' => true
    ],
    'as_table' => [
        'type' => 'callback',
        'callback' => [order_repository::class, 'tableHelperOrderOrigin']
    ]
];

$config['MAKRO.orders.order_tags_small'] = [
    'label' => '',
    'sql' => ['orders.order_tags'],
    'type' => 'callback',
    'callback' => [\wws\Order\OrderTags::class, 'tableHelperOrderTagsSmall']
];

$config['orders.order_tags'] = [
    'label' => '',
    'sql' => ['orders.order_tags'],
    'type' => 'callback',
    'callback' => [\wws\Order\OrderTags::class, 'tableHelperOrderTagsSmall']
];

$config['MAKRO.orders.versand_memo_inline_edit'] = [
    'label' => 'Versand-Memo',
    'type' => 'callback',
    'sql' => ['orders.versand_memo', 'orders.order_id'],
    'callback' => function ($daten, $key) {
        if (!array_key_exists('order_id', $daten)) {
            throw new FatalException('order_id required for MAKRO.orders.versand_memo_inline_edit');
        }

        if (!$daten['order_id']) {
            return '';
        }

        return [
            'value' => '<div id="versand_memo_' . $daten['order_id'] . '" class="versand_memo">' . nl2br($daten['versand_memo']) . '</div>',
            'onclick' => 'OpenMEMO(' . $daten['order_id'] . ')'
        ];
    }
];

return $config;
