<?php

use bqp\form\FormElementBrand;

$config = [];

$config['protokoll'] = [
    'table' => 'protokoll_device',
    'entity_id_field' => 'device_id',
    'field_modus' => 'id'
];

$config['table_object_field_select'] = [
    'group_label' => 'Gerät',
];

$config['product_device.device_id'] = [
    'label' => 'Gerät ID',
    'type' => 'text',
    'order' => true,
];

$config['product_device.brand_id'] = [
    'label' => 'Hersteller',
    'type' => 'text',
    'length_max' => 200,
    'order' => true,
    'as_form' => [
        'type' => FormElementBrand::class
    ]
];


$config['product_device.model_name'] = [
    'label' => 'Modellname',
    'type' => 'text',
    'length_max' => 200,
    'order' => true,
    'as_table' => [
        'template' => '<a href="/ax/artikel/product_device/?device_id={{$device_id}}" onclick="popup_large(event)" class="inline">__VALUE__</a>'
    ]
];

$config['product_device.model_code'] = [
    'label' => 'Modellcode',
    'type' => 'text',
    'length_max' => 200,
    'order' => true,
    'as_table' => [
        'template' => '<a href="/ax/artikel/product_device/?device_id={{$device_id}}" onclick="popup_large(event)" class="inline">__VALUE__</a>'
    ]
];
$config['product_device.product_periode'] = [
    'label' => 'Produktionszeitraum',
    'type' => 'text',
    'order' => true,
    'length_max' => 200
];
$config['product_device.ean'] = [
    'label' => 'EAN',
    'type' => 'text',
    'length_max' => 200
];
$config['product_device.extra_1'] = [
    'label' => 'Extra 1',
    'type' => 'text',
    'length_max' => 200
];
$config['product_device.extra_2'] = [
    'label' => 'Extra 2',
    'type' => 'text',
    'length_max' => 200
];
$config['product_device.seo_index'] = [
    'label' => 'SEO',
    'type' => 'checkbox'
];


$config['product_device.device_detail_cat_id'] = [
    'label' => 'Kategorie',
    'type' => 'text',
    'as_form' => [
        'type' => 'ajax_category',
        'cat_tree_id' => 3
    ]
];

return $config;
