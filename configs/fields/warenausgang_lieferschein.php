<?php

use wws\Shipment\ShipmentRepository;

$config = [];

$config['table_object_field_select'] = [
    'group_label' => 'Warenausgang-Lieferschein',
];

$config['protokoll'] = [
    'table' => 'protokoll_wareneingang_lieferschein',
    'entity_id_field' => 'lieferschein_id'
];

$config['warenausgang_lieferschein.lieferschein_id'] = [
    'label' => 'Lieferschein-ID',
    'type' => 'string',
    'order' => true
];

$config['warenausgang_lieferschein.auftnr'] = [
    'label' => 'Auftnr.',
    'type' => 'string',
    'order' => true,
    'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=__value__" onclick="popup_large(event)" class="inline">__value__</a>'
];

$config['warenausgang_lieferschein.datum'] = [
    'label' => 'Lieferscheindatum',
    'type' => 'date',
    'format' => 'date',
    'format_output' => 'd.m.Y H:i:s',
    'order' => true
];

$config['warenausgang_lieferschein.lager_id'] = [
    'label' => 'Lager',
    'order' => true,
    'type' => 'integer',
    'as_table' => [
        'type' => 'callback',
        'callback' => [\wws\Lager\LagerRepository::class, 'tableHelper_lager'],
    ],
];

$config['warenausgang_lieferschein.sped_id'] = [
    'label' => 'Sped.',
    'order' => true,
    'type' => 'integer',
    'as_table' => [
        'type' => 'callback',
        'callback' => [ShipmentRepository::class, 'tableHelper_sped_logo'],
    ],
    'align' => 'center'
];

$config['warenausgang_lieferschein.status'] = [
    'label' => 'Status',
    'order' => true,
    'type' => 'string',
    'as_table' => [
        'type' => 'callback',
        'callback' => [\wws\Lager\WarenausgangLieferscheinRepository::class, 'tableHelper_status'],
    ]
];

$config['MAKRO.warenausgang_lieferschein.grouped_products'] = [
    'label' => 'Produkte',
    'sql' => ["GROUP_CONCAT(CONCAT(order_item.product_id,'||',warenausgang_lieferschein_items.anzahl,'||',order_item.product) SEPARATOR '|||') AS grouped_products"],
    'order' => 'order_item.product',
    'type' => 'callback',
    'callback' => function($daten, $key) {
        $return = '';
        $products = explode('|||', $daten[$key]);

        foreach($products AS $product_temp) {
            [$product_id, $quantity, $product_name] = explode('||', $product_temp);

            $return .= $quantity . ' x ' . '<a href="/ax/artikel/product/product/?back=close&product_id=' . $product_id . '" onclick="popup_large(event, {name:\'productwin\'})">' . $product_name . '</a><br>';
        }

        return $return;
    }
];

$config['warenausgang_lieferschein.supplier_order_id'] = [
    'label' => 'Bestellnr.',
    'type' => 'callback',
    'order' => true,
    'callback' => function (array $row, string $key) {
        $supplier_order_id = $row[$key];
        if ($supplier_order_id) {
            return '<a href="/ax/gs/bestellung/?supplier_order_id=' . $supplier_order_id . '" target="inline">B' . $supplier_order_id . '</a>';
        }

        return '';
    }
];

return $config;
