<?php

$config = [];

$config['defaults'] = [
    'buchhaltung_rueckzahlung.refund_id' => null,
    'buchhaltung_rueckzahlung.order_id' => 0,
    'buchhaltung_rueckzahlung.refund_status' => 'outstanding',
    'buchhaltung_rueckzahlung.added' => null,
    'buchhaltung_rueckzahlung.date_ended' => null,
    'buchhaltung_rueckzahlung.prioritaet' => 3,
    'buchhaltung_rueckzahlung.betrag' => 0,
    'buchhaltung_rueckzahlung.inhaber' => '',
    'buchhaltung_rueckzahlung.bank' => '',
    'buchhaltung_rueckzahlung.kontonummer' => '',
    'buchhaltung_rueckzahlung.blz' => '',
    'buchhaltung_rueckzahlung.user_id' => 0,
    'buchhaltung_rueckzahlung.iban' => '',
    'buchhaltung_rueckzahlung.bic' => '',
    'buchhaltung_rueckzahlung.frist' => null,
    'buchhaltung_rueckzahlung.bemerkung' => '',
    'buchhaltung_rueckzahlung.gsnr' => '',
    'buchhaltung_rueckzahlung.vormerken' => '',
    'buchhaltung_rueckzahlung.refund_method_id' => 0,
    'buchhaltung_rueckzahlung.refund_ext_reason_id' => ''
];

$config['buchhaltung_rueckzahlung.refund_method_id'] = [
    'label' => 'Rückzahlungsart',
    'required' => true,
    'type' => 'enum',
    'enums' => function() {
        return \wws\OrderRefund\OrderRefundFactory::getOrderRefundRepository()->getRefundMethodNames();
    },
    'as_filter' => [
        'multiple' => true
    ],
    'as_table' => [
        'callback' => [['factory' => '\wws\OrderRefund\OrderRefundFactory', 'getOrderRefundOutputHelper'], 'tableHelper_refound_method'],
        'type' => 'callback',
        'order' => true
    ]
];

$config['buchhaltung_rueckzahlung.refund_status'] = [
    'label' => 'Status',
    'type' => 'enum',
    'enums' => function() {
        return \wws\OrderRefund\OrderRefundFactory::getOrderRefundRepository()->getRefundStatusNames();
    },
    'as_filter' => [
        'multiple' => true
    ],
    'as_table' => [
        'callback' => [['factory' => '\wws\OrderRefund\OrderRefundFactory', 'getOrderRefundOutputHelper'], 'tableHelper_status'],
        'type' => 'callback',
        'order' => true
    ]
];

$config['buchhaltung_rueckzahlung.prioritaet'] = [
    'label' => 'Priorität',
    'type' => 'enum',
    'enums' => function() {
        return \wws\OrderRefund\OrderRefundFactory::getOrderRefundRepository()->getPrioritaeten();
    },
    'as_table' => [
        'callback' => [['factory' => '\wws\OrderRefund\OrderRefundFactory', 'getOrderRefundOutputHelper'], 'tableHelper_prioritaet'],
        'type' => 'callback',
        'order' => true
    ]
];

$config['buchhaltung_rueckzahlung.betrag'] = [
    'label' => 'Betrag',
    'type' => 'currency',
    'as_table' => [
        'order' => true,
        'rollup' => 'SUM'
    ]
];

$config['buchhaltung_rueckzahlung.added'] = [
    'label' => 'Datum',
    'type' => 'date',
    'as_table' => [
        'type' => 'date',
        'format' => 'date',
        'format_output' => 'd.m.Y H:i',
        'order' => true
    ]
];

$config['buchhaltung_rueckzahlung.frist'] = [
    'label' => 'Frist',
    'type' => 'date',
    'as_table' => [
        'type' => 'date',
        'format' => 'date',
        'format_output' => 'd.m.Y',
        'order' => true
    ]
];
$config['buchhaltung_rueckzahlung.bemerkung'] = [
    'label' => 'Bemerkung',
    'type' => 'string'
];


$config['MAKRO.buchhaltung_rueckzahlung.user'] = [
    'label' => 'User',
    'sql' => ['buchhaltung_rueckzahlung.user_id'],
    'order' => 'buchhaltung_rueckzahlung.user_id',
    'type' => 'callback',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

return $config;
