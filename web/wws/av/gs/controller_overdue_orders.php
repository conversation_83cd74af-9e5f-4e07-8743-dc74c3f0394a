<?php

use bqp\form\form_element_checkbox;
use bqp\form\form_element_select;
use bqp\form\wws_form;
use bqp\table\field\table_object_field_callback;
use bqp\table\field\table_object_field_date;
use bqp\table\field\table_object_field_text;
use bqp\table\TableObjectPersistent;
use wws\core\wws_controller;
use wws\Order\OrderConst;
use wws\Product\ProductRepositoryLegacy;
use wws\Shipment\Form\FormElementSpedition;
use wws\Shipment\ShipmentRepository;

//das ist nur für K11
class controller_overdue_orders extends wws_controller
{

    public function view_index(): void
    {
        $this->tpl->addForm($this->getForm(), 'form');

        $table = $this->table_overdue_orders();
        $this->tpl->addTable($table);
        $this->tpl->setTitle('Überfällige Aufträge - K11');
        $this->tpl->smartDisplay();
    }

    public function form_overdue_orders(): wws_form
    {
        return $this->form_index();
    }

    public function form_index(): wws_form
    {
        $form = new wws_form();
        $form->setAction($this->createViewUrl());

        $fieldset = $form->addFieldset('', 'Filter');

        $grouped_by_order = new form_element_checkbox();
        $grouped_by_order->setName('grouped_by_order');
        $grouped_by_order->setLabel('Auftragsansicht');

        $fieldset->add($grouped_by_order);

        $product_repository_legacy = service_loader::get(ProductRepositoryLegacy::class);
        $lieferbaranzeige = $product_repository_legacy::getLieferbaranzeigeNames($this->user->getShopId());

        $liefertermine = new form_element_select();
        $liefertermine->setName('liefertermine');
        $liefertermine->setLabel('Lieferzeit zur Bestellung');
        $liefertermine->addEmptyOption(true);
        $liefertermine->setSize('7');
        $liefertermine->setOptions($lieferbaranzeige);

        $fieldset->add($liefertermine);


        $element = new FormElementSpedition();
        $element->setLabel('Spedition');
        $element->setName('sped_id');
        $element->addEmptyOption(true);

        $fieldset->add($element);

        $form->addActionBarButton('Suchen', 'action_filter', true);

        $form->bindContainer($this->getPersistentContainer());

        return $form;
    }

    public function action_filter(): void
    {
        $this->getForm()->bindValid();
        $this->callView();
    }

    public function table_overdue_orders(): TableObjectPersistent
    {
        $where = '';
        $sped_id = $this->getForm()->getValueByName('sped_id');
        if ($sped_id) {
            $where .= ' AND order_item.sped_id = ' . (int)$sped_id;
        }

        $liefertermin = $this->getForm()->getValueByName('liefertermine');
        if ($liefertermin != '') {
            $where .= ' AND order_item.lieferbaranzeige = ' . (int)$liefertermin;
        }
        //ASWO soll mit rein, weil wir aktuell über unser Lager nach Österreich liefern
        //$where .= ' AND order_item.supplier_id != ' . \wws\Supplier\SuppliersConst::SUPPLIER_ID_EURAS;

        $group_by = '';

        $filter = $this->getForm()->getValues();

        if ($filter['grouped_by_order']) {
            $group_by = "GROUP BY orders.order_id";
        }

        $sql = "
            SELECT
                orders.order_id,
                orders.auftnr,
                supplier_order.supplier_order_id,
                supplier_order.bestell_nr,
                
                order_item.product_id,
                supplier_order_items.gros_product_id,
                order_item.product AS product,
        
                customers.firma,
                customers.name,
                customers.vorname,
        
                orders.added,
                order_item.estimated_delivery_date AS estimated_delivery_date,
                order_item.sped_id
            FROM
                order_item
            INNER JOIN
                orders ON (orders.order_id = order_item.order_id)
            INNER JOIN
                customers ON (orders.customer_id = customers.customer_id)
            LEFT JOIN
                supplier_order ON (supplier_order.order_id = orders.order_id)
            LEFT JOIN
                supplier_order_items ON (
                    supplier_order_items.supplier_order_id = supplier_order.supplier_order_id AND 
                    supplier_order_items.product_id = order_item.product_id
                )
            INNER JOIN
            (
                SELECT
                    order_item.order_id,
                    MAX(order_item.estimated_delivery_date) AS max_estimated_delivery_date
                FROM
                    order_item
                WHERE
                    order_item.shop_id = " . $this->shop_id . " AND
                    order_item.typ = '" . OrderConst::WARENKORB_TYP_PRODUKT . "' AND
                    (
                        (
                          order_item.status NOT IN (" . OrderConst::STATUS_AUFTRAGSANNAHME . ", " . OrderConst::STATUS_EBAY_ZAHLUNGSABWICKLUNG . ", " . OrderConst::STATUS_ZUSTELLUNG_BEENDET . ", " . OrderConst::STATUS_PARK . ", " . OrderConst::STATUS_STORNO . ") AND
                          order_item.quantity_open > 0
                        )
                        OR 
                        (
                          order_item.status IN (" . OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST . ", " . OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND . ")
                        )
                    ) AND
                    order_item.status < " . OrderConst::STATUS_STORNO . "
                GROUP BY
                    order_item.order_id
                HAVING
                    DATEDIFF(MAX(order_item.estimated_delivery_date), NOW()) <= 0
            ) AS overdue_orders
            ON order_item.order_id = overdue_orders.order_id
            WHERE
                orders.shop_id = {$this->shop_id} AND
                orders.order_aktiv = 1 AND
                orders.order_type != '" . OrderConst::ORDER_TYPE_OFFER . "' AND
                orders.zahlungs_status > " . OrderConst::PAYMENT_STATUS_LEGACY_0 . " AND 
                order_item.typ = '" . OrderConst::WARENKORB_TYP_PRODUKT . "' AND
                order_item.status NOT IN (" . OrderConst::STATUS_AUFTRAGSANNAHME . ", " . OrderConst::STATUS_EBAY_ZAHLUNGSABWICKLUNG . ", " . OrderConst::STATUS_ZUSTELLUNG_BEENDET . ", " . OrderConst::STATUS_PARK . ", " . OrderConst::STATUS_STORNO . ")
                $where
            ORDER BY
                order_item.estimated_delivery_date
                $group_by
        ";

        $table = new TableObjectPersistent($sql);
        $table->setExportEnabled(true);
        $table->setCaption(' ');
        $table->removeFieldByKey('orders.order_id');
        $table->removeFieldByKey('order_item.estimated_delivery_date');
        $table->removeFieldByKey('supplier_order.supplier_order_id');

        $table->getFieldByKey('supplier_order.bestell_nr')->setName('Lieferanten-Bestell-Nr.');

        $field = new table_object_field_date('estimated_delivery_date', 'Geplanter Liefertermin');
        $field->setOrderable(true);
        $table->addField($field);

        $field = new table_object_field_text('new_delivery_date', 'neuer Liefertermin');
        $table->addField($field);

        $table->removeFieldByKey('order_item.sped_id');

        if (($filter['grouped_by_order'])) {
            $table->removeFieldByKey('order_item.product_id');
            $table->removeFieldByKey('supplier_order_items.gros_product_id');
            $table->removeFieldByKey('product');
        }

        $field = new table_object_field_callback('sped_id', 'Spedition');
        $field->setCallback(function (array $row) {
            return ShipmentRepository::getSpedLogoAsHtml($row['sped_id']);
        });
        $table->addField($field);

        return $table;
    }
}
