<?php

namespace wws\core;

use ArrayObject;
use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Exceptions\FatalException;
use bqp\form\wws_form_element;
use bqp\Utils\StringUtils;
use config;
use JsonSerializable;
use order_repository;
use output;
use output_autocomplete;
use output_component;
use bqp\RequestHandler;
use bqp\table\TableObject;
use wws\buchhaltung\Invoice\InvoiceDocument;
use wws\Mailsystem\MailsystemMail;
use wws\MarketView\Entities\MarketViewMedia;
use wws\Order\OrderConst;
use wws\Order\OrderDocumentUtils;
use wws\Product\ProductConst;
use wws\Product\ProductRepositoryLegacy;
use wws\ProductStock\ProductStockConst;
use wws\Supplier\SupplierOrder;
use wws\Users\UserSession;

class SmartyWws extends \bqp\output\smarty_comb
{
    /**
     * @var ArrayObject
     */
    public $config;

    /**
     * @var null|string
     */
    private $active_main_menu_path;

    /**
     * @var Breadcrumb
     */
    protected $breadcrumb;

    public function __construct()
    {
        parent::__construct();

        $this->left_delimiter = '{{';
        $this->right_delimiter = '}}';
        $this->caching = false;

        $this->template_dir = config::system('root_dir') . 'web/wws/av/templates/';
        $this->compile_dir = config::system('root_dir') . 'temp/smarty_compile/';

        global $_user, $_info;

        $this->config = new ArrayObject();

        $this->assign('_user', $_user);
        $this->assign('_info', $_info);

        $this->config['title'] = 'NONAME';
        $this->config['subtitle'] = '';
        $this->config['show_title'] = true;
        $this->config['headers'] = '';
        $this->config['template'] = 'common/inner.tpl';
        $this->config['prototype'] = false;
        $this->config['datepicker'] = false;

        $system_config = \service_loader::getConfigRegistry()->get('system');

        $this->config['wws_theme'] = $system_config->getString('theme');
        if ($_user instanceof UserSession && $_user->getWwsTheme()) {
            $this->config['wws_theme'] = $_user->getWwsTheme();
        }
        $this->assign('wws_themes', $system_config->getArray('themes'));

        $this->active_main_menu_path = null;

        if (\env::isK11()) {
            $this->addRawHeader('<script>window.isK11=1;</script>');
        }

        $this->assign('is_window_display', false);

        //smarty wird "sicherer"... sinn? ich brauch eigentlich keine sandbox...
        //Die Alternative: https://www.smarty.net/docs/en/advanced.features.tpl#advanced.features.security
        $classes = [
            DateObj::class,
            SupplierOrder::class,
            Address::class,
            OrderConst::class,
            ProductConst::class,
            ProductStockConst::class,
            OrderDocumentUtils::class,
            InvoiceDocument::class,
            MailsystemMail::class,
            MarketViewMedia::class,
            ProductRepositoryLegacy::class,
            wws_form_element::class,
            order_repository::class,
            StringUtils::class,
        ];

        foreach ($classes as $class) {
             //das ist wieder dieser hickhack mit php namespaces... ::class hat nicht den führenden Backslash,
             //aber in Smarty müssen die Klassen mit führendem Backslash angesprochen werden
            $this->registerClass('\\' . $class, $class);
        }
    }

    public function setTemplate(string $page): void
    {
        $this->config['template'] = $page;
    }


    public function setBreadcrumb(Breadcrumb $breadcrumb): void
    {
        $this->breadcrumb = $breadcrumb;

        $this->assign('breadcrumb', $this->breadcrumb);
    }

    public function resetTemplate(): void
    {
        $this->config['template'] = 'common/inner.tpl';
    }

    public function addForm(\bqp\form\form_element $form, string $form_name = ''): void
    {
        if (!$form_name) {
            $form_name = $form->getId();
        }

        $this->assign($form_name, $form);

        $this->loadDatepicker();
        $this->addComponent(new output_autocomplete());

        $this->solveDependencies($form->getDependencies());
    }

    public function addTable(?TableObject $table, string $name = 'table'): void
    {
        if ($table === null) {
            $this->assign($name, '');
            return;
        }

        $this->assign($name, $table);
    }


    public function addComponent($name, $component = null): void
    {
        if ($name instanceof output_component) {
            $component = $name;
            $name = '';

            if (method_exists($component, 'getName')) {
                $name = $component->getName();
            }
        }

        $this->solveDependencies($component->getDependencies());

        $this->assign($name, $component);
    }

    public function solveDependencies(array $dependencies): void
    {
        $seen_dependencies = [];

        foreach ($dependencies as $dependency) {
            if ($dependency instanceof \bqp\output\output_dependency) {
                $typ = $dependency->getType();

                $key = $dependency->getKey();
                if ($key) {
                    if (isset($seen_dependencies[$key])) {
                        continue;
                    }

                    $seen_dependencies[$key] = true;
                }
            } elseif (is_array($dependency)) {
                $typ = $dependency['typ'];
            } else {
                $typ = $dependency;
            }

            switch ($typ) {
                case 'datepicker':
                    $this->loadDatepicker();
                    break;
                case 'mail_editor_css':
                    $this->addCss('/res/css/mail_editor.css');
                    break;
                case 'css':
                    $file = null;
                    if (isset($dependency['file'])) {
                        $file = $dependency['file'];
                    } elseif (isset($dependency['value'])) {
                        $file = $dependency['value'];
                    }

                    $this->addCss($file);
                    break;
                case 'js':
                    $file = null;
                    if (isset($dependency['file'])) {
                        $file = $dependency['file'];
                    } elseif (isset($dependency['value'])) {
                        $file = $dependency['value'];
                    }

                    $this->addJs($file);
                    break;
                case 'raw':
                    $code = null;

                    if (isset($dependency['code'])) {
                        $code = $dependency['file'];
                    } elseif (isset($dependency['value'])) {
                        $code = $dependency['value'];
                    }

                    $this->addRawHeader($code);
                    break;
            }
        }
    }

    public function stdDisplay(string $page = 'default.tpl'): void
    {
        $this->config['page'] = $this->searchTemplate($page);
        $this->assign('display_config', $this->config);
        $this->assign('_is_php_template', StringUtils::ends($page, '.php'));
        $this->display('common/standard.tpl');
    }

    public function windowDisplay(string $page = 'default.tpl'): void
    {
        $this->config['page'] = $this->searchTemplate($page);
        $this->assign('display_config', $this->config);
        $this->assign('is_window_display', true);

        $this->display('common/window.tpl');
    }

    public function helpDisplay(string $page): void
    {
        $this->config['page'] = $this->searchTemplate($page);
        $this->assign('display_config', $this->config);

        $this->display($page);
    }

    public function fullDisplay(string $page): void
    {
        $this->config['page'] = $this->searchTemplate($page);
        $this->assign('display_config', $this->config);

        $this->display('common/full.tpl');
    }

    /**
     * @depracated
     *
     * @param $template
     * @param false $filename
     * @throws \SmartyException
     */
    public function excelDisplay($template, $filename = false)
    {
        if (!$filename) {
            $filename = 'export.xls';
        }

        header("Content-type: application/vnd-ms-excel");
        header("Content-Disposition: attachment; filename=" . $filename);

        $this->display($template);
    }

    public function pdfDisplay(string $template, $filename = false)
    {
        $pdf = false;

        extract($this->getTemplateVars());
        include($this->template_dir . '' . $template);

        if (!$pdf) {
            throw new FatalException('$pdf ist nicht gesetzt');
        }

        if ($filename) {
            $pdf->Output($filename, 'F');
        } else {
            $pdf->Output();
        }
    }

    public function pdfFetch(string $template): string
    {
        $pdf = false;

        extract($this->getTemplateVars());
        include($this->template_dir . '' . $template);

        if (!$pdf) {
            throw new FatalException('$pdf ist nicht gesetzt');
        }

        return $pdf;
    }

    /**
     * Gibt das übergebenen Array/Objekt als Json String aus
     *
     * @param array|JsonSerializable $json
     */
    public function jsonDisplay(array|JsonSerializable $json): void
    {
        header('Content-Type: application/json');
        echo json_encode($json);
    }

    public function dokumentDisplay(string $template, ?bool $wkhtmltopdf = null): void
    {
        $this->config['page'] = $this->searchTemplate($template);
        $this->assign('display_config', $this->config);

        if ($wkhtmltopdf !== null) {
            $this->assign('wkhtmltopdf', $wkhtmltopdf);
        } else {
            $this->assign('wkhtmltopdf', isset($_REQUEST['wkhtmltopdf']));
        }

        $auto_print = isset($_REQUEST['auto_print']) ? $_REQUEST['auto_print'] : '';

        $this->assign('auto_print', $auto_print);

        $this->display('common/dokument.tpl');
    }

    public function dokumentDisplayContent($content)
    {
        $this->config['page_content'] = $content;
        $this->assign('display_config', $this->config);

        $this->assign('wkhtmltopdf', isset($_REQUEST['wkhtmltopdf']));

        $autoprint = isset($_REQUEST['auto_print']) ? $_REQUEST['auto_print'] : '';

        $this->assign('auto_print', $autoprint);

        $this->display('common/dokument.tpl');
    }

    public function dokumentFetch($template)
    {
        $this->config['page'] = $this->searchTemplate($template);
        $this->assign('display_config', $this->config);

        return $this->fetch('common/dokument.tpl');
    }

    public function dokumentFetchForWkhtmlToPdf($template)
    {
        $this->assign('wkhtmltopdf', true);
        return $this->dokumentFetch($template);
    }


    public function smartDisplay(string $page = 'default.tpl'): void
    {
        $request = new RequestHandler();

        $this->config['page'] = $this->searchTemplate($page);

        $main_template = 'common/main_template_default.tpl';

        $this->assign('display_config', $this->config);
        $this->assign('_is_php_template', StringUtils::ends($page, '.php'));

        if ($request->isAjax()) {
            if (strpos($this->config['page'], '/iframe.tpl')) {
                $this->display($this->config['page']);
            } else {
                if (isset($this->config['headers'])) {
                    echo $this->config['headers'];
                }

                $this->config['inline_page'] = $this->config['page'];

                $this->display($main_template);
            }
        } else {
            if (!strpos($this->config['page'], '/iframe.tpl')) {
                $this->config['inline_page'] = $this->config['page'];
            }

            $this->assign('main_template', $main_template);

            $menu = new \wws\core\WwsMainMenu();

            $menu->initTpl($this);

            if ($this->active_main_menu_path) {
                $menu->setMenuItemActiveByPath($this->active_main_menu_path);
            }

            $this->assign('display_config', $this->config);

            $this->display('main_full.tpl');
        }
    }


    protected function searchTemplate($template)
    {
        $path = \bqp\runtime::getCallingControllerPath();

        if (file_exists($path . '/templates/' . $template)) {
            $template = $path . '/templates/' . $template;
        }

        return $template;
    }


    public function popup($url)
    {
        $code = <<<EOD
<script type="text/javascript">
window.open('$url');
</script>
EOD;

        $this->addRawHeader($code);
    }


    /*===========================================================*/
    // Einstellungen
    /*===========================================================*/
    public function setTitle($title, $subtitle = '')
    {
        $this->config['title'] = $title;
        $this->config['subtitle'] = $subtitle;
    }

    public function setSubtitle($subtitle)
    {
        $this->config['subtitle'] = $subtitle;
    }

    public function addTitle($title)
    {
        $this->config['title'] .= $title;
    }

    public function getTitle()
    {
        return $this->config['title'];
    }

    public function showTitle(bool $status): void
    {
        $this->config['show_title'] = $status;
    }

    public function addCss(string $file): void
    {
        $this->config['headers'] .= '<link rel="stylesheet" type="text/css" href="' . output::revUrlRessource($file) . '" />';
    }

    public function addJs(string $file): void
    {
        if (strpos($this->config['headers'], $file) !== false) { //deduplikation
            return;
        }

        $this->config['headers'] .= '<script type="text/javascript" src="' . output::revUrlRessource($file) . '"></script>';
    }

    /**
     * Hilfsfunktion um ein Javsscript File zu laden. Stellt sicher dass dieses Javascript nur einmal geladen wird. (ajax calls)
     * imperative Scripte sind damit nicht möglich. Einstiegspunkt und Event Handler zu registrieren ist der
     * "DOMContentLoadedWws" Event.
     * Achtung: der Event wird dann für alle weiteren aufrufe per smartDisplay() getriggert. Es muss also ggf. geprüft werden, ob
     * das Script überhaupt für den jeweiligen Aufruf relavant ist.
     *
     * @param string $file
     */
    public function addJsOnce(string $file): void
    {
        $key = preg_replace('~[^a-z0-9]~iu', '', strtolower($file));

        $this->config['headers'] .= '<script>if (!window.' . $key . ') {
            window.' . $key . ' = document.createElement("script");
            window.' . $key . '.setAttribute("src","' . $file . '");
            document.head.appendChild(window.' . $key . ');
        }</script>';
    }

    public function addRawHeader($header)
    {
        $this->config['headers'] .= $header;
    }

    public function loadPrototyp($version = false)
    {
        static $loaded;
        if ($loaded) {
            return;
        }

        switch ($version) {
            case '1.6':
                $this->addJs('/res/js/prototype1.7.js');
                break;
            default:
                $this->config['prototype'] = true;
        }

        $loaded = true;
    }

    public function loadDatepicker()
    {
        $this->config['datepicker'] = true;
    }

    public function addJsArray($name, $array)
    {
        $return = '<script type="text/javascript">';
        $return .= 'var ' . $name . ' = ' . json_encode($array);
        $return .= '</script>';
        $this->addRawHeader($return);
    }

    public function addJsValue($name, $value): void
    {
        if (!is_numeric($value)) {
            $value = '"' . str_replace('"', '\"', $value) . '"';
        }

        $return = '<script type="text/javascript">';
        $return .= 'var ' . $name . ' = ' . $value;
        $return .= '</script>';
        $this->addRawHeader($return);
    }

    /**
     * Lightbox einbinden
     * img rel="lightbox[id]"
     */
    public function loadLightbox()
    {
        $this->loadPrototyp('1.6');
        $this->addJs('/res/js/scriptaculous-js-1.8.3/src/scriptaculous.js?load=effects,builder');
        $this->addJs('/res/js/lightbox2.04/js/lightbox.js');

        $this->addCss('/res/js/lightbox2.04/css/lightbox.css');
    }

    public function loadExtJs($version = '3.0-rc2')
    {
        switch ($version) {
            case '3.0':
            case '3.0-rc2':
                $this->addCss('/res/js/ext-3.0-rc2/resources/css/ext-all.css');
                $this->loadPrototyp();
                //$this->config['headers'] .= '<script type="text/javascript" src="/res/js/ext-3.0-rc2/adapter/prototype/ext-prototype-adapter.js"></script>';
                $this->config['headers'] .= '<script type="text/javascript" src="/res/js/ext-3.0-rc2/adapter/ext/ext-base.js"></script>';
                //$this->config['headers'] .= '<script type="text/javascript" src="/res/js/ext-3.0-rc2/ext-all.js"></script>';
                $this->config['headers'] .= '<script type="text/javascript" src="/res/js/ext-3.0-rc2/ext-all-debug.js"></script>';

                break;
        }
    }

    public function loadExtComponent($component)
    {
        switch ($component) {
            case 'Ext.ux.grid.GridSummary':
                $this->addCss('/res/js/ext-3.0-rc2/ux/Ext.ux.grid.GridSummary/Ext.ux.grid.GridSummary.css');
                $this->addJs('/res/js/ext-3.0-rc2/ux/Ext.ux.grid.GridSummary/Ext.ux.grid.GridSummary.js');
                break;
            case 'Ext.ux.form.StaticTextField':
                $this->addCss('/res/js/ext-3.0-rc2/ux/Ext.ux.form.StaticTextField/Ext.ux.form.StaticTextField.css');
                $this->addJs('/res/js/ext-3.0-rc2/ux/Ext.ux.form.StaticTextField/Ext.ux.form.StaticTextField.js');
                break;
        }
    }

    public function loadJs($component)
    {
        //
        $temp = explode('.', $component);
        unset($temp[0]);

        $dir = '/res/js/';

        foreach ($temp as $name) {
            if (is_dir(config::system('root_dir') . $dir . $name . '/')) {
                $dir = $dir . $name . '/';
            } else {
                break;
            }
        }

        $this->addJs($dir . $component . '.js');
    }

    public function setMainMenuActiveByPath(string $path): void
    {
        $this->active_main_menu_path = $path;
    }

    /**
     * fügt den contant container eine css klasse hinzu. Z.B. um ein
     * @param string $class
     */
    public function addContentClass(string $class): void
    {
        $this->assign('content_class', $class);
    }
}
