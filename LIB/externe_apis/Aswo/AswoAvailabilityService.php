<?php

namespace bqp\extern\Aswo;

use bqp\table\DataSource\TableObjectDataSourceArrayHeaders;
use bqp\table\TableObjectInline;
use bqp\Vat\VatRateConst;
use bqp\Vat\VatRateSimple;
use K11\AswoEedClient\ClientService as AswoEedClient;
use K11\AswoEedClient\Entities\ClientConfig;
use K11\AswoEedClient\NullCache;
use output;
use service_loader;
use wws\Supplier\SupplierAvailabilityService;
use wws\Supplier\SupplierRepository;

class AswoAvailabilityService extends SupplierAvailabilityService
{
    private AswoEedClient $aswo_eed_client;

    public function __construct(int $supplier_id)
    {
        parent::__construct($supplier_id);

        $supplier_repo = service_loader::get(SupplierRepository::class);
        $config_key = $supplier_repo->getOrderApiConfigKey($supplier_id);
        $config_container = service_loader::getConfigRegistry()->get($config_key);

        $aswo_vat_rate = (new VatRateSimple(VatRateConst::UST_19_INT))->getAsFactor();

        $config = new ClientConfig(
            $config_container->getString('eed_id'),
            'https://shop.euras.com/eed.php',
            'https://shop.euras.com/thumb.php?eed=1&artnr=%s&resize=%s',
            new NullCache(),
            $aswo_vat_rate,
            null,
            'https://wws.ecom-dresden.de/'
        );

        $this->aswo_eed_client = new AswoEedClient($config, null);
    }

    public function getLink(string $gros_product_id): string
    {
        return '';
    }

    public function getForm(string $gros_product_id): string
    {
        return '';
    }

    public function getLoginForLink(string $gros_product_id): array
    {
        return [];
    }

    public function handleFormRequest(string $gros_product_id, array $daten): string
    {
        try {
            $article = $this->aswo_eed_client->getArticleGateway()->getArticle($gros_product_id);
        } catch (\Throwable $e) {
            return 'Bei der Anfrage ist ein Fehler aufgetreten. (' . $e::class . ': ' . $e->getMessage() . ')';
        }

        $data = [
            [
                'Lieferzeit in Tagen' => $article->getAvailability(),
                'Bestellbar' => $article->isOrderable() ? 'Ja' : 'Nein',
                'EK (Brutto)' => output::formatPrice($article->getGrossPurchasePrice()),
            ]
        ];

        $src = new TableObjectDataSourceArrayHeaders($data);
        $table = new TableObjectInline($src);
        return $table->render();
    }
}
