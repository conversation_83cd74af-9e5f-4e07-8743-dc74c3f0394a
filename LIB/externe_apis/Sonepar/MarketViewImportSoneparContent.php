<?php

namespace bqp\extern\Sonepar;

use bqp\Csv\CsvReader;
use bqp\Csv\TableReader;
use bqp\Csv\TableReaderCsvSource;
use bqp\Date\DateObj;
use bqp\Date\DateRangeFullDays;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Flysystem\FlysystemUtils;
use bqp\Utils\FileUtils;
use Exception;
use input;
use Laminas\Http\Client;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Entities\MarketViewProductRelation;
use wws\MarketView\Entities\MarketViewSonstiges;
use wws\MarketView\Import\MarketViewImportProduct;
use wws\MarketView\Import\MarketViewImportSimple;
use wws\MarketView\Import\MarketViewImportUtils;
use wws\MarketView\MarketViewConst;
use wws\MarketView\Utils\MarketViewCsvOffsizeErrorHandler;

class MarketViewImportSoneparContent extends MarketViewImportSimple
{
    private bool $dpd_shipping_surcharge = false;

    public function init(): void
    {
        $date_range = new DateRangeFullDays(new DateObj('2025-11-01 00:00:00'), new DateObj('2025-12-24 00:00:00'));

        if ($date_range->isInRange(new DateObj())) {
            $this->dpd_shipping_surcharge = true;
        }
    }

    public function updateContent(): void
    {
        $dst_file = $this->config['import_dir'] . '/deg_product_data.csv';

        try {
            if (!empty($this->config['easy_content_url'])) {
                $this->loadEasyContent($this->config['easy_content_url'], $dst_file);
            } else {
                $this->loadEasyContentViaFtp('12_649_XZXF.csv', $dst_file);
            }
        } catch (Exception $e) {
            $this->protokoll->wfail('Content-Daten konnte nicht geladen werden.', $e->getMessage());
            return;
        }

        $this->parseContent($dst_file);
        $this->archiveContent($dst_file);

        $this->protokoll->wok('Content-Daten erfolgreich eingelesen', $this->list_logger->getSummaryAsText());

        parent::updateComplete();
    }

    public function updateShippingCosts(): void
    {
        $file = $this->config['import_dir'] . '/deg_shipping.csv';

        if (file_exists($file)) {
            unlink($file);
        }

        $this->loadEasyContent($this->config['easy_content_url'], $file);

        $csv = new CsvReader($file);

        $csv->skipOffSizeLines(false);
        $csv->setDelimiter("\t");
        $csv->setSrcCharset('ISO-8859-1');
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);
        $csv->skipFirstLines(1);

        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string', 'checkHeader' => 'ARTNR'],
            'NONE', //ean' => array('typ' => 'string', 'checkHeader' => 'EAN'),
            'NONE', //'sperrgut' => array('typ' => 'string', 'checkHeader' => 'SPERRGUT'),
            'sonstiges_LogistikDaten' => ['typ' => 'string', 'checkHeader' => 'LogistikDaten'],
            'versand_classification' => ['typ' => 'string', 'checkHeader' => 'Versandklasse']
        ]);

        //die angebotsdaten sind nicht kohörent. Lars hat sich jetzt an einen Beispiel gestört, da gabs ein Preis und Bestand,
        //aber die Versandkosten in MarketView waren "veraltet". Keine Lust jetzt über den sinn oder unsinn zu diskutieren... ->
        //wir exen die Daten raus, die nicht teil der Datei sind. (in der Content Version haben wir in der theorie die Daten nochmal.)
        $existing_offers_with_data = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                1
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id ." AND
                market_view_product.versand_classification IS NOT NULL 
        ")->asSingleArray('mvsrc_product_id');

        foreach ($csv as $row) {
            unset($existing_offers_with_data[$row['mvsrc_product_id']]);

            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($row['mvsrc_product_id']);

            $result = $this->shippingClassificationToShippingCost($row['versand_classification']);

            $product->setVersandNetto($result['versand_netto']);
            $product->setVersandClassification($result['versand_classification']);

            if ($row['sonstiges_LogistikDaten']) {
                $result = $this->parseLogsticDataForMarketView($row['sonstiges_LogistikDaten']);

                $product->setGewichtKg($result['gewicht']);
                $product->setHoeheMm($result['hoehe_mm']);
                $product->setBreiteMm($result['breite_mm']);
                $product->setTiefeMm($result['tiefe_mm']);
            }

            $this->market_view_import->updateProduct($product);
        }

        foreach ($existing_offers_with_data as $mvsrc_product_id => $null) {
            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($mvsrc_product_id);
            $product->setVersandClassification(null);
            $product->setVersandNetto(null);

            $this->market_view_import->updateProduct($product);
        }

        $dst_file = FileUtils::appendDir($file, 'last');
        MarketViewImportUtils::moveFileCompressed($file, $dst_file);
    }

    public function updateShippingCostsWithLocalClassification(): void
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_product.mvsrc_product_id,
                market_view_product.versand_classification,
                market_view_product.versand_netto
            FROM
                market_view_product
            WHERE
                market_view_product.mvsrc_id = " . $this->mvsrc_id . " AND
                market_view_product.versand_classification != ''
        ");

        foreach ($result as $row) {
            $versand_result = $this->shippingClassificationToShippingCost($row['versand_classification']);

            if (abs($versand_result['versand_netto'] - $row['versand_netto']) < 0.001) {
                continue;
            }

            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($row['mvsrc_product_id']);
            $product->setVersandNetto($versand_result['versand_netto']);
            $this->market_view_import->updateProduct($product);
        }
    }


    public function loadEasyContent(string $url, string $dst_file): void
    {
        $client = new Client(null, [
            'timeout' => 600,
            'adapter' => Client\Adapter\Curl::class //mit den default socket adapter bricht der download mittlerweile zu 99% irgendwo in der mitte ab...
        ]);

        $client->setUri($url);

        $client->setStream($dst_file); //gzopen(..., 'wb9') erzeugt nur eine leere Datei
        $result = $client->send();

        if ($result->getStatusCode() !== $result::STATUS_CODE_200) {
            throw new FatalException('Download nicht erfolgreich. Status-Code != 200 (' . $result->getStatusCode() . ')');
        }

        $is_file_size = filesize($dst_file);
        $expect_file_size = $result->getHeaders()->get('Content-Length')->getFieldValue();

        if ($is_file_size != $expect_file_size) {
            throw new FatalException('Download nicht erfolgreich. Ist/Soll-Dateigröße abweichend. SOLL: ' . $expect_file_size . ' IST: ' . $is_file_size);
        }
    }

    public function loadEasyContentViaFtp(string $src_filename, string $dst_file): void
    {
        $source = $this->config['easy_content_source'];

        $ftp = FlysystemUtils::createFilesystemFromSource($source);

        if (!$ftp->has($src_filename)) {
            throw new FatalException('Datei ' . $src_filename . ' nicht gefunden');
        }

        if (file_exists($dst_file)) {
            unlink($dst_file);
        }

        $dst_stream = fopen($dst_file, 'wb+');

        $src_stream = $ftp->readStream($src_filename);

        stream_copy_to_stream($src_stream, $dst_stream);

        fclose($dst_stream);

        $ftp->delete($src_filename);
    }


    private function archiveContent(string $file): void
    {
        //Nur die letzte Datei aufbewahren. Das sind mittlerweile 1,2GB
        $dst_file = FileUtils::appendDir($file, 'last');

        if (file_exists($dst_file)) {
            unlink($dst_file);
        }

        MarketViewImportUtils::moveFileCompressed($file, $dst_file);
    }


    public function parseContent(string $file): void
    {
        $source = new TableReaderCsvSource($file, 'ISO-8859-1');
        $source->setDelimiter("\t");
        $source->setMaxLineSize(1024 * 64);

        $csv = new TableReader($source);
        $csv->setSkipOffSizeLineHandler(new MarketViewCsvOffsizeErrorHandler(10));
        $csv->setDstCharset($this->config['charset']);
        $csv->setTrim(true);

        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string', 'checkHeader' => 'ARTNR'],
            'hersteller_name' => ['typ' => 'string', 'checkHeader' => 'HERSTELLER'],
            'product_name_1' => ['typ' => 'string', 'checkHeader' => 'BEZ'],
            'product_name_2' => ['typ' => 'string', 'checkHeader' => 'TYPENBEZ'],
            'mpn' => ['typ' => 'string', 'checkHeader' => 'H_ARTNR'],
            'ean' => ['typ' => 'string', 'checkHeader' => 'EAN'],
            'beschreibung' => ['typ' => 'string', 'checkHeader' => 'LANGTEXT'],
            'features' => ['typ' => 'string', 'checkHeader' => 'MERKMALE'],
            'sonstiges_Artklasse' => ['typ' => 'string', 'checkHeader' => 'ARTKLASSE'],
            'image_1' => ['typ' => 'string', 'checkHeader' => 'BILDURL'],
            'image_aehnlich' => ['typ' => 'string', 'checkHeader' => 'AEHNLICH'],
            'cat_name_2' => ['typ' => 'string', 'checkHeader' => 'Produktkategorie'],
            'NONE',    //'mv_availability' => array('typ' => 'string', 'checkHeader' => 'Lieferstatus'),
            'sperrgut' => ['typ' => 'string', 'checkHeader' => 'SPERRGUT'],
            'cat_name_hwg' => ['typ' => 'string', 'checkHeader' => 'HWG'],
            'cat_name_wg' => ['typ' => 'string', 'checkHeader' => 'WG'],
            'sonstiges_Markenname' => ['typ' => 'string', 'checkHeader' => 'MARKENNAME'],
            'sonstiges_Bez2' => ['typ' => 'string', 'checkHeader' => 'BEZ2'],
            'sonstiges_Herstellerprogramm' => ['typ' => 'string', 'checkHeader' => 'HERSTELLERPROGRAMM'],
            'deg_relationen' => ['typ' => 'string', 'checkHeader' => 'ARTIKLRELATIONEN'],
            'NONE', //'reachkennzeichen' => array('typ' => 'string', 'checkHeader' => 'ReachKennzeichen'),
            'NONE', //'zus_bilder' => array('typ' => 'string', 'checkHeader' => 'ZUS_BILDER'),
            // -> die Bilder sind in der Form nur ganz schwer verwertbar. Das ist "Bildname=Url\n" usw. DAs Problem
            //    ist die Bildnamen sind nicht eindeutig und die Urls auch nicht. Position scheint nicht sinnvoll.
            'NONE', //'zus_dokumente' => array('typ' => 'string', 'checkHeader' => 'ZUS_DOKUMENTE'),
            'sonstiges_Minbez' => ['typ' => 'string', 'checkHeader' => 'MINBEZ'],
            'beschreibung_2' => ['typ' => 'string', 'checkHeader' => 'ENVKV_TEXT'],
            'sonstiges_KennzAbakus' => ['typ' => 'string', 'checkHeader' => 'KENNZ_ABAKUS'],
            'zolltarifnummer' => ['typ' => 'string', 'checkHeader' => 'ZOLLTARIFNR'],
            'sonstiges_LogistikDaten' => ['typ' => 'string', 'checkHeader' => 'LogistikDaten'],
            'okopdf' => ['typ' => 'string', 'checkHeader' => 'OEKOD_PDF'],
            'NONE', //'ursprungsland' => array('typ' => 'string', 'checkHeader' => 'URSPRUNGSLAND'),
            'energielabel_image' => ['typ' => 'string', 'checkHeader' => 'Energielabel'],
            'NONE', //'schnittkennzeichen' => array('typ' => 'string', 'checkHeader' => 'Schnittkennzeichen'),
            'sonstiges_Gefahrgutkennzeichen' => ['typ' => 'string', 'checkHeader' => 'Gefahrgutkennzeichen'],
            'NONE', //'sicherheitsdatenblatt' => array('typ' => 'string', 'checkHeader' => 'Sicherheitsdatenblatt'),
            'sonstiges_IE_PRO_BE' => ['typ' => 'string', 'checkHeader' => 'IE_PRO_BE'],
            'versand_classification' => ['typ' => 'string', 'checkHeader' => 'Versandklasse']
        ]);

        $this->fullUpdatePrepare();

        foreach ($csv as $daten) {
            $daten['product_name'] = $daten['product_name_1'] . ' ' . $daten['product_name_2'];

            $daten['mv_cat_id'] = $this->saveCategory(MarketViewConst::TREE_ID_DEFAULT, [$daten['cat_name_hwg'], $daten['cat_name_wg']]);
            $daten['mv_cat_id_2'] = $this->saveCategory(2, explode('>', html_entity_decode($daten['cat_name_2'])));
            $daten['mv_hersteller_id'] = $this->saveMvHersteller($daten['hersteller_name']);

            $daten['images'] = [];
            if ($daten['image_1']) {
                $media = new MarketViewMedia();
                $media->setUrl($daten['image_1']);
                $media->setMediaType(MarketViewMedia::MEDIA_TYPE_IMAGE_URL);
                $media->setMediaKey('img1_' . $daten['mvsrc_product_id']);

                $daten['images'][] = $media;
            }

            if ($daten['energielabel_image']) {
                $media = new MarketViewMedia();
                $media->setUrl($daten['energielabel_image']);
                $media->setMediaType(MarketViewMedia::MEDIA_TYPE_IMAGE_URL);
                $media->setTopic(MarketViewMedia::MEDIA_TOPIC_ENERGIE_LABEL);
                $media->setMediaKey('el_' . $daten['mvsrc_product_id']);

                $daten['images'][] = $media;
            }

            if ($daten['okopdf']) {
                $media = new MarketViewMedia();
                $media->setUrl($daten['okopdf']);
                $media->setMediaType(MarketViewMedia::MEDIA_TYPE_PDF);
                $media->setTopic(MarketViewMedia::MEDIA_TOPIC_EU_DATENBLATT);
                $media->setMediaKey('oko_' . $daten['mvsrc_product_id']);

                $daten['images'][] = $media;
            }

            $daten['content_flag'] = 1;

            $features = $this->convertFeatureStringToFeatureObject($daten['features']);

            if ($features->isFeatures()) {
                $daten['features_struct'] = $features;
            }

            $daten['default_availability_id'] = MarketViewConst::AVAILABILITY_ID_UNKNOWN;

            if ($daten['sonstiges_LogistikDaten']) {
                $parsed = $this->parseLogsticDataForMarketView($daten['sonstiges_LogistikDaten']);
                $daten = array_merge($daten, $parsed);
            }

            $sonstiges = new MarketViewSonstiges();
            $sonstiges->extractFromArray($daten);

            $daten['sonstiges_struct'] = $sonstiges;
            $daten['sonstiges'] = '';

            $daten['vk_netto_info'] = '';
            if ($daten['sonstiges_KennzAbakus'] === 'J') {
                //$daten['vk_netto_info'] = 'Abakus'; //wir haben kein Abakus mehr über Sonepar. Damit das nicht zu irgendwelchen lustigen Problemen führt, hier nicht mehr setzen.
            }

            $result = $this->shippingClassificationToShippingCost($daten['versand_classification']);
            $daten['versand_netto'] = $result['versand_netto'];
            $daten['versand_classification'] = $result['versand_classification'];

            $this->saveProduct($daten);

            $this->saveProductRelations($daten['mvsrc_product_id'], $daten['deg_relationen']);

            foreach ($daten['images'] as $media) {
                $media->setMvsrcProductId($daten['mvsrc_product_id']);
                $this->addMarketViewMedia($media);
            }
        }

        $this->fullUpdateEnd();
    }


    public function convertFeatureStringToFeatureObject(string $features): MarketViewFeatures
    {
        $features = str_replace(['<br />', '<br/>'], '<br>', $features);
        $feature_lines = explode('<br>', $features);

        $feature_obj = new MarketViewFeatures();

        foreach ($feature_lines as $string) {
            $pos = strpos($string, '=');
            $part1 = substr($string, 0, $pos);
            $part2 = substr($string, $pos + 1);

            $feature_obj->addFeature($part1, $part2);
        }

        return $feature_obj;
    }


    private function saveProductRelations(string $mvsrc_product_id, string $raw_relations): void
    {
        if (!$raw_relations) {
            return;
        }

        $raw_relations = str_replace(['<br />', '<br/>'], '<br>', $raw_relations);
        $raw_relation_lines = explode('<br>', $raw_relations);

        $relations = new MarketViewProductRelation();

        $cross_sell = [];

        foreach ($raw_relation_lines as $raw_relation) {
            $pos = strpos($raw_relation, '=');
            $raw_type = substr($raw_relation, 0, $pos);
            $related_mvsrc_product_id = substr($raw_relation, $pos + 1);

            switch ($raw_type) {
                case 'Zubehör':
                    $type = $relations::TYPE_CROSS_SELL;
                    break;
                case 'Geeignet für':
                    $type = $relations::TYPE_PROPER_FOR;
                    break;
                case 'Nachfolger':
                    $type = $relations::TYPE_REPLACED_BY;
                    break;
                case 'Alternativ':
                    $type = $relations::TYPE_ALTERNATIVE;
                    break;
                case '':
                    continue 2;
                default:
                    throw new DevException('unknown relation type (' . $raw_type . ')');
                //$type = $relations::TYPE_UNKNOWN;
            }

            $relations->add($type, $related_mvsrc_product_id);

            if ($raw_type === 'Zubehör') {
                $cross_sell[] = $related_mvsrc_product_id;
            }
        }

        $this->saveProductRelation($mvsrc_product_id, $relations);

        if ($cross_sell) {
            $this->saveCrossSelling($mvsrc_product_id, $cross_sell);
        }
    }


    /**
     * Extrahier aus den DEG Logistikdaten die für MarketView relevanten Daten.
     * ->es wird nur die erste VPE verwendet.
     *
     * @param string $logistic_data_raw
     * @return array
     */
    private function parseLogsticDataForMarketView(string $logistic_data_raw): array
    {
        $logistic_data_raw = str_replace(['<br>', '<br/>', '<br />'], '<br>', $logistic_data_raw);
        $lines = explode('<br>', $logistic_data_raw);

        $items = [];
        $items_raw = explode(';', $lines[0]);

        foreach ($items_raw as $item_raw) {
            list($key, $value) = explode('=', $item_raw);
            switch ($key) {
                case 'VPMENGE':
                case 'LAENGE':
                case 'BREITE_DURCHM':
                case 'HOEHE':
                    $value = (int)$value;
                    break;
                case 'BGEW':
                case 'NGEW':
                    $value = input::parseFloat($value);
                    break;
            }

            $items[$key] = $value;
        }

        return [
            'breite_mm' => $items['BREITE_DURCHM'],
            'hoehe_mm' => $items['HOEHE'],
            'tiefe_mm' => $items['LAENGE'],
            'gewicht' => $items['BGEW'] ?: $items['NGEW']
        ];
    }

    /**
     * @param string $versand_classification
     * @return array{
     *      versand_netto: ?float,
     *      versand_classification: ?string
     * }
     * @throws FatalException
     */
    private function shippingClassificationToShippingCost(string $versand_classification): array
    {
        // ACHTUNG -> bei anpassung sollte danach crons/ecom/marketview/read_sonepar_shipping_update_local.php ausgeführt werden
        // Bei Sonepar kommen Preise, Bestände und Versandkosten aus unterschiedlichen Quellen und die sind teilweise untereinander inkonsistent.
        // Wir bekommen dann zwar für bestimmte Angebote Preise und Bestände, aber dann keine Versandkosten mehr...

//            10 = paketfähigen Produkten: Paketmaß max. 120 cm x 60 cm x 60 cm, max 31,5 kg
//            20 = Sperrgut-Paket: Paketmaß größer 120 cm x 60 cm x 60 cm, max 31,5 kg
//            30 = Speditionsware: Abmessungen max. 240x180x220 cm (LxBxH), größer 31,5 kg oder von den Abmessungen nicht mehr als Paket zu versenden
//            40 = Speditionsware Großraum 1: ab 2,41m bis 6,00 m Länge und maximal 30 kg je Packstück, max. 600 x 40 x 40 cm (LxBxH)
//            50 = Speditionsware Großraum 2: Langgut Typ 2 – ab 2,41 m Länge und über 30 kg je Packstück
        $result = [
            'versand_netto' => null,
            'versand_classification' => null
        ];

        //wir bekommen von Sonepar die numerische Klassifikation -> da ich aber die Klassifikationen aus der DB u.U. auch nochmal durch die Funktion schicken muss,
        //gibt es hier zusätzliche die Pürfung auf die Strings.
        switch ($versand_classification) {
            case '10':
            case 'paketfähigen Produkten':
                $result['versand_classification'] = 'paketfähigen Produkten';
                $result['versand_netto'] = 5.79;

//                if ($this->dpd_shipping_surcharge) {
//                    $result['versand_netto'] = 5.70;
//                }
                break;
            case '20':
            case 'Sperrgut-Paket':
                $result['versand_classification'] = 'Sperrgut-Paket';
                $result['versand_netto'] = 5.79 + 20; //dpd
                break;
            case '30':
            case 'Speditionsware':
                //ACHTUNG!!!
                //\wws\Product\Ecom\ProductSoneparDropshippingManipulation::manipulateDropshipping() muss mit angepasst werden

                $result['versand_classification'] = 'Speditionsware';
                $result['versand_netto'] = 51;
                break;
            case '40':
            case 'Speditionsware Großraum 1':
                //ACHTUNG siehe    Speditionsware
                $result['versand_classification'] = 'Speditionsware Großraum 1';
                $result['versand_netto'] = 80 + 51;
                break;
            case '50':
            case 'Speditionsware Großraum 2':
                //ACHTUNG siehe    Speditionsware
                $result['versand_classification'] = 'Speditionsware Großraum 2';
                $result['versand_netto'] = 250;
                break;
            case '0':
            case 'Unbekannt':
                $result['versand_classification'] = 'Unbekannt';
                break;
            default:
                throw new FatalException('unknown versand classification (' . $result['versand_classification'] . ')');
        }

        return $result;
    }
}
