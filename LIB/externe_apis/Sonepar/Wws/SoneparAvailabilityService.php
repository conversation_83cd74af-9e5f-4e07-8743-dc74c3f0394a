<?php

namespace bqp\extern\Sonepar\Wws;

use bqp\Config\ConfigContainer;
use bqp\extern\Sonepar\SoneparPriceInventoryWebservice;
use bqp\table\DataSource\TableObjectDataSourceArrayHeaders;
use bqp\table\TableObjectInline;
use wws\Supplier\SupplierAvailabilityService;

class SoneparAvailabilityService extends SupplierAvailabilityService
{
    private SoneparPriceInventoryWebservice $sonepar_inventory_webservice;

    public function __construct(int $supplier_id, ConfigContainer $config)
    {
        $this->sonepar_inventory_webservice = new SoneparPriceInventoryWebservice($config);
        parent::__construct($supplier_id);
    }

    public function handleFormRequest(string $gros_product_id, array $daten): string
    {
        $data = $this->sonepar_inventory_webservice->getDetails([$gros_product_id]);

        $src = new TableObjectDataSourceArrayHeaders($data);

        $table = new TableObjectInline($src);

        return $table->render();
    }

    public function getForm(string $gros_product_id): string
    {
        return '';
    }

    public function getLoginForLink(string $gros_product_id): bool
    {
        return false;
    }

    public function getLink(string $gros_product_id): bool
    {
        return false;
    }
}
