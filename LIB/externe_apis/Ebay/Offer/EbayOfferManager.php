<?php

namespace bqp\extern\Ebay\Offer;

use bqp\Config\ConfigContainer;
use bqp\db\db_generic;
use bqp\Exceptions\FatalException;
use bqp\extern\Ebay\EbayClient;
use bqp\extern\Ebay\EbayStockCalculator;
use bqp\extern\Ebay\Event\EbayCatChangeByEbay;
use bqp\extern\Ebay\Exception\EbayExceptionApi;
use bqp\extern\Ebay\Exception\EbayExceptionInvalidData;
use bqp\extern\Ebay\Exception\EbayProductNotFoundException;
use bqp\table\DataSource\TableObjectDataSourceArrayHeaders;
use bqp\table\Renderer\TableObjectRendererPlainText;
use bqp\table\TableObject;
use debug;
use SoapFault;
use wws\Product\Ebay\ProductEbayErrorService;
use wws\Product\ProductPriceGroupRepository;

class EbayOfferManager
{
    protected bool $do_add = true;
    protected bool $do_update = true;
    protected bool $do_delete = true;

    private EbayClient $ebay_client;
    private ConfigContainer $config;
    private db_generic $db;
    private EbayProductCreator $product_creator;
    protected ProductEbayErrorService $product_ebay_error_service;
    protected EbayOfferCache $ebay_offer_cache;
    private EbayStockCalculator $ebay_stock_calculator;

    public function __construct(
        ConfigContainer $config,
        EbayClient $ebay_client,
        db_generic $db,
        ProductEbayErrorService $product_ebay_error_service,
        EbayProductCreator $product_creator,
        EbayStockCalculator $ebay_stock_calculator
    ) {
        $this->db = $db;
        $this->config = $config;
        $this->ebay_client = $ebay_client;
        $this->product_ebay_error_service = $product_ebay_error_service;
        $this->product_creator = $product_creator;
        $this->ebay_stock_calculator = $ebay_stock_calculator;
    }

    public function getEbayOfferCache(): EbayOfferCache
    {
        if (!isset($this->ebay_offer_cache)) {
            $this->ebay_offer_cache = new EbayOfferCache($this->config, $this->db);
        }

        return $this->ebay_offer_cache;
    }

    /**
     * legt fest ob neue angebote erezgut werden sollen
     */
    public function setDoAdd(bool $status): void
    {
        $this->do_add = $status;
    }

    public function setDoUpdate(bool $status): void
    {
        $this->do_update = $status;
    }

    public function setDoDelete(bool $status): void
    {
        $this->do_delete = $status;
    }

    public function getEbayProfileId(): int
    {
        return $this->config['ebay_profile_id'];
    }

    public function getPriceGroupId(): int
    {
        return $this->config['price_group_id'];
    }

    public function getEkGroupId(): int
    {
        return ProductPriceGroupRepository::getEkGroupIdForPriceGroup($this->getPriceGroupId());
    }

    public function getFilterListId(): int
    {
        return $this->config['filter_list_id'];
    }

    public function getShopId(): int
    {
        return $this->config['shop_id'];
    }

    /**
     * hilfsfunktion um einzelnes produkt auf ebay zu aktualisieren
     */
    public function dev_refreshProduct(int $product_id): void
    {
        $ebay_item_id = $this->getActiveEbayItemIdByProductId($product_id);

        if (!$ebay_item_id) {
            throw new FatalException('Kein aktives Angebot auf ebay');
        }

        $product = $this->createEbayProduct($product_id);

        var_dump($product);

        $result = $this->updateOffer($ebay_item_id, $product, true);

        debug::extendXdebug();
        var_dump($result);
    }


    /**
     * @param $ebay_item_id
     * @param EbayProduct $product
     * @param bool $full
     * @return EbayOfferResult
     * @throws EbayProductNotFoundException
     * @thorw SoapFault
     */
    public function updateOffer(string $ebay_item_id, EbayProduct $product, bool $full = false): EbayOfferResult
    {
        $offer = $this->db->query("
            SELECT
                ext_ebay_offers.eek_label_product_media_id,
                ext_ebay_offers.eek_fiche_product_media_id
            FROM
                ext_ebay_offers
            WHERE
                ext_ebay_offers.ebay_profile_id = '" . $this->getEbayProfileId() . "' AND
                ext_ebay_offers.product_id = '" . $product->getProductId() . "'
            ORDER BY
                ext_ebay_offers.date_created DESC
            LIMIT
                1
        ")->first();

        $update_eek = $offer && ($product->getEnergyEfficiencyLabelProductMediaId() != $offer['eek_label_product_media_id'] || $product->getEnergyEfficiencyFicheProductMediaId() != $offer['eek_fiche_product_media_id']);

        try {
            if ($full) {
                $raw_result = $this->ebay_client->updateProductFull($ebay_item_id, $product, $update_eek);
            } else {
                $raw_result = $this->ebay_client->updateProduct($ebay_item_id, $product, $update_eek);
            }

            $result = new EbayOfferResult($raw_result);
            $result->setAction(EbayOfferResult::ACTION_UPDATE);
        } catch (EbayProductNotFoundException $e) {
            $this->getEbayOfferCache()->removeOffer($ebay_item_id);
            throw $e;
        }

        if (!$result->isError() && !$result->isPictureSize()) {
            $this->getEbayOfferCache()->cacheOffer($ebay_item_id, $product);
        }

        return $result;
    }

    public function removeOffer(string $ebay_item_id): void
    {
        $result = $this->ebay_client->endFixedPriceItem($ebay_item_id);

        if ($result->Ack === 'Success') {
            $this->getEbayOfferCache()->removeOffer($ebay_item_id);
            return;
        }

        //fehlerfall... das ist wieder alles käse
        if (isset($result->Errors)) {
            //hm... Errors->ErrorCode?!? das sieht schon wieder nach liste/objekt mix aus...
            if (
                $result->Errors->ErrorCode === '17' || //angebot existiert nicht oder gehört nicht zu unserem Account
                $result->Errors->ErrorCode === '1047' //angebot bereits beendet
            ) {
                $this->getEbayOfferCache()->removeOffer($ebay_item_id);
                return;
            }
        }

        \debug::dump('Fehler beim Löschen von ' . $ebay_item_id, $result);
    }


    public function getActiveEbayItemIdByProductId(int $product_id): ?string
    {
        $ebay_item_id = $this->db->fieldQuery("
            SELECT
                ext_ebay_offers.ebay_item_id
            FROM
                ext_ebay_offers
            WHERE
                ext_ebay_offers.product_id = " . $product_id . " AND
                ext_ebay_offers.ebay_profile_id = " . $this->getEbayProfileId() . " AND
                ext_ebay_offers.active = 1
        ");

        return $ebay_item_id;
    }


    /**
     * @param EbayProduct $product
     * @return EbayOfferResult
     * @throws EbayExceptionApi
     * @throws EbayExceptionInvalidData
     */
    public function createOffer(EbayProduct $product): EbayOfferResult
    {
        $relisted = false;

        //prüfen ob wir ein produkt relisten können
        //letzten 2 angebote für das Produkt testen (ebay hat teilweise schon Angebote gelöscht und danach beschwert weil doppelt!?!)
        //->wir testen nur noch das letzte...
        $old_offers = $this->db->query("
            SELECT
                ext_ebay_offers.ebay_item_id
            FROM
                ext_ebay_offers
            WHERE
                ext_ebay_offers.ebay_profile_id = '" . $this->getEbayProfileId() . "' AND
                ext_ebay_offers.product_id = '" . $product->getProductId() . "'
            ORDER BY
                ext_ebay_offers.date_created DESC
            LIMIT
                1
        ");

        if ($old_offers) {
            foreach ($old_offers as $old_offer) {
                try {
                    $raw_result = $this->ebay_client->relistProduct($old_offer['ebay_item_id'], $product, true);
                    $relisted = true;
                    break;
                } catch (EbayProductNotFoundException $ebay_exception_product_not_found) {
                }
            }
        }

        if ($relisted === false) {
            $raw_result = $this->ebay_client->addProduct($product);
        }

        $result = new EbayOfferResult($raw_result);
        $result->setAction(EbayOfferResult::ACTION_ADD);
        if (!$result->isError()) {
            $ebay_item_id = $result->getEbayItemId();
            $this->getEbayOfferCache()->cacheOffer($ebay_item_id, $product, true);
        }

        return $result;
    }

    /**
     * @param string $offer_id
     * @param EbayProduct $product
     * @return \bqp\extern\Ebay\Offer\EbayOfferResult
     * @throws EbayExceptionApi
     * @throws EbayExceptionInvalidData
     */
    public function removeAndCreateOffer($offer_id, EbayProduct $product): EbayOfferResult
    {
        $this->removeOffer($offer_id);

        $raw_result = $this->ebay_client->addProduct($product);
        $result = new EbayOfferResult($raw_result);
        $result->setAction(EbayOfferResult::ACTION_ADD);
        if (!$result->isError()) {
            $ebay_item_id = $result->getEbayItemId();
            $this->getEbayOfferCache()->cacheOffer($ebay_item_id, $product);
        }

        return $result;
    }

    public function createEbayProduct(int $product_id): EbayProduct
    {
        return $this->product_creator->createByProductId($product_id);
    }

    public function getChangeList(): array
    {
        $cache = $this->getEbayOfferCache();

        $result = $this->db->query("
            SELECT
                product.product_id,
                product_prices.vk_brutto,

                product_ek.supplier_id,

                product_lager_meta.*
            FROM
                product_filter_list_results INNER JOIN
                product ON (product_filter_list_results.product_id = product.product_id) INNER JOIN
                product_prices ON (product.product_id = product_prices.product_id AND product_prices.price_group_id = " . $this->getPriceGroupId() . ") INNER JOIN
                product_lager_meta ON (product.product_id = product_lager_meta.product_id) INNER JOIN
                product_shop ON (product.product_id = product_shop.product_id AND product_shop.shop_id = " . $this->getShopId() . ") INNER JOIN
                product_ek_active ON (product.product_id = product_ek_active.product_id AND product_ek_active.ek_group_id =  " . $this->getEkGroupId() . ") INNER JOIN
		        product_ek ON (product_ek_active.ek_id = product_ek.ek_id)
            WHERE
                product_filter_list_results.filter_list_id = " . $this->getFilterListId() . " AND
                product_filter_list_results.status = 1 AND
                product_prices.price_active = 1 AND
                product_prices.vk_brutto > 0 AND
                product.product_id NOT IN (" . $this->db->makeIn($this->getBlacklistetedProductIds()) . ")
        ")->addCallback(function (array $row) {
            $row['bestand'] = $this->ebay_stock_calculator->limitBestand($this->ebay_stock_calculator->calcBestand($row, $row['supplier_id']), $row['vk_brutto']);
            return $row;
        });

        foreach ($result as $row) {
            $cache->compare($row['product_id'], $row['bestand'], $row['vk_brutto']);
        }

        return $cache->getChanges();
    }


    public function getBlacklistetedProductIds(): array
    {
        $product_ids = [];

        if ($this->product_ebay_error_service) {
            $product_ids = array_merge($product_ids, $this->product_ebay_error_service->getProductIdsWithError());
        }

        return $product_ids;
    }

    public function displayChangeList($changes = null): void
    {
        if ($changes === null) {
            $changes = $this->getChangeList();
        }

        echo "<pre>\n";
        echo count($changes) . "\n";
        if (!$changes) {
            echo "</pre>\n";
            return;
        }

        $src = new TableObjectDataSourceArrayHeaders($changes);

        $table = new TableObject($src);
        $table->setEntriesPerPage(2500);

        $renderer = new TableObjectRendererPlainText($table);
        echo $renderer->render();
        echo "</pre>\n";

        //$table->display();
    }


    public function refreshOffers(array $changes = null): void
    {
        if ($changes === null) {
            $changes = $this->getChangeList();
        }

        foreach ($changes as $action) {
            try {
                switch ($action['action']) {
                    case EbayOfferCache::ADD:
                        if ($this->do_add) {
                            $product = $this->createEbayProduct($action['product_id']);

                            //durch die Laufzeit kann es zwischenzeitlichen Bestandsänderungen kommen => Angebot überspringen, falls nicht mehr vorrätig.
                            if ($product->getBestand() < 1) {
                                break;
                            }

                            $result = $this->createOffer($product);
                            $this->handleOfferError($result, $product);
                        }
                        break;
                    case EbayOfferCache::UPDATE:
                        if ($this->do_update) {
                            $product = $this->createEbayProduct($action['product_id']);

                            if ($product->getBestand() < 1) {
                                break;
                            }

                            try {
                                $result = $this->updateOffer($action['ebay_item_id'], $product, true);
                                $this->handleOfferError($result, $product);
                            } catch (EbayProductNotFoundException $e) { //dürfte nur passieren wenn bestand abverkauft wurde
//                                var_dump($product->getProductId(), $product->getProductName());
//                                var_dump(get_class($e));
//                                var_dump($e->getMessage());

                                //$this->getEbayClient()->relistProduct($action['ebay_item_id'], $product);
                                //$this->createOffer($product);
                                //var_dump($product->getProductName());
                            }
                        }
                        break;
                    case EbayOfferCache::DEL:
                        if ($this->do_delete) {
                            $this->removeOffer($action['ebay_item_id']);
                        }
                        break;
                }
            } catch (SoapFault $e) {
                //interne anwendungsfehler von ebay ignrieren
                //@todo hat sich als dumme Idee rausgestellt -> ebay spuckt relativ häufig diesen Fehler, aber es geht total unter wenn ebay nur noch diesen Fehler zurückgibt
                if (strpos($e->getMessage(), 'Interner Anwendungsfehler') !== false) {
                    break;
                }

                if (strpos($e->getMessage(), 'Error Fetching http headers') !== false) {
                    break;
                }
            } catch (EbayProductCreatorException $e) {
                $this->createErrorEntry($action['product_id'], $e->getMessage());
            } catch (\Exception $e) {
                \debug::dump($action);
                throw $e;
            }
        }
    }

    public function handleOfferError(EbayOfferResult $result, EbayProduct $product): void
    {
        if ($result->isCatChangeByEbay() && $result->getEbayItemId()) {
            $event = new EbayCatChangeByEbay($product->getProductId(), $result->getEbayItemId());

            \service_loader::getEventDispatcher()->dispatch($event);
        }

        if (!$result->isError()) {
            return;
        }

        if ($result->isOfferLimit()) {
            echo 'LIMIT ADD: ' . $product->getProductId() . ' ' . $product->getProductName() . "<br>\n";
            return;
        }

        if ($result->isPictureSize()) {
            $this->createErrorEntry($product->getProductId(), 'Bildgröße entspricht nicht den eBay Richtlinien.', $result->getRawResultAsJson());
            return;
        }

        if ($result->isSystemError()) {
            return;
        }

        if ($result->isError()) {
            $message = '';

            if ($result->isEnvkvError()) {
                $message .= 'Verstoß gegen ENVKV';
            }

            foreach ($result->getErrors() as $error) {
                if ($message) {
                    $message .= "\n";
                }

                $message .= $error['msg'];
            }

            $this->createErrorEntry($product->getProductId(), $message, $result->getRawResultAsJson());
            return;
        }
    }

    protected function createErrorEntry(int $product_id, string $error_message, string $error_message_raw = ''): void
    {
        if (!$this->product_ebay_error_service) {
            return;
        }

        $this->product_ebay_error_service->addError($product_id, $error_message, $error_message_raw);
    }

    public function queueProductUpdate(int $product_id): void
    {
        $this->db->query("
            UPDATE
                ext_ebay_offers
            SET
                ext_ebay_offers.bestand = 777
            WHERE
                ext_ebay_offers.active = 1 AND
                ext_ebay_offers.product_id = " . $product_id . "
        ");
    }
}
