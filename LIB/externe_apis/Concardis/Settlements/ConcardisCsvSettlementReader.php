<?php

namespace bqp\extern\Concardis\Settlements;

use bqp\Date\DateMinMax;
use bqp\Date\DateObj;
use bqp\table\DataSource\TableObjectDataSourceArrayHeaders;

class ConcardisCsvSettlementReader
{
    private $raw_transactions = [];

    private $vertragspartnernummern = [];
    private $zahlungsnummer = null;

    public function loadByContent(string $csv_content): void
    {
        $csv = new \bqp\Csv\CsvReader();
        $csv->setCsvString($csv_content);

        foreach ($csv as $row) {
            //@todo format explizit prüfen... derzeit nur über datentyp error wegen null

            $this->add(
                $row['vertragspartnernummer'],
                $row['zahlungsnummer'],
                DateObj::createFromFormat('d.m.Y', $row['transaktionsdatum_datum']),
                $row['autorisierungscode'],
                $row['maskierte_kartennummer'],
                \input::parseFloat($row['umsatz_brutto']),
                \input::parseFloat($row['entgelt_gesamt_abr-whrg'])
            );
        }
    }

    public function getRechnungsnummer(): string
    {
        //abartigesformat
        //-In der Abrechnung sind mehrere Vertragspartnernummern enthalten. (wahrscheinlich dürfte es besser gehen von der Geschäftspartnernr auf die Vertranspartnernummer für die Abrechnung zu mappen, als aus den Vertragspartnernummern in der Abrechnung selbst.)
        //-Jahr ist auch nix explizit... Rechnung 0000001.20 enthält nur Transaktionen aus 2019

        if (strpos($this->zahlungsnummer, 'GIR-') === 0) {
            //bei EC Karten ist natürliche alles anders!
            return $this->zahlungsnummer;
        }

        $rechnungsnummer = '';
        $rechnungsnummer .= $this->getVertragspartnerNummer();
        $rechnungsnummer .= '.';
        $rechnungsnummer .= $this->zahlungsnummer;
        $rechnungsnummer .= '.';
        $rechnungsnummer .= $this->getYear();

        return $rechnungsnummer;
    }

    public function getSummeTransaktionen(): float
    {
        $sum = 0;

        foreach ($this->raw_transactions as $entry) {
            $sum += $entry['amount'];
        }

        return $sum;
    }

    public function getSummeGebuehren(): float
    {
        $sum = 0;

        foreach ($this->raw_transactions as $entry) {
            $sum += $entry['amount_fee'];
        }

        return $sum;
    }

    public function getAnzahlTransaktionen(): int
    {
        return count($this->raw_transactions);
    }

    public function getVertragspartnerNummer(): string
    {
        if (strpos($this->zahlungsnummer, 'GIR-') === 0) {
            //bei EC Karten gibt es natürlich keine Vertragspartner Nummer!
            //brauch das aber jetzt als Dummy, weil im Import geprüft wird, ob nicht vielleicht Abrechnungen zwischen K11 und Ecom vertauscht wurden!
            return '151098186';
        }

        $nrs = $this->vertragspartnernummern;
        sort($nrs);

        $match = implode('|', $nrs);

        switch ($match) {
            case '141098188':
            case '151098186':
            case '141098188|151098186':
                return '151098186';
            case '142926453':
            case '152926451':
            case '142926453|152926451':
                return '152926451';
            case '144181633|154181631':
                return '154181631';
        }

        throw new \DomainException('VP-Nummer des Zahlungsempfängers des Empfängers konnte nicht ermittelt werden (' . $match . ')');
    }

    private function getYear(): string
    {
        $max_date = $this->getLastTransactionDate();


        $nr = (int)$this->zahlungsnummer;

        if ($nr >= 4) { //Annahme: das auszahlungnr 000004 immer Transaktionen aus dem auszahlungsjahr enthält
            return $max_date->format('y');
        }

        //Annahme: am Jahresanfang gibt es Abrechnungen bei den die Transaktionen alle im vorjahr liegen.
        //Wir verscheiben die Transaktion um 30 Tage, damit diese definitv im nächsten Jahr landen.
        $max_date->addSimple('days', 30);
        return $max_date->format('y');
    }

    public function getLastTransactionDate(): DateObj
    {
        $date_min_max = new DateMinMax();

        foreach ($this->raw_transactions as $entry) {
            $date_min_max->add($entry['date']);
        }

        return $date_min_max->getMaxDate();
    }

    private function add(
        string $vertragspartnernummer,
        string $zahlungsnummer,
        DateObj $transaktionsdatum_datum,
        string $autorisierungscode,
        string $maskierte_kartennummer,
        float $umsatz,
        float $endgeld
    ): void {
        $this->checkNumbers($vertragspartnernummer, $zahlungsnummer);

        $this->raw_transactions[] = [
            'date' => $transaktionsdatum_datum,
            'authcode' => $autorisierungscode,
            'cc_number' => $maskierte_kartennummer,
            'amount' => $umsatz,
            'amount_fee' => $endgeld
        ];
    }

    private function checkNumbers(string $vertragspartnernummer, string $zahlungsnummer)
    {
        $this->vertragspartnernummern[$vertragspartnernummer] = $vertragspartnernummer;

        if ($this->zahlungsnummer === null) {
            $this->zahlungsnummer = $zahlungsnummer;
        }

        if (!$this->zahlungsnummer) {
            throw new \DomainException('Leere Zahlungsnnummern enthalten.');
        }

        if ($this->zahlungsnummer !== $zahlungsnummer) {
            throw new \DomainException('In dieser Abrechnung sind mehrere Zahlungsnnummern enthalten.');
        }
    }

    public function getRawTransactions(): array
    {
        return $this->raw_transactions;
    }


    public function debug()
    {
        var_dump($this->getRechnungsnummer());
        var_dump($this->getSummeTransaktionen());
        var_dump($this->getAnzahlTransaktionen());
        var_dump($this->getSummeGebuehren());

        var_dump($this->zahlungsnummer);
        var_dump($this->vertragspartnernummern);

        if (!$this->raw_transactions) {
            var_dump('no raw transactions');
            return;
        }

        $src = new TableObjectDataSourceArrayHeaders($this->raw_transactions);
        $table = new \bqp\table\TableObject($src);
        $table->display();
    }
}
