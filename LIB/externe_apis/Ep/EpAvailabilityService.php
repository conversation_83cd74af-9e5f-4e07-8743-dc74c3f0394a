<?php

namespace bqp\extern\Ep;

use bqp\table\DataSource\TableObjectDataSourceArrayHeaders;
use bqp\table\TableObjectInline;
use service_loader;
use wws\Supplier\SupplierAvailabilityService;
use wws\Supplier\SupplierRepository;

class EpAvailabilityService extends SupplierAvailabilityService
{
    private EpStockApiClient $ep_stock_api_client;

    public function __construct(int $supplier_id)
    {
        parent::__construct($supplier_id);

        $supplier_repo = service_loader::get(SupplierRepository::class);
        $config_key = $supplier_repo->getOrderApiConfigKey($supplier_id);
        $config_container = service_loader::getConfigRegistry()->get($config_key);

        $this->ep_stock_api_client = new EpStockApiClient($config_container);
    }

    public function getLink(string $gros_product_id): string
    {
        return '';
    }

    public function getForm(string $gros_product_id): string
    {
        return '';
    }

    public function getLoginForLink(string $gros_product_id): array
    {
        return [];
    }

    public function handleFormRequest(string $gros_product_id, array $daten): string
    {
        try {
            $data = $this->ep_stock_api_client->getByGrosProductId($gros_product_id);
        } catch (\Throwable $e) {
            return 'Bei der Anfrage ist ein Fehler aufgetreten. (' . $e::class . ': ' . $e->getMessage() . ')';
        }

        if (!$data) {
            return 'Keine Bestandsinformationen für die Artikelnummer ' . $gros_product_id . ' gefunden.';
        }

        $src = new TableObjectDataSourceArrayHeaders($data);
        $table = new TableObjectInline($src);
        return $table->render();
    }
}
