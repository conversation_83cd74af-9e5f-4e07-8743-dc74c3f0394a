<?php

namespace bqp\form;

use bqp\Exceptions\FatalException;
use bqp\field;
use bqp\field_manager;
use bqp\form\Validator\FormValidatorEmpty;
use bqp\form\Validator\FormValidatorLength;
use bqp\form\Validator\FormValidatorNumeric;
use service_loader;
use wws\Product\Form\FormElementProductPicker;

class wws_form_element
{

    /**
     * @param $field_key
     * @param bool|string $as_filter
     * @return form_element_checkbox|form_element_select|form_element_text|form_element_zahlungsart
     * @throws \bqp\Exceptions\FatalException
     * @throws \Exception
     */
    public static function field($field_key, $as_filter = false)
    {
        $config = field_manager::getInstance()->config($field_key);

        $di_container = service_loader::getDiContainer();

        if ($config === null) {
            throw new FatalException('unknown field "' . $field_key . '"');
        }

        if ($as_filter === false) {
            $as_filter = 'form';
        }

        if ($as_filter === true) {
            $as_filter = 'filter';
        }

        if ($as_filter) {
            $as_filter = 'as_' . $as_filter;
        }

        if ($as_filter && isset($config[$as_filter])) {
            $config = array_merge($config, $config[$as_filter]);
        }

        switch ($config['type']) {
            case 'string':
            case 'std_string':
                $field = new form_element_text();
                $field->addPreFilter(form_filter_trim::class);
                break;
            case 'integer_positiv':
                $field = new form_element_text();
                $filter = new form_filter_int();
                $field->addOutputFilter($filter);

                $validator = new Validator\FormValidatorNumeric();
                $validator->setOnlyInteger('Es sind nur ganzzahlige Werte erlaubt.');

//                if ($nullable) {
//                    $filter->setEmptyStringToZero(false);
//                    $validator->setCanEmpty(true);
//                }

                $field->addValidator($validator);

                break;
            case 'integer':
                $field = new form_element_text();
                $field->addOutputFilter(new form_filter_int());
                $validator = new Validator\FormValidatorNumeric();
                $validator->setOnlyInteger('Es sind nur ganzzahlige Werte erlaubt.');

                $field->addValidator($validator);
                break;
            case 'currency':
                $field = new form_element_text();

                $validator = new Validator\FormValidatorNumeric();
                if (isset($config['can_empty']) && $config['can_empty']) {
                    $validator->setCanEmpty(true);

                    $field->addOutputFilter(new form_filter_float())->setEmptyStringToZero(false);
                } else {
                    $field->addOutputFilter(new form_filter_float());
                }

                $field->addValidator($validator);

                break;
            case 'enum':
            case 'zahlungsart':
                if ($config['type'] === 'zahlungsart') {
                    $field = new form_element_zahlungsart();
                } else {
                    $field = new form_element_select();
                }

                $enums = false;

                if (isset($config['enums'])) {
                    $enums = $config['enums'];
                } elseif (isset($config['enum'])) {
                    $enums = $config['enum'];
                }

                if ($enums) {
                    if (is_callable($enums)) {
                        $enums = $di_container->call($enums);
                    }

                    $field->setOptionsSimple($enums);
                }
                break;
            case 'bool':
                $field = new form_element_checkbox();
                $field->setValue(1);
                break;
            case 'memo':
            case 'std_memo':
            case 'std_memo_large':
            case 'std_memo_xlarge':
                $field = new form_element_textarea();

                if ($config['type'] === 'std_memo_large') {
                    $field->setClass('large');
                }
                if ($config['type'] === 'std_memo_xlarge') {
                    $field->setClass('xlarge');
                }
                break;
            case 'html':
                $field = new form_element_tinymce();
                break;
            case 'html_small':
                $field = new form_element_tinymce();
                $field->setHeight(100);
                break;
            case 'html_shop':
                $field = new form_element_tinymce();
                $field->setCmsHelp(true);
                $field->setFilemanger(true);
                $field->addPlugin('shop_images');
                $field->addContentCss('https://www.smartgoods.de/design/images/style.css');
                break;
            case 'date':
                $field = new form_element_date_picker();
                break;
            case 'shop_url':
                $field = new form_element_shop_url();
                $field->addPreFilter(form_filter_trim::class);
                break;
            case 'hidden_id':
                $field = new form_element_hidden();
                $field->addPreFilter(form_filter_int::class);
                break;
            case 'product':
                $field = new FormElementProductPicker();
                break;
            case 'multiple_products':
                $field = new form_element_product_multi_picker();
                $field->setCatTreeId($config['cat_tree_id']);
                break;
            case 'multiple_devices':
                $field = new form_element_device_multi_picker();
                $field->enableResize();
                break;
            case 'ajax_category':
                $field = new form_element_ajax_category();
                $field->setShowPickerLink(true);
                if (isset($config['cat_tree_id'])) {
                    $field->setCatTreeId($config['cat_tree_id']);
                }
                break;
            case 'multiple_categories':
                $field = new form_element_category_multi_picker();
                if (isset($config['cat_tree_id'])) {
                    $field->setCatTreeId($config['cat_tree_id']);
                }
                break;
            case 'product_type':
                $field = new form_element_product_type();
                if (isset($config['multiple']) && $config['multiple']) {
                    $field->setName($config['field'] . '[]');
                    $field->setAutosize(true);
                }

                break;
            case 'supplier':
                $field = new FormElementSuppliers();
                break;
            default:
                if (strpos($config['type'], '\\') !== false) {
                    $key = $config['type'];
                } else {
                    $key = self::mapTypeToFqcn($config['type']);
                }

                $field = new $key();
        }

        $field->setLabel($config['label']);

        if ($field instanceof form_element_select) {



            if (isset($config['multiple']) && $config['multiple']) {
                $field->setName($config['field'] . '[]');
            }

            if (empty($config['multiple']) && (empty($config['required']) || !empty($config['default_empty']))) {
                $field->addEmptyOption(true);
            }

            if (array_key_exists('autosize', $config)) {
                $field->setAutosize($config['autosize']);
            }

            if (array_key_exists('size', $config)) {
                $field->setSize($config['size']);
            }

            if (isset($config['empty_option']) && $config['empty_option']) {
                $field->addEmptyOption($config['empty_option']);
            }
        }

        if (isset($config['required'])) {
            if ($config['required']) {
                $field->addValidator(FormValidatorEmpty::class);
            } else {
                if (method_exists($field, 'setCanEmpty')) {
                    $field->setCanEmpty(true);
                }

                if (method_exists($field, 'addEmptyOption')) {
                    if (!$field->isMultiple()) {
                        $field->addEmptyOption(true);
                    }
                }

                foreach ($field->getValidators() as $validator) {
                    if ($validator instanceof FormValidatorNumeric) {
                        $validator->setCanEmpty(true);
                    }
                }
            }
        }

        if (isset($config['length_min']) || isset($config['length_max'])) {
            /** @var FormValidatorLength $length */
            $length = $field->addValidator(FormValidatorLength::class);

            if (isset($config['length_min'])) {
                $length->setMinLength($config['length_min']);
            }

            if (isset($config['length_max'])) {
                $length->setMaxLength($config['length_max']);
            }
        }

        if (!$field->isName()) {
            $field->setName($config['field']);
        }

        if (isset($config['default_value'])) {
            $field->setValue($config['default_value']);
        }

        if (isset($config['input_callback'])) {
            $config['input_callback']($field);
        }

        if (isset($config['hint'])) {
            $field->setHelp($config['hint']);
        }

        if (isset($config['dynamic_suffix_template'])) {
            $field->setDynamicSuffixTemplate($config['dynamic_suffix_template']);
        }

        if (isset($config['append_after']) && method_exists($field, 'appendAfter')) {
            $field->appendAfter($config['append_after']);
        }

        return $field;
    }

    public static function fieldLabel(string $field_key): string
    {
        $config = field_manager::getInstance()->config($field_key);

        if ($config === null) {
            throw new FatalException('unknown field "' . $field_key . '"');
        }

        return $config['label'];
    }

    //@todo
    private static function mapTypeToFqcn(string $type): ?string
    {
        return match ($type) {
            'actionbar' => \bqp\form\form_element_actionbar::class,
            'ajax_category' => \bqp\form\form_element_ajax_category::class,
            'auft_search' => \bqp\form\form_element_auft_search::class,
            'brand_multi_picker' => \bqp\form\form_element_brand_multi_picker::class,
            'button' => \bqp\form\form_element_button::class,
            'category_multi_picker' => \bqp\form\form_element_category_multi_picker::class,
            'category_picker' => \bqp\form\form_element_category_picker::class,
            'checkbox' => \bqp\form\form_element_checkbox::class,
            'color' => \bqp\form\form_element_color::class,
            'conditional_group' => \bqp\form\form_element_conditional_group::class,
            'container' => \bqp\form\form_element_container::class,
            'date_picker' => \bqp\form\form_element_date_picker::class,
            'date_range_picker' => \bqp\form\form_element_date_range_picker::class,
            'device_multi_picker' => \bqp\form\form_element_device_multi_picker::class,
            'errorcode_multi_picker' => \bqp\form\form_element_errorcode_multi_picker::class,
            'feature_group_picker' => \bqp\form\form_element_feature_group_picker::class,
            'fieldset' => \bqp\form\form_element_fieldset::class,
            'filemanager' => \bqp\form\form_element_filemanager::class,
            'hidden' => \bqp\form\form_element_hidden::class,
            'html' => \bqp\form\form_element_html::class,
            'html_persistent' => \bqp\form\form_element_html_persistent::class,
            'images' => \bqp\form\form_element_images::class,
            'input' => \bqp\form\form_element_input::class,
            'laeger' => \bqp\form\form_element_laeger::class,
            'line' => \bqp\form\form_element_line::class,
            'mail_system' => \bqp\form\form_element_mail_system::class,
            'matrix' => \bqp\form\form_element_matrix::class,
            'matrix_checkbox' => \bqp\form\form_element_matrix_checkbox::class,
            'numeric' => \bqp\form\form_element_numeric::class,
            'password' => \bqp\form\form_element_password::class,
            'product_multi_picker' => \bqp\form\form_element_product_multi_picker::class,
            'product_type' => \bqp\form\form_element_product_type::class,
            'radio' => \bqp\form\form_element_radio::class,
            'regex' => \bqp\form\form_element_regex::class,
            'select' => \bqp\form\form_element_select::class,
            'select_extended' => \bqp\form\form_element_select_extended::class,
            'shipment_type' => \bqp\form\form_element_shipment_type::class,
            'shop_url' => \bqp\form\form_element_shop_url::class,
            'table' => \bqp\form\form_element_table::class,
            'table_object_container' => \bqp\form\form_element_table_object_container::class,
            'table_row' => \bqp\form\form_element_table_row::class,
            'text' => \bqp\form\form_element_text::class,
            'textarea' => \bqp\form\form_element_textarea::class,
            'tinymce' => \bqp\form\form_element_tinymce::class,
            'url' => \bqp\form\form_element_url::class,
            'zahlungsart' => \bqp\form\form_element_zahlungsart::class,
            default => null,
        };
    }
}
