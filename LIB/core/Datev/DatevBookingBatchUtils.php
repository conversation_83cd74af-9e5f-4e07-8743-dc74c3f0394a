<?php

namespace bqp\Datev;

use bqp\table\DataSource\TableObjectDataSourceArrayHeaders;
use bqp\table\field\table_object_field;
use bqp\table\TableObject;
use Traversable;

class DatevBookingBatchUtils
{


    public static function getTableSaldoByAccountAndCounterAccount(array|Traversable $rows): TableObject
    {
        $saldos = [];
        foreach ($rows as $row) {
            $account = $row['account_number'];
            $counter_account = $row['counter_account_number'];

            $key = $account . '-' . $counter_account;

            if (!isset($saldos[$key])) {
                $saldos[$key] = [
                    'key' => $key,
                    'account' => $account,
                    'counter_account' => $counter_account,
                    'transactions' => 0,
                    'balance_S' => 0,
                    'transactions_S' => 0,
                    'balance_H' => 0,
                    'transactions_H' => 0,
                    'S-H' => 0
                ];
            }

            $saldos[$key]['balance_' . $row['debit_credit_indicator']] += $row['transaction_value'];
            $saldos[$key]['transactions']++;
            $saldos[$key]['transactions_' . $row['debit_credit_indicator']]++;

            $saldos[$key]['S-H'] = round($saldos[$key]['balance_S'] - $saldos[$key]['balance_H'], 2);
        }

        uasort($saldos, function ($a, $b) {
            return $b['transactions'] <=> $a['transactions'];
        });

        $src = new TableObjectDataSourceArrayHeaders($saldos);
        $table = new TableObject($src);
        $table->setCaption('Summen nach Konto - Gegenkonto');
        $table->setEntriesPerPage(100);

        return $table;
    }


    public static function getTableSaldoByAccount(array|Traversable $rows): TableObject
    {
        $saldos = [];
        foreach ($rows as $row) {
            $account = $row['account_number'];
            $key = $account;

            if (!isset($saldos[$key])) {
                $saldos[$key] = [
                    'account' => $account,
                    'transactions' => 0,
                    'balance_S' => 0,
                    'transactions_S' => 0,
                    'balance_H' => 0,
                    'transactions_H' => 0,
                    'S-H' => 0
                ];
            }

            $sign = $row['debit_credit_indicator'];

            $saldos[$key]['balance_' . $sign] += $row['transaction_value'];
            $saldos[$key]['transactions']++;
            $saldos[$key]['transactions_' . $sign]++;
            $saldos[$key]['S-H'] = round($saldos[$key]['balance_S'] - $saldos[$key]['balance_H'], 2);

            //gegenkonto
            $account = $row['counter_account_number'];
            $key = $account;

            if (!isset($saldos[$key])) {
                $saldos[$key] = [
                    'account' => $account,
                    'transactions' => 0,
                    'balance_S' => 0,
                    'transactions_S' => 0,
                    'balance_H' => 0,
                    'transactions_H' => 0,
                    'S-H' => 0
                ];
            }

            $sign = $row['debit_credit_indicator'] === 'H' ? 'S' : 'H';

            $saldos[$key]['balance_' . $sign] += $row['transaction_value'];
            $saldos[$key]['transactions']++;
            $saldos[$key]['transactions_' . $sign]++;
            $saldos[$key]['S-H'] = round($saldos[$key]['balance_S'] - $saldos[$key]['balance_H'], 2);
        }

        uasort($saldos, function ($a, $b) {
            return $b['transactions'] <=> $a['transactions'];
        });

        $src = new TableObjectDataSourceArrayHeaders($saldos);
        $table = new TableObject($src);
        $table->setCaption('Summen nach Konto');
        $table->setEntriesPerPage(100);

        return $table;
    }
}
