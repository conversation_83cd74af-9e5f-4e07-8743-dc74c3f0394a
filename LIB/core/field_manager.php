<?php

namespace bqp;

use bqp\Exceptions\DevException;
use bqp\Exceptions\SecurityException;
use bqp\FileAttachment\FileAttachmentOutput;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_callback;
use bqp\table\field\table_object_field_checkbox;
use bqp\table\field\table_object_field_currency;
use bqp\table\field\table_object_field_date;
use bqp\table\field\table_object_field_html;
use bqp\table\field\table_object_field_mapping;
use bqp\table\field\table_object_field_numeric;
use bqp\table\field\table_object_field_pos;
use bqp\table\field\table_object_field_short_url;
use bqp\table\field\table_object_field_url;
use bqp\Utils\StringUtils;

class field_manager
{
    protected array $configs = [];

    public static function getInstance(): field_manager
    {
        static $instance;
        if (!$instance) {
            $instance = new field_manager();
        }

        return $instance;
    }

    public function isFieldKey(string $field_key): bool
    {
        if (strpos($field_key, '.') === false) {
            return false;
        }

        $tokens = explode('.', $field_key);

        if (count($tokens) === 2) {
            $table = $tokens[0];
        } elseif (count($tokens) > 2) {
            if ($tokens[0] !== 'MAKRO') {
                return false;
            }
            $table = $tokens[1];
        } else {
            return false;
        }

        if (preg_match('~[^_a-z0-9]~', $table) || !$this->isModelConfig($table)) {
            return false;
        }

        $config = $this->getModelConfig($table);

        return isset($config[$field_key]);
    }

    private function getFilePath(string $table): string
    {
        if (preg_match('~[^_a-z0-9]~', $table)) {
            throw new SecurityException('Invalid table name "' . $table . '"');
        }

//
//        if (!preg_match('~^[_a-z0-9]+$~', $table) || !file_exists(config::system('root_dir') . '/configs/fields/' . $table . '.php')) {
//            return null;
//        }

        return __DIR__  . '/../../configs/fields/' . $table . '.php';
    }

    public function getModelConfig(string $table): array
    {
        if (!isset($this->configs[$table])) {
            $this->configs[$table] = include($this->getFilePath($table));
        }

        return $this->configs[$table];
    }

    public function isModelConfig(string $table): bool
    {
        if (isset($this->configs[$table])) {
            return true;
        }

        if (is_readable($this->getFilePath($table))) {
            return true;
        }

        return false;
    }

    public function getProtokollConfig(string $table): array
    {
        $config = $this->getModelConfig($table);

        if (isset($config['protokoll'])) {
            return $config['protokoll'];
        }

        return [];
    }

    /**
     * @param string $table
     * @return array
     */
    public function getDefaults(string $table): array
    {
        $config = $this->getModelConfig($table);

        if (isset($config['defaults'])) {
            $defaults = [];
            foreach ($config['defaults'] as $key => $value) {
                //defaults können mit tabellen angabe sein
                $pos = strpos($key, '.');
                if ($pos !== false) {
                    $key = substr($key, $pos + 1); //tabelle entfernen
                }

                $defaults[$key] = $value;
            }

            return $defaults;
        }

        return [];
    }


    /**
     * Gibt die Spalten zurück die im TableObject frei genutzt werden können für die Tabelle
     *
     * WIP: sobald table_object_field_select definiert ist, geben wir alle Spalten als Option zurück. Das lässt sich um eine
     * Blacklist/Whitelist erweitern. Auch Macros und co sind vielleicht noch interessant.
     */
    public function getTableObjectSelectableFieldKeys(string $table): array
    {
        $model_config = $this->getModelConfig($table);

        //bräuchte es jetzt auch nicht zwingend mehr...
        if (!isset($model_config['table_object_field_select'])) {
            return [];
        }

        $field_keys = [];

        foreach ($model_config as $key => $field_config) {
            if (!str_contains($key, '.')) {
                continue;
            }

            if (isset($field_config['required_sql']) || isset($field_config['as_table']['required_sql'])) {
                //@todo das funktioniert derzeit nicht im Zusammenspiel mit \bqp\table\DataSource\TableObjectDataSourceWws::addOptionalHeaders()
                continue;
            }

            $field_keys[] = $key;
        }

        return $field_keys;
    }


    /**
     * gibt die konfiguration eines Feldes zurück
     */
    public function config(string $field_key, bool $strict = true): ?array
    {
        $tokens = explode('.', $field_key);
        if (count($tokens) === 2) {
            $table = $tokens[0];
            $field = $tokens[1];
        } elseif (count($tokens) > 2) {
            if ($tokens[0] !== 'MAKRO') {
                if ($strict) {
                    throw new DevException('invalid field_key "' . $field_key . '"');
                } else {
                    return null;
                }
            }

            $table = $tokens[1];
            $field = $tokens[2];
        }

        $parameter = null;

        if (preg_match('~^([_a-z0-9]+)\[([^\]]*)\]$~', $field, $temp)) {
            $field = $temp[1];
            $parameter = $temp[2];

            $field_key = $table . '.' . $field;
        }

        //in isModelConfig ist ein harter Check drin auf unzulässige Zeichen, weil wir $table includen... das fangen wir hier vorher ab. wenn nicht strict
        //$strict wird verwendet, im kontext wenn die $fields aus den Queries extrahiert wird
        if (!$strict && preg_match('~[^_a-z0-9]~', $table)) {
            return null;
        }

        if (!self::getInstance()->isModelConfig($table)) {
            return null;
        }

        $_cfg = self::getInstance()->getModelConfig($table);

        if (!isset($_cfg[$field_key])) {
            if (!$strict) {
                return null;
            }
            throw new DevException('unknown $field_key "' . $field_key . '"');
        }

        $field_config = $_cfg[$field_key];

        $field_config['table'] = $table;
        $field_config['field'] = $field;
        $field_config['field_key'] = $field_key;
        $field_config['group_label'] = $_cfg['table_object_field_select']['group_label'] ?? $table;

        return $field_config;
    }

    /**
     * validiert die $value gegen die Regeln des Feldes
     *
     * @param $field_key
     * @param $value
     */
    public function validValue($field_key, $value)
    {
    }


    private function configAsTableConfig($field_key, $strict = true)
    {
        if (strpos($field_key, '.') === false) {
            return null;
        }

        $config = $this->config($field_key, $strict);

        if (!$config) {
            return $config;
        }

        if (isset($config['table_type'])) {
            $config['type'] = $config['table_type'];
        } else {
            $config['type'] = self::typeToTableType($config['type']);
        }

        if (isset($config['as_table'])) {
            $config = array_merge($config, $config['as_table']);
        }

        if (isset($config['sql'])) {
            $config['required_sql'] = $config['sql'];
        }

        $config['name'] = $config['label'];

        if (isset($config['order']) && $config['order'] === true) {
            $config['order'] = $field_key;
        }

        return $config;
    }

    public function getAsTableField(string $field_key, ?string $alias = null): table_object_field
    {
        if ($alias === null) {
            $alias = substr($field_key, strrpos($field_key, '.') + 1);
        }
        $alias = trim($alias, '`');

        $config = $this->configAsTableConfig($field_key);

        $field = self::createTableField($config, $field_key);

        $field->setKey($field_key);
        $field->setDataKey($alias);
        if (isset($config['alias'])) { //wenn der alias definiert ist, dann wird der schon über das SQL in der config vorgegeben...
            $field->setDataKey($config['alias']);
        }
        $field->setMetadata('field_key', $field_key);

        //macht das hier sinn? nein... das ist für ProductListHeaderProfile
        //das ist hier durch ein refactoring gelandet, um die Strukturen zu vereinheitlichen. Zwischenschritt
        //um ProductList und ProductListHeaderProfile auf "FieldSelect" umzustellen. Damit lässt sich das hier später wieder auf.
        if (!$field->getRequierdSqlFields() && !StringUtils::beginsi($field_key, 'MAKRO.')) {
            $field->addRequierdSqlFields([$field_key]);
        }

        return $field;
    }

    private static function createTableField($config, $field_key)
    {
        $type = '';

        if (!isset($config['label']) && isset($config['name'])) {
            $config['label'] = $config['name'];
        }

        if (isset($config['callback'])) {
            $type = 'callback';
        }

        if (isset($config['type'])) {
            $type = $config['type'];
        }

        switch ($type) {
            case 'currency':
                $field = new table_object_field_currency($field_key, $config['label']);
                $field->setStyle('white-space: nowrap;');
                $field->setAlign(table_object_field::ALIGN_RIGHT);
                $field->setNullToZero(false);
                if (isset($config['currency_code'])) {
                    $field->setCurrencyCodeDynamic($config['currency_code']);
                }
                break;
            case 'currency_high':
                $field = new table_object_field_currency($field_key, $config['label']);
                $field->setStyle('white-space: nowrap;');
                $field->setAlign(table_object_field::ALIGN_RIGHT);
                $field->setHighlight(true);
                if (isset($config['currency_code'])) {
                    $field->setCurrencyCodeDynamic($config['currency_code']);
                }
                break;
            case 'percent':
                $field = new table_object_field_numeric($field_key, $config['label']);
                $field->setAlign(table_object_field::ALIGN_RIGHT);
                $field->setSuffix('%');
                break;
            case 'date':
            case 'datetime':
                $field = new table_object_field_date($field_key, $config['label']);

                if (isset($config['format'])) {
                    $field->setFormat($config['format']);
                }

                if (isset($config['format_output'])) {
                    $field->setFormatOutput($config['format_output']);
                } elseif ($type === 'datetime') {
                    $field->setFormatOutput('d.m.Y H:i:s');
                }

                break;
            case 'checkbox':
                $field = new table_object_field_checkbox($field_key, $config['label']);
                $field->setDisabled();
                $field->setCheckedCallback(function ($value) use ($field) {
                    return $value[$field->getDataKey()] == 1;
                });
                break;
            case 'int':
                $field = new table_object_field_numeric($field_key, $config['label']);
                $field->setAlign(table_object_field::ALIGN_RIGHT);
                if ($config['decimal']) {
                    $field->setFormat($config['decimal']);
                }
                break;
            case 'callback':
            case table_object_field_callback::class:
                $field = new table_object_field_callback($field_key, $config['label']);
                $field->setCallback($config['callback']);
                break;
            case 'pos':
            case table_object_field_pos::class:
                $field = new table_object_field_pos($field_key, $config['label']);
                break;
            case 'url':
                $field = new table_object_field_url($field_key, $config['label']);
                break;
            case 'short_url':
                $field = new table_object_field_short_url($field_key, $config['label']);
                break;
            case 'string':
            case 'string_no_html':
                $field = new table_object_field($field_key, $config['label']);
                $field->setAllowHtmlValues(false);
                break;
            case 'html':
                $field = new table_object_field_html($field_key, $config['label']);
                break;
            case 'enum':
                $field = new table_object_field_mapping($field_key, $config['label']);
                $field->setMapping($config['enums']);
                break;
            case 'FileAttachments':
                $field = new table_object_field_callback($field_key, $config['label']);
                $field->setCallback(function (array $row, string $key) {
                    return FileAttachmentOutput::serializedToHtml($row[$key]);
                });
                break;
            default:
                $field = new table_object_field($field_key, $config['label']);
        }

        if (isset($config['order'])) {
            $field->setOrderable($config['order']);
        }

        if (isset($config['align'])) {
            $field->setAlign($config['align']);
        }

        if (isset($config['template'])) {
            $field->setTemplate($config['template']);
        }

        if (isset($config['link'])) {
            $field->addLink($config['link']);
        }

        if (isset($config['rollup_complete'])) {
            $field->setRollup($config['rollup_complete']);
        }

        if (isset($config['rollup'])) {
            $field->setRollup($config['rollup'], false);
        }

        if (isset($config['header_style'])) {
            $field->setHeaderStyle($config['header_style']);
        }

        if (isset($config['required_sql'])) {
            $field->addRequierdSqlFields($config['required_sql']);
        }

        if (isset($config['style'])) {
            $field->addStyle($config['style']);
        }

        if (isset($config['prefix'])) {
            $field->setPrefix($config['prefix'], false);
        }

        if (isset($config['suffix'])) {
            $field->setSuffix($config['suffix'], false);
        }

        if (isset($config['help'])) {
            $field->setHelp($config['help']);
        }

        if (isset($config['group_label'])) {
            $field->setGroupLabel($config['group_label']);
        }

        if (isset($config['alias'])) {
            $field->setDataKey($config['alias']);
        }

        return $field;
    }

    public static function typeToTableType(?string $type): string
    {
        switch ($type) {
            case 'checkbox':
                return 'checkbox';
            case 'enum':
                return 'enum';
            case 'date':
                return 'date';
            case 'datetime':
                return 'datetime';
            case 'currency':
                return 'currency';
            case 'currency_high':
                return 'currency_high';
            case 'callback':
                return 'callback';
        }
        return 'string';
    }
}
