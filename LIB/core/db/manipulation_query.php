<?php

use bqp\db\sql\SqlLexer;
use bqp\field_manager;
use bqp\Utils\StringUtils;

/**
 * This is a simple sql tokenizer / parser.
 * I needed code to get the total count of a paged recordset, so I wrote this.
 * It seems to handle sane mssql/mysql queries.
 *
 * PROTOTYPE
 *
 * <AUTHOR> <<EMAIL>>
 * @license LGPL
 * @version 0.0.3
 */

class manipulation_query extends query
{
    public $handle = null;
    public static $querysections = ['alter', 'create', 'drop', 'select', 'delete', 'insert', 'update', 'from', 'where', 'limit', 'order', 'group', 'having'];
    public static $operators = ['=', '<>', '<', '<=', '>', '>=', 'like', 'clike', 'slike', 'not', 'is', 'in', 'between'];
    public static $types = ['character', 'char', 'varchar', 'nchar', 'bit', 'numeric', 'decimal', 'dec', 'integer', 'int', 'smallint', 'float', 'real', 'double', 'date', 'datetime', 'time', 'timestamp', 'interval', 'bool', 'boolean', 'set', 'enum', 'text'];
    public static $conjuctions = ['by', 'as', 'on', 'into', 'from', 'where', 'with'];
    public static $funcitons = ['avg', 'count', 'max', 'min', 'sum', 'nextval', 'currval', 'concat'];
    public static $reserved = [
        'absolute',
        'action',
        'add',
        'all',
        'allocate',
        'and',
        'any',
        'are',
        'asc',
        'ascending',
        'assertion',
        'at',
        'authorization',
        'begin',
        'bit_length',
        'both',
        'cascade',
        'cascaded',
        'case',
        'cast',
        'catalog',
        'char_length',
        'character_length',
        'check',
        'close',
        'coalesce',
        'collate',
        'collation',
        'column',
        'commit',
        'connect',
        'connection',
        'constraint',
        'constraints',
        'continue',
        'convert',
        'corresponding',
        'cross',
        'current',
        'current_date',
        'current_time',
        'current_timestamp',
        'current_user',
        'cursor',
        'day',
        'deallocate',
        'declare',
        'default',
        'deferrable',
        'deferred',
        'desc',
        'descending',
        'describe',
        'descriptor',
        'diagnostics',
        'disconnect',
        'distinct',
        'domain',
        'else',
        'end',
        'end-exec',
        'escape',
        'except',
        'exception',
        'exec',
        'execute',
        'exists',
        'external',
        'extract',
        'false',
        'fetch',
        'first',
        'for',
        'foreign',
        'found',
        'full',
        'get',
        'global',
        'go',
        'goto',
        'grant',
        'group',
        'having',
        'hour',
        'identity',
        'immediate',
        'indicator',
        'initially',
        'inner',
        'input',
        'insensitive',
        'intersect',
        'isolation',
        'join',
        'key',
        'language',
        'last',
        'leading',
        'left',
        'level',
        'limit',
        'local',
        'lower',
        'match',
        'minute',
        'module',
        'month',
        'names',
        'national',
        'natural',
        'next',
        'no',
        'null',
        'nullif',
        'octet_length',
        'of',
        'only',
        'open',
        'option',
        'or',
        'order',
        'outer',
        'output',
        'overlaps',
        'pad',
        'partial',
        'position',
        'precision',
        'prepare',
        'preserve',
        'primary',
        'prior',
        'privileges',
        'procedure',
        'public',
        'read',
        'references',
        'relative',
        'restrict',
        'revoke',
        'right',
        'rollback',
        'rows',
        'schema',
        'scroll',
        'second',
        'section',
        'session',
        'session_user',
        'size',
        'some',
        'space',
        'sql',
        'sqlcode',
        'sqlerror',
        'sqlstate',
        'substring',
        'system_user',
        'table',
        'temporary',
        'then',
        'timezone_hour',
        'timezone_minute',
        'to',
        'trailing',
        'transaction',
        'translate',
        'translation',
        'trim',
        'true',
        'union',
        'unique',
        'unknown',
        'upper',
        'usage',
        'user',
        'using',
        'value',
        'values',
        'varying',
        'view',
        'when',
        'whenever',
        'work',
        'write',
        'year',
        'zone',
        'eoc'
    ];
    public static $startparens = ['{', '(', '['];
    public static $endparens = ['}', ')', ']'];
    public static $tokens = [',', ' '];
    protected $query = [];
    protected $raw_query = [];

    public function getSqlQuery()
    {
        if ($this->query['select']) {
            $query = '';
            $query .= $this->query['select'] . ' ';
            $query .= $this->query['from'] . ' ';
            $query .= $this->query['where'] . ' ';
            if (isset($this->query['group'])) {
                $query .= $this->query['group'] . ' ';
            }
            if (isset($this->query['group_by'])) {
                $query .= $this->query['group_by'] . ' ';
            }
            if (isset($this->query['group by'])) {
                $query .= $this->query['group by'] . ' ';
            }
            if (isset($this->query['having'])) {
                $query .= $this->query['having'] . ' ';
            }
            if (isset($this->query['order'])) {
                $query .= $this->query['order'] . ' ';
            }
            if (isset($this->query['order_by'])) {
                $query .= $this->query['order_by'] . ' ';
            }
            if (isset($this->query['order by'])) {
                $query .= $this->query['order by'] . ' ';
            }
            if (isset($this->query['limit'])) {
                $query .= $this->query['limit'];
            }

            return $query;
        }

        return implode('', $this->query);
    }

    /**
     * Simple SQL Parser
     *
     * @param string $sqlQuery
     * @license LGPL
     * <AUTHOR> Carlson <<EMAIL>>
     */
    public function __construct($sqlQuery)
    {
        parent::__construct($sqlQuery);

        $tokens = (new SqlLexer())->split(trim($sqlQuery));

        $tokenCount = count($tokens);
        $queryParts = [];
        $section = $tokens[0];

        // parse the tokens
        for ($t = 0; $t < $tokenCount; $t++) {
            if (in_array($tokens[$t], self::$startparens)) {
                $sub = $this->readsub($tokens, $t);

                $t = $this->readsub_position;
                $this->query[$section] .= $sub;
                $this->raw_query[$section][] = $sub;
            } else {
                if (in_array(strtolower($tokens[$t]), self::$querysections) && !isset($this->query[$tokens[$t]])) {
                    $section = strtolower($tokens[$t]);
                }

                // rebuild the query in sections
                if (!isset($this->query[$section]) || $this->query[$section] == '') {
                    $this->query[$section] = '';
                }
                $this->query[$section] .= $tokens[$t];
                $this->raw_query[$section][] = $tokens[$t];
            }
        }

        if (!isset($this->query['where'])) {
            $this->query['where'] = '';
        }
    }

    private $readsub_position = 0;

    /**
     * Parses a section of a query ( usually a sub-query or where clause )
     *
     * @param array $tokens
     * @param int $position
     * @return string section
     */
    private function readsub($tokens, $position = null)
    {
        if ($position !== null) {
            $this->readsub_position = $position;
        }

        $sub = $tokens[$this->readsub_position];
        $tokenCount = count($tokens);
        $this->readsub_position++;
        $subs = 0;
        while (!in_array($tokens[$this->readsub_position], self::$endparens) && $this->readsub_position < $tokenCount) {
            if (in_array($tokens[$this->readsub_position], self::$startparens)) {
                $sub .= $this->readsub($tokens);
                $subs++;
            } else {
                $sub .= $tokens[$this->readsub_position];
            }
            $this->readsub_position++;
        }

        $sub .= $tokens[$this->readsub_position];
        return $sub;
    }


    /**
     * Returns manipulated sql to get the number of rows in the query.
     *
     * @return string sql
     * @license LGPL
     * <AUTHOR> Carlson <<EMAIL>>
     */
    public function getCountQuery()
    {
        $this->query['select'] = 'select count(*) as `count` ';
        unset($this->query['limit']);
        #die(implode('',$this->query));
        return implode('', $this->query);
    }

    /**
     * Returns manipulated sql to get the unlimited number of rows in the query.
     *
     * @return string sql
     * @license LGPL
     * <AUTHOR> Carlson <<EMAIL>>
     */
    public function getLimitedCountQuery()
    {
        $this->query['select'] = 'select count(*) as `count` ';
        return implode('', $this->query);
    }

    /**
     * Returns the select section of the query.
     *
     * @return string sql
     * @license LGPL
     * <AUTHOR> Carlson <<EMAIL>>
     */
    public function getSelectStatement()
    {
        return $this->query['select'];
    }

    /**
     * Returns the from section of the query.
     *
     * @return string sql
     * @license LGPL
     * <AUTHOR> Carlson <<EMAIL>>
     */
    public function getFromStatement()
    {
        return $this->query['from'];
    }

    /**
     * Returns the where section of the query.
     *
     * @return string sql
     * @license LGPL
     * <AUTHOR> Carlson <<EMAIL>>
     */
    public function getWhereStatement()
    {
        return $this->query['where'];
    }

    public function getLimit()
    {
        if (!isset($this->query['limit'])) {
            return false;
        }

        if (!preg_match('~LIMIT (?:(?:(?P<offset>\d*) ?, ?)(?P<row_count>\d*)|(?:(?P<offset_row_count>\d*) OFFSET (?P<offset_offset>\d*))|(?P<only_row_count>\d*))~i', $this->query['limit'], $temp)) {
            return false;
        }

        if ($temp['only_row_count']) {
            return ['row_count' => $temp['only_row_count'], 'offset' => 0];
        }
        if ($temp['offset_row_count']) {
            return ['row_count' => $temp['offset_row_count'], 'offset' => $temp['offset_offset']];
        }
        if ($temp['row_count']) {
            return ['row_count' => $temp['row_count'], 'offset' => $temp['offset']];
        }

        return false;
    }

    public function setLimit($value)
    {
        $this->query['limit'] = 'LIMIT ' . $value;
    }

    public function setSelectOption(string $option): void
    {
        if (str_contains($this->query['select'], $option)) {
            return;
        }

        $this->query['select'] = preg_replace('~^SELECT~i', 'SELECT ' . $option . ' ', $this->query['select']);
    }

    public function getSelectFieldsBeforeMakro(): array
    {
        $tokens = $this->raw_query['select'];
        unset($tokens[0]);

        $tokens_new = [];
        $i = 0;
        foreach ($tokens as $token) {
            $token = trim($token);
            if (!$token) {
                continue;
            }

            if ($token === ',') {
                $i++;
                continue;
            }

            if (!isset($tokens_new[$i])) {
                $tokens_new[$i] = $token;
            } else {
                if ($token[0] !== '(') {
                    $tokens_new[$i] .= ' ';
                }
                $tokens_new[$i] .= $token;
            }
        }

        $tokens = $tokens_new;

        $fields = [];

        foreach ($tokens as $token) {
            $pos = strrpos($token, ' AS ');

            if ($pos !== false) {
                $temp1 = substr($token, 0, $pos);
                $temp2 = substr($token, $pos + 4);

                $token = [$temp1, $temp2];
            } else {
                $token = [$token];
            }

            if (isset($token[1])) {
                $alias = $token[1];
                $is_explicit_alias = true;
            } else {
                preg_match('~([-_a-z0-9]{1,})$~i', $token[0], $temp);

                $alias = $temp[1];
                $is_explicit_alias = false;
            }

            $fields[] = [
                'field' => $token[0],
                'alias' => trim($alias, '`'),
                'alias_is_explicit' => $is_explicit_alias,
            ];
        }

        return $fields;
    }

    public function addSelectField($field, $alias = '')
    {
        $sql = $field;
        if ($alias) {
            $sql .= ' AS ' . $alias;
        }
        $this->query['select'] .= ', ' . $sql;

        $this->raw_query['select'][] = ',';
        $this->raw_query['select'][] = $field;
        if ($alias) {
            $this->raw_query['select'][] = 'AS';
            $this->raw_query['select'][] = $alias;
        }
        $this->raw_query['select'][] = ' ';
    }

    public function replaceMakros(): void
    {
        $fields = $this->getSelectFieldsBeforeMakro();

        usort($fields, function ($a, $b) {
            $a = strlen($a['field']);
            $b = strlen($b['field']);

            if ($a === $b) {
                return 0;
            }

            return ($a > $b) ? -1 : 1;
        });

        foreach ($fields as $field) {
            if (!StringUtils::begins($field['field'], 'MAKRO.')) {
                continue;
            }

            $sql = $this->resolveMakro($field['field']);
            $this->query['select'] = str_replace($field['field'], $sql, $this->query['select']);
        }
    }

    protected function resolveMakro(string $marko_key): string
    {
        $parts = explode('.', $marko_key);

        $table = $parts[1];

        $config = null;

        $field_manager = field_manager::getInstance();

        if ($field_manager->isModelConfig($table)) {
            $config = $field_manager->getModelConfig($table)[$marko_key] ?? null;
        }

        if (!$config) {
            throw new \bqp\Exceptions\FatalException('unknown MAKRO (' . $marko_key . ')');
        }

        if (!isset($config['sql'])) {
            return '1'; //MAKROS ohne SQL -> es ist nicht ganz trivial die sauber aus dem SQL Query rauszubekommen, deswegen ersetzen wir die mit 1, da kommen dann ein paar 1 mehr von der DB zurück als nötig.
        }

        $sql = $config['sql'];
        if (is_array($sql)) {
            $sql = implode(',', $sql);
        }

        return $sql;
    }

    /**
     * gibt die verwendeten Tabellen aus der FROM-Klausel zurück.
     * - die Tabelle nicht der Alias
     * - inhalt von Subqueries wird ignoriert
     * @see getTables()
     * @return array
     */
    public function getTablesSimple(): array
    {
        $tables = $this->getTables();

        $result = [];

        foreach ($tables as $table) {
            if ($table['is_subquery']) {
                continue;
            }
            $result[] = $table['table'];
        }

        return $result;
    }

    /**
     * @return array<int, array{table: string, alias: ?string, is_subquery: bool}>
     */
    public function getTables(): array
    {
        if (!isset($this->raw_query['from'])) {
            return [];
        }

        $tokens = $this->raw_query['from'];
        $token_count = count($tokens);
        $tables = [];

        $is_whitespace = static function (string $token): bool {
            return trim($token) === '';
        };

        $is_subquery_token = static function (string $token): bool {
            $token = trim($token);
            if ($token === '') {
                return false;
            }

            $first = substr($token, 0, 1);
            $last = substr($token, -1, 1);

            if ($first === '(' && $last === ')') {
                return true;
            }

            if ($first === '{' && $last === '}') {
                return true;
            }

            return $first === '[' && $last === ']';
        };

        $is_alias_stop_token = static function (string $token): bool {
            $token = strtoupper(trim($token));
            if ($token === '') {
                return true;
            }

            return in_array(
                $token,
                [
                    ',',
                    'ON',
                    'USING',
                    'JOIN',
                    'STRAIGHT_JOIN',
                    'INNER',
                    'LEFT',
                    'RIGHT',
                    'FULL',
                    'CROSS',
                    'OUTER',
                    'NATURAL',
                    'WHERE',
                    'GROUP',
                    'ORDER',
                    'LIMIT',
                    'HAVING',
                    'UNION',
                    'EXCEPT',
                    'INTERSECT',
                ],
                true
            );
        };

        $expecting_table = false;

        for ($i = 0; $i < $token_count; $i++) {
            $token = $tokens[$i];
            if ($is_whitespace($token)) {
                continue;
            }

            $token_trimmed = trim($token);
            $token_upper = strtoupper($token_trimmed);

            if ($token_upper === 'FROM') {
                $expecting_table = true;
                continue;
            }

            if ($token_upper === ',' || $token_upper === 'JOIN' || $token_upper === 'STRAIGHT_JOIN') {
                $expecting_table = true;
                continue;
            }

            if (!$expecting_table) {
                continue;
            }

            $table = $token_trimmed;

            if (!$is_subquery_token($table)) {
                while (
                    ($i + 2) < $token_count
                    && trim($tokens[$i + 1]) === '.'
                    && !$is_whitespace($tokens[$i + 2])
                ) {
                    $table .= '.' . trim($tokens[$i + 2]);
                    $i += 2;
                }
            }

            $is_subquery = $is_subquery_token($table);
            $alias = null;

            $j = $i + 1;
            while ($j < $token_count && $is_whitespace($tokens[$j])) {
                $j++;
            }

            if ($j < $token_count) {
                $next_upper = strtoupper(trim($tokens[$j]));
                if ($next_upper === 'AS') {
                    $j++;
                    while ($j < $token_count && $is_whitespace($tokens[$j])) {
                        $j++;
                    }
                }

                if ($j < $token_count && !$is_alias_stop_token($tokens[$j])) {
                    $alias = trim($tokens[$j]);
                    $i = $j;
                }
            }

            $tables[] = [
                'table' => $table,
                'alias' => $alias,
                'is_subquery' => $is_subquery,
            ];

            $expecting_table = false;
        }

        return $tables;
    }


    public function setOrderBy($field, $direction = '')
    {
        $this->query['order'] = 'ORDER BY ' . $field . ' ' . $direction;
    }

    public function addTable($sql)
    {
        $this->query['from'] .= ' ' . $sql . ' ';
    }

    public function addWhere($sql)
    {
        $this->query['where'] .= ' AND ' . $sql . ' ';
    }

    public function getOrdersBy(): ?array
    {
        if (!empty($this->query['order'])) {
            $order = $this->query['order'];
        } else {
            return null;
        }

        $order = substr($order, 9);

        $replaces = [];

        $order = preg_replace_callback('~\(.*?\)~', function ($result) use (&$replaces) {
            $placeholder = '|||' . md5($result[0]) . '|||';
            $replaces[$placeholder] = $result[0];
            return $placeholder;
        }, $order);

        $order = explode(',', $order);

        $return = [];

        foreach ($order as $t) {
            $t = trim($t);
            if (preg_match('~^(.*) (ASC|DESC)$~i', $t, $r)) {
                $sub_result = ['order_by' => $r[1], 'order_by_dir' => strtoupper($r[2])];
            } else {
                $sub_result = ['order_by' => $t, 'order_by_dir' => 'ASC'];
            }

            $sub_result['order_by'] = strtr($sub_result['order_by'], $replaces);

            $return[] = $sub_result;
        }

        return $return;
    }

    public function getOrderBy(): ?array
    {
        $order_bys = $this->getOrdersBy();

        if ($order_bys) {
            return $order_bys[0];
        }
        return null;
    }

    /**
     * gibt für die typischen WWS Abfragen, die Tabellen aus dem GROUP BY zurück.
     * Setzt voraus 'tabelle.spalte'
     * @return array
     */
    public function getGroupByTablesWws(): array
    {
        if (!isset($this->raw_query['group'])) {
            return [];
        }

        $tokens = $this->raw_query['group'];
        $tokens = array_values(array_filter($tokens, fn(string $x) => trim($x) !== ''));
        unset($tokens[0], $tokens[1]);


        $tables = [];
        foreach ($tokens as $token) {
            if (preg_match('~^([-_a-z0-9]+)\.([-_a-z0-9]+)$~i', $token, $temp)) {
                $tables[] = $temp[1];
            }
        }
        return $tables;
    }
}
