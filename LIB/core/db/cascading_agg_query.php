<?php

use bqp\Utils\StringUtils;

class cascading_agg_query extends paging_query
{
    const EMPTY_DUMMY_ARRAY = 'dummy_array';
    const EMPTY_ARRAY = 'empty_array';

    public $config = [];
    private $mapping = [];
    protected $result_set = 'cascading_agg_result';
    protected $empty_array = 'dummy_array';

    public $org_limit = false;

    public function __construct($sql)
    {
        parent::__construct($sql);

        $this->setEntriesPerPage(10000);
    }

    public function getSqlQuery()
    {
        $this->replaceMakros();

        $i = 0;
        while ($this->query['select'] = preg_replace_callback('~\[(?<fields>[^\[]*)\] AS (?<alias>[-_.0-9a-z]*?)~Ui', [$this, 'replace_parser'], $this->query['select'])) {
            if ($i++ > 10) {
                break;
            }
        }

        $this->query['select'] = preg_replace('~,:::SUB:::(\S*)~', '', $this->query['select']);

        return parent::getSqlQuery();
    }

    public function replace_parser(array $cascade): string
    {
        $fields = [];
        $fields_org = $this->parseSelectFields($cascade['fields']);

        foreach ($fields_org as $field) {
            if (StringUtils::begins($field['field_select'], 'MAKRO.')) {
                $select = $this->resolveMakro($field['field_select']);

                $makro_fields = $this->parseSelectFields($select);
                foreach ($makro_fields as $field) {
                    $fields[] = $field;
                }
            } else {
                $fields[] = $field;
            }
        }

        $this->mapping[] = [
            'as' => $cascade['alias'],
            'fields' => array_column($fields, 'field')
        ];

        $sql_fields = implode(',', array_map(function ($field) {
            return "COALESCE(" . $field . ",'')";
        }, array_column($fields, 'field_select')));

        $sql = "GROUP_CONCAT(CONCAT_WS('|#|', " . $sql_fields . ") SEPARATOR '|##|') AS " . $cascade['alias'];

        return $sql;
    }

    private function parseSelectFields(string $select_field): array
    {
        $fields = [];

        $fields_temp = explode(',', $select_field);
        foreach ($fields_temp as $raw_field) {
            $raw_field = trim($raw_field);

            $field = '';
            $field_select = '';
            if (preg_match('~(.*) AS (\S*)~', $raw_field, $temp)) {
                $field = $temp[2];
                $field_select = $temp[1];
            } else {
                $field_select = $raw_field;

                $pos = strrpos($raw_field, '.');
                if ($pos !== false) {
                    $field = substr($raw_field, $pos + 1);
                } else {
                    $field = $raw_field;
                }
            }

            if (preg_match('~:::SUB:::(\S*)~', $raw_field, $temp)) {
                $field = $temp[1];
            }

            $fields[] = [
                'field' => $field,
                'field_select' => $field_select
            ];
        }

        return $fields;
    }

    public function getMapping()
    {
        return $this->mapping;
    }

    public function get()
    {
        return 'cascading_result';
    }

    public function beforeExecute($db)
    {
        static $must_executed = true;

        if ($must_executed) {
            $db->query('SET @@group_concat_max_len = 20480');
            $must_executed = false;
        }
    }

    /**
     * @return string
     */
    public function getEmptyArray()
    {
        return $this->empty_array;
    }

    /**
     * @param string $empty_result
     */
    public function setEmptyArray($empty_result)
    {
        $this->empty_array = $empty_result;
    }
}

class cascading_agg_result extends paging_result
{
    public function __construct(query $query, $result, $db_handle)
    {
        parent::__construct($query, $result, $db_handle);

        foreach ($query->getMapping() as $mapping) {
            $this->addMappingCallback($mapping['as'], function ($daten) use ($query, $mapping) {
                $result = [];

                if (!$daten[$mapping['as']]) {
                    if ($query->getEmptyArray() == 'dummy_array') {
                        foreach ($mapping['fields'] as $key => $field) {
                            $result[$field] = null;
                        }

                        return [$result];
                    } else {
                        return [];
                    }
                }

                $lines = explode('|##|', $daten[$mapping['as']]);

                foreach ($lines as $line) {
                    $values = explode('|#|', $line);

                    $t = [];
                    foreach ($mapping['fields'] as $key => $field) {
                        $t[$field] = $values[$key];
                    }

                    $result[] = $t;
                }

                return $result;
            });
        }
    }
}
