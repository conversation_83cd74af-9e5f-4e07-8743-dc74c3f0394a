<?php

/** @noinspection PhpFullyQualifiedNameUsageInspection */

namespace bqp\controller;

use bqp\Exceptions\DevException;
use bqp\RequestHandler;
use bqp\table\TableObjectFieldSelectMask;
use service_loader;
use session_handler;

class front_controller
{
    /**
     * @var string
     */
    protected $controller_url = '';

    /**
     * @var null|front_controller_action
     */
    protected $called_action = null; //@todo container um parameter von einer action in ein view zu transportieren -> das muss weg

    /**
     * @var string|null
     */
    protected $called_view = null; //$called_view und $view_to_call sind augenscheinlich redundant...
    protected $view_to_call = null; //wird von ctro gesetzt und über run() ausgeführt

    protected $action_to_call = null; //wird von ctro gesetzt und über run() ausgeführt

    protected RequestHandler $request;

    /**
     * Session Handler
     */
    protected session_handler $session;

    /**
     * parameter die an die url angehangen werden sollen createViewUrl();
     */
    protected array $default_parameter = [];

    private ControllerArgumentResolver $argument_resolver;

    public function __construct()
    {
        $this->request = RequestHandler::fromGlobals();
        $this->session = session_handler::getInstance();

        $di_container = service_loader::getDiContainer();

        $this->argument_resolver = $di_container->get(ControllerArgumentResolver::class);
        $this->argument_resolver->addDiContainer($di_container);
        $this->argument_resolver->addClassResolver(RequestHandler::class, fn() => $this->request);
        $this->argument_resolver->addClassResolver(\Symfony\Component\HttpFoundation\Request::class, function () {
            return \Symfony\Component\HttpFoundation\Request::createFromGlobals();
        });

        $this->init_extend();
    }

    /**
     * legt fest welcher view bei run() aufgerufen werden soll
     * @param string $view
     */
    public function setViewToCall(string $view): void
    {
        $this->view_to_call = $view;
        $this->called_view = $view;
    }

    public function setActionToCall(?string $action): void
    {
        $this->action_to_call = $action;
    }

    public function setControllerUrl(string $url): void
    {
        $this->controller_url = $url;
    }

    public function run()
    {
        if (!$this->initPreExecution()) {
            return;
        }

        $this->init();

        if ($this->action_to_call) {
            $this->callAction($this->action_to_call);
        } else {
            $this->callView($this->view_to_call);
        }
    }

    public function init()
    {
    }

    public function init_extend(): void
    {
    }

    /**
     * wird als erstes in run() ausgeführt
     * Kann:
     * -> views und actions überschreiben
     * -> ausführung abbrechen (return false)
     * -> den state des controllers manipulieren
     *
     * @return bool
     */
    public function initPreExecution(): bool
    {
        return true;
    }

    public function redirectView($view, $paramter)
    {
        throw new DevException('nicht implementiert');
    }

    /**
     * @param string|null $view
     * @param string|array $parameters
     * @throws FrontControllerException
     */
    public function callView($view = null, $parameters = 'request')
    {
        if (!$view) {
            $view = $this->called_view;
        }

        $this->called_view = $view;

        $method = $this->getMethodToCall($view);

        if ($parameters === 'request') {
            $parameters = &$_REQUEST;
        } elseif (!is_array($parameters)) {
            throw new DevException('callView $parameters must be a array or "request"');
        }

        $call_parameters = $this->argument_resolver->resolve($this, $method, $parameters);

        $result = call_user_func_array([$this, $method], $call_parameters);

        $this->handleViewResult($result);
    }

    /**
     * @param string $view
     * @return string
     * @throws FrontControllerException
     */
    protected function getMethodToCall(string $view): string
    {
        $view = str_replace(['.', '-'], '_', $view);

        if (substr($view, 0, 6) === 'table_' && method_exists($this, $view)) {
            //sonderbehandlung für table_views...
        } else {
            $view = 'view_' . $view;
        }

        if (!method_exists($this, $view)) {
            throw new FrontControllerException('View existiert nicht.');
        }

        return $view;
    }

    /**
     * @param mixed $result
     */
    protected function handleViewResult($result): void
    {
        if ($result instanceof \bqp\table\TableObject) {
            if (isset($_REQUEST['table_field_select']) && $result->isFieldSelectEnabled()) {
                $mask = new TableObjectFieldSelectMask($result);
                echo $mask->render();
            }
            echo $result->render();
            return;
        }
    }

    private function callAction(string $action_name): void
    {
        $extra_parameters = [];

        //doppelpunkt syntax bei action call. Wenn an der Action ein doppeltpunkt dran hängt, dann wird der der Action Methode als erster Parameter übergeben
        if (strpos($action_name, ':')) {
            list($action_name, $action_parameter) = explode(':', $action_name, 2);
            $extra_parameters['action_parameter'] = $action_parameter;
        }

        $action = 'action_' . $action_name;

        if (!method_exists($this, $action)) {
            throw new FrontControllerException('Action existiert nicht. "' . $action . '"');
        }

        $this->called_action = new front_controller_action($action_name);

        $extra_parameters['action'] = $this->called_action;

        $call_parameters = $this->argument_resolver->resolve($this, $action, $_REQUEST, $extra_parameters);

        call_user_func_array([$this, $action], $call_parameters);
    }


    public function setDefaultParameters($parameters)
    {
        $this->default_parameter = $parameters;
    }

    public function addDefaultParameter($key, $value)
    {
        $this->default_parameter[$key] = $value;
    }

    public function getDefaultParameters()
    {
        return $this->default_parameter;
    }

    public function getDefaultParameter($key)
    {
        return $this->default_parameter[$key];
    }

    public function createViewUrl($view = false, $parameters = '', $remove = [])
    {
        die('abstract');
    }

    /**
     * @return RequestHandler
     * @deprecated -> wird an einer Stelle im System verwendet, um eine Weiterleitung auf ein view zu machen
     * und dabei die request Parameter zu überschreiben. Das ist unsinnig... ein normales redirect mit entsprechenden
     * Parametern wäre sauberer.
     */
    public function getRequestHandler(): RequestHandler
    {
        return $this->request;
    }
}
