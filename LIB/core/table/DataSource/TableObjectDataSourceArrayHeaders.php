<?php

namespace bqp\table\DataSource;

use bqp\table\field\table_object_field;

class TableObjectDataSourceArrayHeaders extends TableObjectDataSourceArray implements DataSourceWithHeader
{
    public function getHeaders()
    {
        $table_fields = [];

        if ($this->daten) {
            $first = current($this->daten);
            foreach ($first as $key => $null) {
                $table_fields[] = new table_object_field($key, $key);
            }
        }
        return $table_fields;
    }
}
