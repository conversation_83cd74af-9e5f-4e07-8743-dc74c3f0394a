<?php

namespace bqp\table\DataSource;

use bqp\field_manager;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_inline_table_by_array;
use manipulation_query;

class DataSourceHeaderGeneratorSqlWws implements DataSourceHeaderGeneratorSql
{

    public function getHeaders(TableObjectDataSourceWws $data_source): array
    {
        $table_fields = [];

        $selected_fields = $data_source->getQuery()->getSelectFieldsBeforeMakro();

        foreach ($selected_fields as $field) {
            if (strpos($field['field'], '[') === 0) {
                $container_field = new table_object_field_inline_table_by_array($field['alias'], $field['alias']);

                $select = 'SELECT ' . trim($field['field'], '[]');

                $query = new manipulation_query($select);

                $inner_fields = $query->getSelectFieldsBeforeMakro();

                foreach ($inner_fields as $inner_field) {
                    $temp = $this->getField($inner_field['field'], $inner_field['alias']);

                    if ($temp) {
                        $container_field->addField($temp);
                    }
                }

                $table_fields[] = $container_field;
            } else {
                $temp = $this->getField($field['field'], $field['alias'], $field['alias_is_explicit']);

                if ($temp) {
                    $table_fields[] = $temp;
                }
            }
        }

        return $table_fields;
    }

    public function getField(string $field_key, ?string $alias = null, bool $alias_is_explicit = false): table_object_field
    {
        if (!$alias) {
            $alias = substr($field_key, strrpos($field_key, '.') + 1);
        }
        $alias = trim($alias, '`');

        $field = null;

        $field_manager = field_manager::getInstance();

        if ($field_manager->isFieldKey($field_key)) {
            $field = $field_manager->getAsTableField($field_key, $alias);
        }

        if (!$field) {
            // wenn das Feld nicht im field_manager bekannt ist, dann nehmen wir als Key den expliziten ALIAS aus der Abfrage. Wenn es kein Alias gibt, dann nehmen wir den vollen Name (table.column und setzen column als alias)
            if ($alias_is_explicit) {
                $field = new table_object_field($alias, $alias);
                $field->setDataKey($alias);
            } else {
                $field = new table_object_field($field_key, $alias);
                $field->setDataKey($alias);
            }
        }

        $field->setMetadata('field_key', $field_key);
        $field->setMetadata('field_alias', $alias);

        return $field;
    }
}
