<?php

namespace bqp\table\DataSource;

use bqp\db\db_generic;

class TableObjectDataSourceWwsMerge extends TableObjectDataSourceWws implements DataSource
{
    protected string $relation_field;
    protected array $data_sources;

    protected string $prefix;
    protected bool $drop_relation_field;

    /**
     * @param \bqp\db\db_generic $db
     * @param string $sql
     * @param string $relation_field
     * @param \bqp\table\DataSource\TableObjectDataSourceWws[] $data_sources
     */
    public function __construct(db_generic $db, string $sql, string $relation_field = 'id', array $data_sources = [], string $prefix = '', bool $drop_relation_field = true)
    {
        parent::__construct($db, $sql);
        $this->relation_field = $relation_field;

        $this->data_sources = $data_sources;

        $this->prefix = $prefix;
        $this->drop_relation_field = $drop_relation_field;
    }

    public function setEntriesPerPage($entries_per_page): void
    {
        parent::setEntriesPerPage($entries_per_page);
        foreach ($this->data_sources as $data_source) {
            $data_source->setEntriesPerPage($entries_per_page * 2);
        }
    }

    public function getDaten(): array
    {
        $daten = [];

        $parent_rows = parent::getDaten();

        $relation_data_key = $this->relation_field;
        $first_parent_row = $parent_rows[0] ?? null;
        if ($first_parent_row && !array_key_exists($relation_data_key, $first_parent_row)) {
            foreach (parent::getHeaders() as $header) {
                if ($header->getKey() === $this->relation_field) {
                    $relation_data_key = $header->getDataKey();
                    break;
                }
            }
        }

        foreach ($parent_rows as $row) {
            $daten[$row[$relation_data_key]] = $row;
        }

        if (!count($daten)) {
            return [];
        }

        foreach ($this->data_sources as &$data_source) {
            $data_source->getQuery()->addWhere($this->relation_field . ' IN (' . $data_source->getDb()->in(array_keys($daten)) . ')');

            $data = $data_source->getDaten();

            $child_relation_data_key = $this->relation_field;
            $first_child_row = $data[0] ?? null;
            if ($first_child_row && !array_key_exists($child_relation_data_key, $first_child_row)) {
                foreach ($data_source->getHeaders() as $header) {
                    if ($header->getKey() === $this->relation_field) {
                        $child_relation_data_key = $header->getDataKey();
                        break;
                    }
                }
            }

            foreach ($data as $row) {
                $relation_value = $row[$child_relation_data_key] ?? null;
                if ($relation_value === null || !isset($daten[$relation_value])) {
                    continue;
                }

                $daten[$relation_value] = array_merge($daten[$relation_value], $row);
            }

            foreach ($data_source->getHeaders() as $header) {
                foreach ($daten as $i => $row) {
                    $data_key = $header->getDataKey();

                    if (!isset($row[$data_key])) {
                        $daten[$i][$data_key] = '';
                    }
                }
            }
        }

        return array_values($daten);
    }

    public function getHeaders(): array
    {
        $headers = parent::getHeaders();
        if ($this->prefix) {
            foreach ($headers as $header) {
                $header->setName($this->prefix . ' - ' . $header->getName());
            }
        }

        foreach ($this->data_sources as $name => $data_source) {
            $add_headers = $data_source->getHeaders();
            foreach ($add_headers as $i => $header) {
                $header->setName($name . ' - ' . $header->getName());
                if (($header->getKey() == $this->relation_field || $header->getDataKey() == $this->relation_field) && $this->drop_relation_field) {
                    unset($add_headers[$i]);
                }
                $header->setOrderable(false);
            }
            $headers = array_merge($headers, $add_headers);
        }
        return $headers;
    }
}
