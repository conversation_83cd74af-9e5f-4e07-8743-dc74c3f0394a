<?php

namespace bqp\table\DataSource;

use bqp\db\db_generic;
use bqp\field_manager;
use bqp\table\field\table_object_field;
use cascading_agg_query;
use paging_query;

class TableObjectDataSourceWws extends TableObjectDataSourceBase implements DataSourceWithHeader, DataSourceExplainable, DataSourceFieldAware
{
    protected db_generic $db;
    protected string $sql;
    protected paging_query $query;

    protected int $total_row_count = 0;

    protected ?DataSourceHeaderGeneratorSql $header_generator = null;

    protected array $field_select_tables = [];

    /**
     * @param db_generic $db
     * @param string $sql
     */
    public function __construct(db_generic $db, $sql)
    {
        $this->db = $db;
        $this->sql = $sql;

        $this->query = new cascading_agg_query($this->sql);

        $this->header_generator = new DataSourceHeaderGeneratorSqlWws();

        //@todo eigentlich sollte das auch als default funktionieren
        $this->addFieldSelectFromQueryTables();
    }

    /**
     * @return paging_query
     */
    public function getQuery()
    {
        return $this->query;
    }

    public function getDaten()
    {
        if ($this->rollup_complete) {
            $daten = $this->getDatenWithRollup();
        } else {
            $this->query->setEntriesPerPage($this->entries_per_page);
            $this->query->setActPage($this->act_page);

            if ($this->order_by) {
                $this->query->setOrderBy($this->order_by, $this->order_by_dir);
            }

            $result = $this->db->execute($this->query);

            $this->total_row_count = $result->getFoundRows();

            $daten = $result->asArray();
        }

        $daten = $this->handleDataCallbacks($daten);

        $this->rollup_daten = $this->rollupDaten($daten, 'part');

        return $daten;
    }

    public function getDatenWithRollup()
    {
        $rollup_limit = 1000000;

        $this->query->setEntriesPerPage($rollup_limit);
        $this->query->setActPage(0);

        if ($this->order_by) {
            $this->query->setOrderBy($this->order_by, $this->order_by_dir);
        }

        $result = $this->db->execute($this->query);

        $this->total_row_count = $result->getFoundRows();

        $daten = $result->asArray();

        $this->rollup_complete_daten = $this->rollupDaten($daten, 'complete');

        //mitigation: wenn es vermeintlich weitere Datensätze gibt, dann ist das rollup falsch. -> wir entfernen die falschen Daten
        if (count($daten) === $rollup_limit) {
            $this->rollup_complete_daten = null;
        }

        $daten = array_slice($daten, $this->act_page * $this->entries_per_page, $this->entries_per_page, true);

        return $daten;
    }

    public function getTotalRowCount()
    {
        return $this->total_row_count;
    }

    public function setHeaderGenerator(?DataSourceHeaderGeneratorSql $header_generator): void
    {
        $this->header_generator = $header_generator;
    }

    public function getHeaders()
    {
        if (!$this->header_generator) {
            return [];
        }

        $headers = $this->header_generator->getHeaders($this);

        $headers = $this->addOptionalHeaders($headers);

        return $headers;
    }

    private function addOptionalHeaders(array $headers): array
    {
        if (!$this->field_select_tables) {
            return $headers;
        }

        $field_manager = field_manager::getInstance();

        //Wir sammeln hier alle Felder zusammen die noch nicht bekannt sind.
        //Diese Felder werden immer mit einem alias "tabelle.spalte" hinzugefügt um kollision zu vermeiden. Gleichzeitig
        //wird in den Metadaten schon das hinterlegt, was zum erweitern der Abfrage notwendig ist. Das spart den
        //Step in processHeaders()
        foreach ($this->field_select_tables as $table) {
            if (!$field_manager->isModelConfig($table)) {
                continue;
            }

            $field_keys = $field_manager->getTableObjectSelectableFieldKeys($table);

            foreach ($field_keys as $field_key) {
                foreach ($headers as $header) {
                    if ($header->getKey() === $field_key) {
                        continue 2;
                    }
                }

                if (str_starts_with($field_key, 'MAKRO.')) {
                    $alias = '';
                    $sql = $field_key;
                } else {
                    $alias = $field_key;
                    $sql = $field_key . ' AS `' . $field_key . '`';
                }

                $header = $this->header_generator->getField($field_key, $alias);
                $header->setDefaultHidden(true);
                $header->setMetadata('select_sql', $sql);

                $headers[] = $header;
            }
        }

        return $headers;
    }

    public function getOrderBy()
    {
        if ($this->order_by) {
            return [
                'order_by' => $this->order_by,
                'order_by_dir' => $this->order_by_dir
            ];
        }

        $order_by = $this->query->getOrderBy();

        return $order_by;
    }

    public function explain(): string
    {
        $sql = $this->query->getSqlQuery();

        return $sql . "\n\n\n-----\n\n\n" . \bqp\db\SqlFormater::formatSqlAsText($sql);
    }

    public function getDb(): db_generic
    {
        return $this->db;
    }


    public function addFieldSelectByTable(string $table): void
    {
        $this->field_select_tables[$table] = $table;
    }

    public function addFieldSelectFromQueryTables(): void
    {
        $this->addFieldSelectByTable('global');

        //wenn der query gruppiert ist, dann nur automatisch die Tabellen nehmen in dem GROUP BY
        //->das ist sehr naiv implementiert...
        $tables = $this->query->getGroupByTablesWws();
        if ($tables) {
            foreach ($tables as $table) {
                $this->addFieldSelectByTable($table);
            }
            return;
        }

        foreach ($this->query->getTablesSimple() as $table) {
            $this->addFieldSelectByTable($table);
        }
    }

    public function clearFieldSelectTables(): void
    {
        $this->field_select_tables = [];
    }

    /**
     * @param table_object_field[] $fields
     */
    public function processFields(array $fields): void
    {
        foreach ($fields as $field) {
            $select_sql = $field->getMetadata('select_sql');
            if ($select_sql) {
                $this->query->addSelectField($select_sql);
            }
        }
    }
}
