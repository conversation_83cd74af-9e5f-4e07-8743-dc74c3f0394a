<?php

namespace bqp\table;

use bqp;
use bqp\table\DataSource\DataSource;
use bqp\table\DataSource\DataSourceExplainable;
use bqp\table\DataSource\DataSourceFieldAware;
use bqp\table\DataSource\DataSourceWithHeader;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_multi;
use bqp\table\Renderer\TableObjectRenderer;
use bqp\table\Renderer\TableObjectRendererHtmlInteractive;
use config;
use env;

class TableObject
{
    use bqp\table\field\TableObjectFieldContainer;
    use TableObjectParameterTrait;

    public const MULTIDIMENSIONAL_MODE_EMPTY = 'empty';
    public const MULTIDIMENSIONAL_MODE_ROWSPAN = 'rowspan';

    protected DataSource $data_source;

    protected ?array $daten = null;


    protected $attributes = [ //html renderer
        'id' => 'table_object',
        'class' => 'table_normal',
    ];
    protected $caption = '';
    protected $caption_extra = '';

    /**
     * Inhalt des Footers. Wenn $navigation aktiv ist, wird der aber übeschreiben
     * @var string
     */
    protected $footer_content = '';  //html renderer?

    /**
     * @var bool
     */
    protected $render_footer = true;

    /**
     * legt fest ob die navigation erzeugt werden soll
     * @var bool
     */
    protected $render_navigation = true;


    protected $render_rollup_interim_result = true;
    protected $extra_javascript = false; //html renderer

    /**
     * Flag um zu steuern ob die Tabelle automatisch auf Seite 1 springen soll falls auf der aktuellen Seite keine Einträge sind
     * @var bool
     */
    protected bool $avoid_empty_pages = true;

    protected int $entries_per_page_default = 100;
    protected ?int $entries_per_page = null;
    protected array $entries_per_page_options = [25, 50, 100, 250, 500, 1000, 2500];

    protected int $act_page = 0;
    protected $order_by = '';
    protected $order_by_dir = 'ASC';
    protected $order_by_default = null; //wenn in der Datenquelle eine default sortierung vorgegeben ist wird die hier gespeichert um zu prüfen ob die sortierung erlaubt ist (da nicht unbedingt als sortierbares feld vorhanden)
    protected $order_able = true;

    protected bool $export_enabled = false;
    protected bool $export_do = false;
    protected string $export_type = 'csv';
    protected string $export_download_snippet = '';

    protected bool $explain_data_source = false;
    protected bool $do_explain_datasource = false;

    protected $row_formater = [];

    protected $group_header = null;

    protected $inbody_header = null;

    protected $url = '';

    protected TableObjectRenderer $renderer;

    protected $inner_envelop_callback; //html renderer

    /**
     * @var ExtraRow[]
     */
    protected array $extra_rows = [];

    /**
     * steuert ob die Tabelle eine Durckoption hat
     * @var bool
     */
    protected bool $print = false;

    /**
     * legt fest, ob rowspan oder leere Zellen gerendert werden, wenn Felder in Zeilen aus mehr als einer Zelle bestehen
     * empty -> renderEmpty()
     * rowspan -> rendert die Zelle mit einem rowspan
     * @var string
     */
    protected string $multidimensonal_mode = self::MULTIDIMENSIONAL_MODE_EMPTY;

    protected array $highlight_phrases = [];


    protected bool $field_select_enabled = true;
    protected ?array $field_select = null;

    public function __construct(DataSource $daten)
    {
        $this->renderer = new TableObjectRendererHtmlInteractive($this);

        $this->data_source = $daten;

        if ($this->data_source instanceof DataSourceWithHeader) {
            $this->fields = $this->data_source->getHeaders();

            foreach ($this->fields as $field) {
                $field->setHtmlTable($this);
            }
        }

        //wenn in der datenquelle eine sortierung voreingestellt und noch keine sortierung gesetzt ist die aus der datenquelle übernhemen
        $order_by = $this->data_source->getOrderBy();
        if ($order_by) {
            $this->order_by_default = $order_by['order_by'];
        }

        $temp = $this->getOrderBy();
        if (!$temp['field']) {
            if ($order_by) {
                $this->setOrderBy($order_by['order_by'], $order_by['order_by_dir']);
            }
        }

        //@todo... gehört hier alles nicht rein
        if (isset($_REQUEST['export_as'])) {
            $this->export_do = true;
            $this->export_type = $_REQUEST['export_as'];
        }

        if (isset($_REQUEST['explain_datasource'])) {
            $this->do_explain_datasource = true;
        }

        $user = env::getUserSession();
        if ($user && $user->hasRight('rights.dev')) {
            $this->explain_data_source = true;
        }
        //...
    }

    public function eventBeforeRender(): void
    {
        $this->loadDaten();

        foreach ($this->getFields() as $field) {
            $field->eventBeforeRender();
        }

        $this->renderer->beforeRender();
    }

    // <editor-fold defaultstate="collapsed" desc="Renderer">
    public function setRenderer(TableObjectRenderer $renderer): void
    {
        $this->renderer = $renderer;
    }

    public function getRenderer(): TableObjectRenderer
    {
        return $this->renderer;
    }

    public function __toString()
    {
        return $this->render();
    }

    public function display(): void
    {
        echo $this->render();
    }

    public function render(): string
    {
        return $this->renderer->render();
    }

    /**
     * @deprecated automatisch Felder generieren so nicht mehr erwünscht -> über Datenquelle abbilden
     * @todo https://allego.myjetbrains.com/youtrack/issue/WWS-1218
     */
    public function generateFields()
    {
        $this->loadDaten();

        $daten = current($this->daten);
        if ($daten) {
            foreach ($daten as $key => $value) {
                $this->fields[$key] = new table_object_field($key, $key);
                $this->fields[$key]->setTable($this);
            }
        }
    }

    public function makeNavigationUrl(int $page): string
    {
        return $this->makeUrl('filter[page]=' . $page);
    }

    public function makeUrl($settings)
    {
        return str_replace('{{$settings}}', 'action=set_filter&' . $settings, $this->url);
    }

    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Settings Rnder spezifisch">
    public function setId($id)
    {
        $this->attributes['id'] = $id;
    }

    public function getId()
    {
        return $this->attributes['id'];
    }

    public function setClass($class)
    {
        $this->attributes['class'] = $class;
    }

    public function addClass($class)
    {
        if (!isset($this->attributes['class'])) {
            $this->attributes['class'] = '';
        } else {
            $this->attributes['class'] .= ' ';
        }

        $this->attributes['class'] .= $class;
    }

    public function setStyle($style)
    {
        $this->attributes['style'] = $style;
    }

    public function setCaption($caption)
    {
        $this->caption = $caption;
    }

    public function setCaptionExtra($extra)
    {
        $this->caption_extra = $extra;
    }

    public function getCaption()
    {
        return $this->caption;
    }

    public function setFooterContent(string $footer_content): void
    {
        $this->footer_content = $footer_content;
    }

    public function setRenderFooter(bool $status): void
    {
        $this->render_footer = $status;
    }

    public function isRenderFooter(): bool
    {
        return $this->render_footer;
    }

    public function getFooterContent(): string
    {
        return (string)$this->footer_content;
    }

    public function setRenderNavigation(bool $value): void
    {
        $this->render_navigation = $value;
    }

    public function isRenderNavigation(): bool
    {
        return $this->render_navigation;
    }

    /**
     * legt fest, ob rowspan oder leere Zellen gerendert werden, wenn Felder in Zeilen aus mehr als einer Zelle bestehen
     * empty/rowspan
     * @see self::MULTIDIMENSIONAL_MODE_EMPTY
     * @see self::MULTIDIMENSIONAL_MODE_ROWSPAN
     * @param string $multidimensonal_mode
     * @return void
     */
    public function setMultidimensonalMode(string $multidimensonal_mode): void
    {
        $this->multidimensonal_mode = $multidimensonal_mode;
    }

    public function getMultidimensonalMode(): string
    {
        return $this->multidimensonal_mode;
    }

    public function setRowFormater($callback)
    {
        $this->row_formater = [$callback];
    }

    public function addRowFormater($callback)
    {
        $this->row_formater[] = $callback;
    }

    public function loadRowFormaterCheckbox()
    {
        $this->addRowFormater(function () {
            return [
                'onclick' => "if(event.originalTarget.tagName == 'TD') { var temp = $(this).find('input[type=checkbox]')[0]; temp.checked = !temp.checked; temp.dispatchEvent(new window.Event('change', { bubbles: true })); }",
                /*'style' => 'cursor: pointer;'*/
            ];
        });
    }

    /**
     * @param string $field
     * @param mixed $value
     * @param string $class
     */
    public function addRowFormaterHighlight(string $field, $value, string $class = 'yellow'): void
    {
        $this->addRowFormater(function ($row) use ($field, $value, $class) {
            if ($row[$field] == $value) {
                return ['class' => $class];
            }

            return null;
        });
    }


    public function setAttribute($attribute, $value)
    {
        $this->attributes[$attribute] = $value;
    }

    public function addExtraJavascript($javascript)
    {
        $this->extra_javascript .= $javascript;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function getInnerEnvelopCallback()
    {
        return $this->inner_envelop_callback;
    }

    public function getExtraJavascript(): string
    {
        return (string)$this->extra_javascript;
    }

    public function getHighlightPhrases(): array
    {
        return $this->highlight_phrases;
    }

    public function getRowFormater(): array
    {
        return $this->row_formater;
    }



    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Settings">

    public function setEntriesPerPageOptions(array $options): void
    {
        $this->entries_per_page_options = $options;
    }

    public function getEntriesPerPageOptions(): array
    {
        return $this->entries_per_page_options;
    }

    public function setEntriesPerPage($entries_per_page)
    {
        $this->entries_per_page = $entries_per_page;
    }

    public function setEntriesPerPageDefault($entries_per_page)
    {
        $this->entries_per_page_default = $entries_per_page;
    }

    public function getEntriesPerPage()
    {
        return $this->entries_per_page ?: $this->entries_per_page_default;
    }

    public function setUrl($url)
    {
        $this->url = $url;
    }

    public function setPage($page)
    {
        $this->act_page = $page;
    }

    public function getPage()
    {
        return $this->act_page;
    }

    public function setOrderBy($field, $direction = 'ASC')
    {
        $this->order_by = $field;
        $this->order_by_dir = $direction;
    }

    public function getOrderBy()
    {
        return ['field' => $this->order_by, 'direction' => $this->order_by_dir];
    }

    public function setExportEnabled(bool $export_enabled): void
    {
        $this->export_enabled = $export_enabled;
    }

    public function isExportEnabled(): bool
    {
        return $this->export_enabled;
    }

    public function isExportOngoing(): bool
    {
        return $this->export_do;
    }

    public function getExportDownloadSnippet(): string
    {
        return $this->export_download_snippet;
    }

    public function setPrint($status): void
    {
        $this->print = (bool)$status;
    }

    public function isPrint()
    {
        return $this->print;
    }

    public function setOrderable($value)
    {
        $this->order_able = $value;
    }

    public function isOrderable()
    {
        return $this->order_able;
    }

    public function setInnerEnvelopCallback($callback): void
    {
        $this->inner_envelop_callback = $callback;
    }

    public function setTableForm(\bqp\table\Form\TableObjectForm $form): void
    {
        $this->setInnerEnvelopCallback([$form, 'render']);
    }

    public function setRenderRollupInterimResult($value)
    {
        $this->render_rollup_interim_result = $value;
    }

    public function isRenderRollupInterimResult(): bool
    {
        return $this->render_rollup_interim_result;
    }

    /**
     * gibt alle extra rows gruppiert nach position und sortiert nach prio zurück
     * @return array
     */
    public function getExtraRowsGrouped()
    {
        $result = [
            ExtraRow::LOCATION_BEFORE => [],
            ExtraRow::LOCATION_AFTER => [],
            ExtraRow::LOCATION_TOP => [],
            ExtraRow::LOCATION_BOTTOM => []
        ];

        foreach ($this->getExtraRows() as $extra_row) {
            $result[$extra_row->getLocation()][] = $extra_row;
        }

        return $result;
    }

    /**
     * gibt alle extra rows zurück
     * @return ExtraRow[]
     */
    public function getExtraRows()
    {
        return $this->extra_rows;
    }

    public function addExtraRow(ExtraRow $extra_row)
    {
        $this->extra_rows[] = $extra_row;
    }

    public function addGroupHeaderByField($string, $callback = true)
    {
        $this->group_header = $string;
    }

    public function setInbodyHeader(int $per_rows): void
    {
        $this->inbody_header = $per_rows;
    }

    public function getGroupHeaderField()
    {
        return $this->group_header;
    }

    public function getInbodyHeader()
    {
        return $this->inbody_header;
    }

    // </editor-fold>


    /**
     * @return table_object_field[]
     */
    public function getFields(): array
    {
        if (!$this->fields) {
            throw new bqp\Exceptions\DevException('table object has no fields defined.');
        }

        foreach ($this->fields as $field) {
            $field->setHtmlTable($this);
        }

        return $this->fields;
    }

    /**
     * @return table_object_field[]
     */
    public function getFieldsForRender(): array
    {
        $fields = [];

        if (isset($this->field_select) && $this->field_select_enabled) {
            foreach ($this->field_select as $field_key) {
                $field = $this->tryGetFieldByKey($field_key);
                if ($field) {
                    $fields[] = $field;
                }
            }

            return $fields;
        }

        foreach ($this->getFields() as $field) {
            if ($field->isDefaultHidden()) {
                continue;
            }

            $fields[] = $field;
        }

        return $fields;
    }

    public function isFieldSelectEnabled(): bool
    {
        return $this->field_select_enabled;
    }

    public function setFieldSelectEnabled(bool $field_select_enabled): void
    {
        $this->field_select_enabled = $field_select_enabled;
    }

    public function setFieldSelect(?array $field_select): void
    {
        $this->field_select = $field_select;
    }

    public function getDaten(): array
    {
        if ($this->daten === null) {
            $this->loadDaten();
        }

        return $this->daten;
    }

    // <editor-fold defaultstate="collapsed" desc="Datenverwaltung">
    protected function loadDaten(): void
    {
        $fields = $this->getFieldsForRender();

        if ($this->data_source instanceof DataSourceFieldAware && $this->isFieldSelectEnabled() && $this->field_select) {
            $this->data_source->processFields($fields);
        }

        if (!$this->checkOrderBy($this->order_by)) {
            //exception macht keinen Sinn mehr. Wir haben immer mehr tabelle mit dynamischen Spalten...
            //throw new TableObjectUnknownOrderByException('unknown order by "' . $this->order_by . '"');

            //auf default setzen (auch nicht optimal, aber macht hier erstmal kein ärger mehr.)
            $this->setOrderBy(false);
        }

        if ($this->export_enabled && $this->export_do) {
            ini_set('memory_limit', '2048M');
            $this->data_source->setEntriesPerPage(500_000);

            $this->data_source->setPage(0);
            $this->data_source->setOrderBy($this->order_by, $this->order_by_dir);

            $this->daten = $this->data_source->getDaten();

            $id = rand(10000, 99999);

            $temp_file =  config::getLegacy('system')->temp_dir . '/table_object_' . $id . '.raw';

            $filename_download = bqp\Utils\StringUtils::slug($this->getCaption());
            if (!$filename_download) {
                $filename_download = 'table';
            }

            switch ($this->export_type) {
                case 'csv':
                    $renderer = new \bqp\table\Renderer\TableObjectRendererCsv($this);
                    $filename_download .= '.csv';
                    break;
                case 'xlsx':
                    $renderer = new \bqp\table\Renderer\TableObjectRendererPhpSpreadsheet($this);
                    $filename_download .= '.xlsx';
                    break;
                default:
                    throw new bqp\Exceptions\FatalException();
            }

            $renderer->beforeRender();

            file_put_contents($temp_file, $renderer->renderInTableObjectContext());
            $secret = md5_file($temp_file);

            $this->export_download_snippet = '<iframe src="/table_csv.php?id=' . $id . '&secret=' . $secret . '&type=' . $this->export_type . '&filename=' . $filename_download . '" style="display: none;"></iframe>';

            $this->daten = null;
            $this->export_do = false;
        }

        if ($this->daten === null) {
            foreach ($fields as $field) {
                $rollup = $field->getRollupCompleteNew();

                if ($rollup) {
                    foreach ($rollup as $key => $rollup_method) {
                        $this->data_source->setRollupComplete($key, $rollup_method);
                    }
                }

                $rollup = $field->getRollupNew();
                if ($rollup) {
                    foreach ($rollup as $key => $rollup_method) {
                        $this->data_source->setRollup($key, $rollup_method);
                    }
                }
            }

            $this->data_source->setEntriesPerPage($this->getEntriesPerPage());
            $this->data_source->setPage($this->act_page);
            $this->data_source->setOrderBy($this->order_by, $this->order_by_dir);

            $daten = $this->data_source->getDaten();

            if ($this->explain_data_source && $this->do_explain_datasource && $this->data_source instanceof DataSourceExplainable) {
                echo 'EXLAIN: <pre>' . $this->data_source->explain() . '</pre>';
            }

            if ($this->avoid_empty_pages && $this->act_page > 0 && !$daten) {
                $this->act_page = 0;
                $this->loadDaten();
                return;
            }

            $this->daten = $daten;
        }
    }

    public function getDataSource(): DataSource
    {
        return $this->data_source;
    }

    public function refreshDaten(): void
    {
        $this->daten = null;
        $this->loadDaten();
    }

    protected function checkOrderBy($order_by): bool
    {
        if ($order_by == '') {
            return true;
        }

        if ($order_by == $this->order_by_default) {
            return true;
        }

        foreach ($this->fields as $field) {
            if ($field->getOrderable() == $order_by) {
                return true;
            }
            if ($field->getKey() == $order_by) {
                return true;
            }
            if ($field->getDataKey() == $order_by) {
                return true;
            }

            if ($field instanceof table_object_field_multi) {
                foreach ($field->getFields() as $fieldm) {
                    if ($fieldm->getOrderable() == $order_by) {
                        return true;
                    }
                    if ($fieldm->getKey() == $order_by) {
                        return true;
                    }
                    if ($fieldm->getDataKey() == $order_by) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * prüft ob nur ein Datensatz gefunden wurde
     * @return bool
     */
    public function isOnlyOneResult(): bool
    {
        if ($this->act_page !== 0) {
            return false;
        }

        $this->loadDaten();

        if (count($this->daten) === 1) {
            return true;
        }

        return false;
    }

    public function isNoneResult(): bool
    {
        if ($this->act_page !== 0) {
            return false;
        }

        $this->loadDaten();

        if (count($this->daten) === 0) {
            return true;
        }

        return false;
    }

    /**
     * gibt den ersten Datensatz "roh" zurück
     * @return null|array
     */
    public function getFirstResult(): ?array
    {
        $this->loadDaten();

        return $this->daten[0];
    }

    // </editor-fold>

    public function setFilter($filter): void
    {
        if (isset($filter['page'])) {
            $this->setPage($filter['page']);
        }
        if (isset($filter['entries_per_page'])) {
            $this->setEntriesPerPage($filter['entries_per_page']);
        }

        if (isset($filter['order_by']) && isset($filter['order_by_dir'])) {
            $this->setOrderBy($filter['order_by'], $filter['order_by_dir']);
        } elseif (isset($filter['order_by'])) {
            $this->setOrderBy($filter['order_by']);
        } else {
            //$table->orderBy($this->filter['order_by']);
        }

        if (isset($filter['field_select'])) {
            $this->setFieldSelect(explode(',', $filter['field_select']));
        }
    }

    public function resetFilter(): void
    {
        $this->setPage(0);
        $this->setEntriesPerPage(100);
        $this->setOrderBy('', 'ASC');
    }


    public function setHighlightPhrases(array $phrases): void
    {
        $this->highlight_phrases = array_values($phrases);
    }

    public function setHightlightAndTokenize(string $phrase): void
    {
        $phrases = preg_split('~ \t~', $phrase);
        $this->setHighlightPhrases($phrases);
    }

    public function getCaptionExtra()
    {
        return $this->caption_extra;
    }

    public function isExplainDataSource(): bool
    {
        return $this->explain_data_source;
    }

    public function applyDynamicRowParameters(array $row): void
    {
        $this->calcDynamicRowParameters($row);
    }
}
