<?php

namespace bqp\table;

use bqp\Exceptions\DevException;
use persistent_container;

class TableObjectActionHandler
{

    public static function actionHandler($filter, persistent_container $container): void
    {
        if (!$filter) {
            return;
        }

        if (!is_array($filter)) {
            var_dump($filter);
            throw new DevException();
        }

        if (isset($filter['page'])) {
            $container['page'] = $filter['page'];
        }

        if (isset($filter['entries_per_page'])) {
            $container['entries_per_page'] = $filter['entries_per_page'];
            $container['page'] = 0;
        }

        if (array_key_exists('order_by', $filter) || array_key_exists('order_by_dir', $filter)) {
            if (!isset($filter['order_by_dir']) && !isset($container['order_by_dir'])) {
                $filter['order_by_dir'] = 'ASC';
            }

            if ($filter['order_by'] == $container['order_by']) {
                $container['order_by_dir'] = $container['order_by_dir'] === 'ASC' ? 'DESC' : 'ASC';
            }

            $container['order_by'] = $filter['order_by'];
            if (isset($filter['order_by_dir'])) {
                $container['order_by_dir'] = $filter['order_by_dir'];
            }
        }

        if (isset($filter['field_select'])) {
            if ($filter['field_select'] === 'null') { //"null" -> korrekt
                unset($container['field_select']);
            } else {
                $container['field_select'] = $filter['field_select'];
            }
        }

        if (isset($filter['header_profile_id'])) {
            unset($container['header_profile_id']);

            if ($filter['header_profile_id']) {
                $container['header_profile_id'] = $filter['header_profile_id'];
            }
        }
    }
}
