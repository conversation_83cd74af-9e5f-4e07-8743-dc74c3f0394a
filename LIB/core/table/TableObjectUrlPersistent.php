<?php

namespace bqp\table;

use bqp\controller\front_controller;
use bqp\table\DataSource\TableObjectDataSourceWws;
use db;

class TableObjectUrlPersistent extends TableObject
{
    use TableObjectControllerAware;

    protected $persistent_fields = [];

    public function __construct($datasource_or_sql)
    {
        $this->addPersistentField('page', 0);
        $this->addPersistentField('entries_per_page', 100);
        $this->addPersistentField('order_by');
        $this->addPersistentField('order_by_dir');
        $this->addPersistentField('field_select');

        if (is_string($datasource_or_sql)) {
            $datasource = new TableObjectDataSourceWws(db::getInstance(), $datasource_or_sql);
        } else {
            $datasource = $datasource_or_sql;
        }

        $datasource->getOrderBy();

        $this->refreshUrl();

        parent::__construct($datasource);

        $this->setFilter($_REQUEST);
    }

    public function refreshUrl()
    {
        $this->setUrlByControllerIfPossible();
    }

    public function setId($id)
    {
        parent::setId($id);

        $this->refreshUrl();
    }


    public function addPersistentFieldByController(front_controller $controller)
    {
        $parameters = $controller->getDefaultParameters();

        foreach ($parameters as $key => $value) {
            $this->addPersistentField($key, $value);
        }
    }

    public function addPersistentField($key, $default_value = null)
    {
        $this->persistent_fields[$key] = $default_value;
    }

    public function getPersistentFieldValues()
    {
        $return = [];
        foreach ($this->persistent_fields as $key => $default_value) {
            $value = $default_value;
            if (array_key_exists($key, $_REQUEST)) {
                $value = $_REQUEST[$key];
            }

            $return[$key] = $value;
        }

        return $return;
    }

    public function makeUrl($settings)
    {
        $values = $this->getPersistentFieldValues();

        if (preg_match_all('~(?:filter\[([^\]]*)\]|([^\]=]*))=([^&]*)~', $settings, $result, PREG_SET_ORDER)) {
            foreach ($result as $f) {
                $key = $f[1] ? $f[1] : $f[2];

                $values[$key] = $f[3];
            }
        }

        $t = http_build_query($values);

        $url = str_replace('{{$settings}}', $t, $this->url);


        return $url;
    }

    public function setEntriesPerPageDefault($entries_per_page)
    {
        parent::setEntriesPerPageDefault($entries_per_page);

        if ($this->entries_per_page === null) {
            $this->addPersistentField('entries_per_page', $entries_per_page);
        }
    }
}
