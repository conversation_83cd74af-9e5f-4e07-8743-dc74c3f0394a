<?php

namespace bqp\table;

use bqp\table\Renderer\TableObjectRenderer;

class ExtraRowRollup extends ExtraRow
{
    const METHOD_SUM = 'sum';

    private $fields = [];
    private $sums = [];
    private $defaults = [];

    protected $location = 'bottom';

    protected $class = 'rollup';

    protected $title = '';
    protected $static_fields = [];

    protected $modus = 'page';

    /**
     * @var callable
     */
    protected $filter = null;

    private function GetExistingKey(array $values, string $field_data_key, string $field_key): ?string
    {
        if (array_key_exists($field_data_key, $values)) {
            return $field_data_key;
        }

        if (array_key_exists($field_key, $values)) {
            return $field_key;
        }

        return null;
    }

    public function addRollupField($field, $method, $default_value = null)
    {
        $this->fields[$field] = $method;
        $this->defaults[$field] = $default_value;
    }


    public function setTitle($title)
    {
        $this->title = $title;
    }


    public function setClass($class)
    {
        $this->class = $class;
    }


    public function addStaticContent($field, $daten)
    {
        $this->static_fields[$field] = $daten;
    }


    public function setFilter($filter)
    {
        $this->filter = $filter;
    }

    public function dataHandler($daten, $src = 'page')
    {
        if ($this->modus === 'page' && $src === 'page') {
            if ($this->filter) {
                $filter = $this->filter;
                if (!$filter($daten)) {
                    return $daten;
                }
            }

            foreach ($this->fields as $field => $method) {
                if (!isset($this->sums[$field])) {
                    $this->sums[$field] = 0;
                }

                switch ($method) {
                    case self::METHOD_SUM:
                        $this->sums[$field] += $daten[$field];
                        break;
                    default:
                        if (is_callable($method)) {
                            $this->sums[$field] = $method($daten, $field, $this->sums[$field]);
                        } else {
                            throw new \bqp\Exceptions\DevException('unknown operation');
                        }
                }
            }

            return $daten;
        } else {
        }
    }

    public function render(TableObjectRenderer $renderer, array $row = []): string
    {
        $return = '<tr class="' . $this->class . '">';
        $pos = 0;
        $colspan = 0;
        $check = 0;
        if ($this->title === '') {
            $colspan = null;
        }

        foreach ($renderer->getFields() as $field) {
            $pos++;

            $field_key = $field->getKey();
            $field_data_key = $field->getDataKey();

            $has_sum = array_key_exists($field_data_key, $this->sums) || array_key_exists($field_key, $this->sums);
            $has_default = array_key_exists($field_data_key, $this->defaults) || array_key_exists($field_key, $this->defaults);

            if ($has_sum || $has_default) {
                if ($colspan !== null && $pos > 1) {
                    $return .= '<td colspan="' . $colspan . '">' . $this->title . '</td>';
                }

                $values = $has_sum ? $this->sums : $this->defaults;

                $existing_key = $this->GetExistingKey($values, $field_data_key, $field_key);
                if ($existing_key !== null && $existing_key !== $field_data_key) {
                    $values[$field_data_key] = $values[$existing_key];
                }

                $return .= $field->render($values);
                $colspan = null;
            } else {
                $existing_key = $this->GetExistingKey($this->static_fields, $field_data_key, $field_key);
                if ($existing_key !== null) {
                    $value = $this->static_fields[$existing_key];

                    if (is_callable($value)) {
                        $value = $value($this->sums);
                    }

                    $return .= '<td>' . $value . '</td>';
                    $colspan = null;
                } elseif ($colspan !== null) {
                    $colspan++;
                } else {
                    $return .= '<td></td>';
                }
            }
        }
        $return .= '</tr>';

        return $return;
    }
}
