<?php

namespace bqp\table\Renderer;

class TableObjectRendererPlainText extends TableObjectRenderer
{
    protected $dst_charset = 'UTF-8';

    protected $width = 120;
    protected $fill_max_width = true;

    protected $border_chars = [
        'line_v' => "─",
        'line_h' => "│",
        'corner_tl' => "┌",
        'corner_tr' => "┐",
        'corner_bl' => "└",
        'corner_br' => "┘",
        'junc_x' => "┼",
        'junc_vd' => "┬",
        'junc_vt' => "┴",
        'junc_hl' => "├",
        'junc_hr' => "┤"
    ];

    protected $render_h_lines = false;
    protected $render_h_line_after_header = true;

    public function render(): string
    {
        $this->table->eventBeforeRender();

        $cache = $this->getPreparedData();
        $widths = $this->calcWidths($cache);


        $return = '';

        $return .= $this->renderHeader($cache, $widths);

        $c = $this->border_chars;

        $row_id_max = count($cache) - 1;

        foreach ($cache as $row_id => $row) {
            $row_cache = [];
            $lines = 0;
            foreach ($widths as $key => $width) {
                if (mb_strlen($row[$key], $this->dst_charset) > $width['render_size']) {
                    $rest = $row[$key];
                    $i = 0;
                    while ($rest !== '') {
                        $row_cache[$i][$key] = mb_substr($rest, 0, $width['render_size'], $this->dst_charset);
                        $rest = mb_substr($rest, $width['render_size'], null, $this->dst_charset);
                        $i++;
                    }

                    $lines = max($lines, $i - 1);
                } else {
                    $row_cache[0][$key] = $row[$key];
                }
            }

            for ($i = 0; $i <= $lines; $i++) {
                $return .= $c['line_h'];

                foreach ($widths as $key => $width) {
                    if ($key > 0) {
                        $return .= $c['line_h'];
                    }
                    $return .= ' ';

                    if (isset($row_cache[$i][$key])) {
                        $return .= $row_cache[$i][$key];
                        $return .= str_repeat(' ', $width['render_size'] - mb_strlen($row_cache[$i][$key], $this->dst_charset));
                    } else {
                        $return .= str_repeat(' ', $width['render_size']);
                    }

                    $return .= ' ';
                }

                $return .= $c['line_h'] . "\n";
            }


            if (($this->render_h_lines || ($this->render_h_line_after_header && $row_id === 0)) && $row_id_max != $row_id) {
                $return .= $c['junc_hl'];

                foreach ($widths as $key => $width) {
                    if ($key > 0) {
                        $return .= $c['junc_x'];
                    }
                    $return .= str_repeat($c['line_v'], $width['render_size'] + 2);
                }

                $return .= $c['junc_hr'] . "\n";
            }
        }


        $return .= $c['corner_bl'];
        foreach ($widths as $key => $width) {
            if ($key > 0) {
                $return .= $c['junc_vt'];
            }
            $return .= str_repeat($c['line_v'], $width['render_size'] + 2);
        }
        $return .= $c['corner_br'] . "\n";

        return $return;
    }

    /**
     * trägt die benötigten daten zusammen und konvertiert ggf. den Zeichensatz
     *
     * @return array
     */
    private function getPreparedData()
    {
        $cache = [];

        $row = [];
        foreach ($this->fields as $key => $field) {
            $row[] = mb_convert_encoding($field->getName(), $this->dst_charset, 'UTF-8');
        }

        $cache[] = $row;

        foreach ($this->table->getDaten() as $row_daten) {
            $row = [];
            foreach ($this->fields as $field) {
                $value = $field->renderCsvValue($row_daten);

                $row[] = mb_convert_encoding($value, $this->dst_charset, 'UTF-8');
            }

            $cache[] = $row;
        }

        return $cache;
    }

    /**
     * berechnet anhand des übergebenene arrays die spalten weiten
     *
     * @param $cache
     * @return array
     */
    private function calcWidths(array $cache): array
    {
        //var_dump($cache);

        if (!$cache[0]) { //@todo deplaziert? die konstelation tritt auf, wenn die quelle ein header automatisch erzeugt mit leeren daten. rest der funktion kommt mit einer leeren liste nicht klar
            return [];
        }

        $widths = [];
        //spalten weiten bestimmen
        //nur anhand des headers
        foreach ($cache[0] as $col) {
            $len = mb_strlen($col, $this->dst_charset);
            $widths[] = ['min' => $len, 'max' => $len, 'avg' => $len, 'render_size' => null, 'sum' => $len, 'count' => 1];
        }

        //komplett, optional überschreibt die header infos
        foreach ($cache as $row) {
            foreach ($row as $key => $col) {
                $len = mb_strlen($col, $this->dst_charset);

                $widths[$key]['sum'] += $len;
                $widths[$key]['count']++;

                $widths[$key]['min'] = min($widths[$key]['min'], $len);
                $widths[$key]['max'] = max($widths[$key]['max'], $len);
            }
        }

        foreach ($widths as $key => $width) {
            $widths[$key]['avg'] = round($width['sum'] / $width['count']);
        }


        //berechnen
        $total_width = 0;
        $counted_total_width = 0;
        foreach ($widths as $key => $width) {
            if ($width['avg'] <= 5 and $width['avg'] < $width['max']) {
                $widths[$key]['avg_org'] = $widths[$key]['avg'];
                $widths[$key]['avg'] = $width['avg'] = $width['max'];

                $total_width += $width['avg'] + 2;
            } else {
                $counted_total_width += $width['avg'] + 2;
                $total_width += $width['avg'] + 2;
            }
        }

        $borders = count($widths) + 1;
        $free_excess = $this->width - $borders - $total_width;

        $largest_col = 0;
        $largest_col_size = 0;

        foreach ($widths as $key => $width) {
            if (isset($width['avg_org'])) {
                $percent = 0;
            } else {
                $percent = ($width['avg'] + 2) / ($counted_total_width);
            }

            $widths[$key]['render_size'] = $width['avg'] + floor($free_excess * $percent);

            if ($largest_col_size < $widths[$key]['render_size']) {
                $largest_col_size = $widths[$key]['render_size'];
                $largest_col = $key;
            }
        }

        //schauen ob was fehlt
        $total_width = 0;
        foreach ($widths as $key => $width) {
            $total_width += $width['render_size'] + 2;
        }

        $free_excess = $this->width - $borders - $total_width;

        if ($free_excess > 0) {
            $widths[$largest_col]['render_size'] += $free_excess;
        }

        return $widths;
    }

    private function renderHeader($cache, $widths)
    {
        $c = $this->border_chars;

        $return = '';

        if ($this->table->getCaption()) {
            $return .= $c['corner_tl'];
            $return .= str_repeat($c['line_v'], $this->width - 2);
            $return .= $c['corner_tr'] . "\n";

            $return .= $c['line_h'];
            $return .= ' ' . str_pad($this->table->getCaption(), $this->width - 3, ' ');
            $return .= $c['line_h'] . "\n";

            $return .= $c['junc_hl'];
            foreach ($widths as $key => $width) {
                if ($key > 0) {
                    $return .= $c['junc_vd'];
                }
                $return .= str_repeat($c['line_v'], $width['render_size'] + 2);
            }
            $return .= $c['junc_hr'] . "\n";
        } else {
            $return .= $c['corner_tl'];
            foreach ($widths as $key => $width) {
                if ($key > 0) {
                    $return .= $c['junc_vd'];
                }
                $return .= str_repeat($c['line_v'], $width['render_size'] + 2);
            }
            $return .= $c['corner_tr'] . "\n";
        }

        return $return;
    }

    /**
     * horizontale linie zwischen jeden datensatz
     *
     * @param boolean $status
     */
    public function setRenderHLines($status)
    {
        $this->render_h_lines = $status;
    }

    /**
     * horizontale linie nach header
     *
     * @param boolean $status
     */
    public function setRenderHLineAfterHeader($status)
    {
        $this->render_h_line_after_header = $status;
    }

    /**
     * legt die tabellen breite in zeichenfest
     *
     * @param int $width
     */
    public function setWidth($width)
    {
        $this->width = $width;
    }

    public function autoWidthByCli()
    {
        $cols = exec('tput cols');
        //exec('tput lines');
        if ($cols > 10) {
            $this->setWidth($cols);
        }
    }
}
