<?php

namespace bqp\table\Renderer;

/**
 * Class TableObjectRendererGsLegacy
 *
 * Für das Grossisten-System um die bisherigen Tabellen als TableObject abbilden zu können.
 * Das ist kein vollwertiger Renderer, sondern wrappter nur den bestehenden Renderer und manipuliert das TableObject und
 * die Ausgabe so, dass diese in das Grossisten-System passt.
 *
 * @package bqp\table\renderer
 */
class TableObjectRendererGsLegacy extends TableObjectRenderer
{
    private $field_classes = [];

    private $default_field_class = 'tdrow2';

    public function setDefaultFieldClass(string $class): void
    {
        $this->default_field_class = $class;
    }

    public function setFieldClass(string $field_key, string $class): void
    {
        $this->field_classes[$field_key] = $class;
    }

    public function render(): string
    {
        $this->beforeRender();

        $this->table->setAttribute('cellspacing', 0);
        $this->table->setAttribute('cellpadding', 5);
        $this->table->setAttribute('width', '100%');

        $caption = $this->table->getCaption();
        $this->table->setCaption(null);

        foreach ($this->fields as $field) {
            if (!$field->getClass()) {

                $field_key = $field->getKey();
                $field_data_key = $field->getDataKey();

                if (isset($this->field_classes[$field_key])) {
                    $field->setClass($this->field_classes[$field_key]);
                } elseif (isset($this->field_classes[$field_data_key])) {
                    $field->setClass($this->field_classes[$field_data_key]);
                } else {
                    $field->setClass($this->default_field_class);
                }
            }
        }

        $result = '<div class="table_border">';
        $result .= '    <div class="table_title">' . $caption . '</div>';
        $result .= $this->table->render();
        $result .= '    </div>';
        $result .= '</div>';

        return $result;
    }
}
