<?php

namespace bqp\table\Renderer;

use bqp\table\field\table_object_field;
use bqp\table\TableObject;

abstract class TableObjectRenderer
{
    protected TableObject $table;

    /**
     * @var table_object_field[]
     */
    protected array $fields;

    public function __construct(TableObject $table)
    {
        $this->table = $table;
    }

    public function beforeRender(): void
    {
        $this->fields = $this->table->getFieldsForRender();
    }

    /**
     * @return string
     */
    abstract public function render(): string;

    public function __toString()
    {
        return $this->render();
    }

    public function getTable(): TableObject
    {
        return $this->table;
    }

    /**
     * @return table_object_field[]
     */
    public function getFields(): array
    {
        return $this->fields;
    }
}
