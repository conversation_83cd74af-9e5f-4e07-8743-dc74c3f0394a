<?php

namespace bqp\table\Renderer;

use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_multi;

class TableObjectRendererCsv extends TableObjectRenderer
{
    protected $dst_charset = 'CP1252';

    public function render(): string
    {
        $this->table->eventBeforeRender();

        return $this->renderInTableObjectContext();
    }

    public function renderInTableObjectContext(): string
    {
        $return = '';
        $return .= $this->renderCsvHeader($this->fields);
        $return .= $this->renderCsvBody();
        return $return;
    }

    /**
     * @param table_object_field[] $fields
     */
    public function renderCsvHeader(array $fields, string $prefix = ''): string
    {
        $return = '';

        foreach ($fields as $field) {
            $header = mb_convert_encoding($field->renderCsvHeader(), $this->dst_charset, 'UTF-8');
            if ($field instanceof table_object_field_multi) {
                $return .= $this->renderCsvHeader($field->getFields(), $header . ' - ') . ';';
            } else {
                $return .= '"' . $prefix . $header . '";';
            }
        }

        return substr($return, 0, -1);
    }

    private function renderCsvBodyRow(array $fields, array $data): string
    {
        $return = '';
        foreach ($fields as $field) {
            if ($field instanceof table_object_field_multi) {
                $return .= $this->renderCsvBodyRow($field->getFields(), $data) . ';';
                continue;
            }

            $value = $field->renderCsvValue($data);

            $return .= mb_convert_encoding($this->csv_quote($value), $this->dst_charset, 'UTF-8') . ';';
        }
        return substr($return, 0, -1);
    }

    public function renderCsvBody(): string
    {
        $return = '';

        foreach ($this->table->getDaten() as $row) {
            $return .= "\n" . $this->renderCsvBodyRow($this->fields, $row);
        }

        return $return;
    }

    private function csv_quote($value)
    {
        if (is_numeric($value)) {
            return $value;
        }

        return '"' . str_replace('"', '""', $value) . '"';
    }
}
