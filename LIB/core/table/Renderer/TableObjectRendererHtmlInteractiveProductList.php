<?php

namespace bqp\table\Renderer;

use bqp\Utils\HtmlBuilder;
use config;
use wws\Product\ProductList;
use wws\Product\ProductRepositoryLegacy;

class TableObjectRendererHtmlInteractiveProductList extends TableObjectRendererHtmlInteractive
{
    private bool $show_header_profile_picker = false;

    protected ProductList $product_list;

    public function __construct(ProductList $table)
    {
        $this->product_list = $table;

        parent::__construct($table);
    }

    public function isMultiSelect(): bool
    {
        foreach ($this->product_list->getHeaderProfile()->getHeaders() as $header) {
            if ($header->getDataKey() === 'multi_select' || $header->getKey() === 'multi_select') {
                return true;
            }
        }

        return false;
    }


    public function setShowHeaderProfilePicker($status): void
    {
        $this->show_header_profile_picker = $status;
    }

    public function renderCaptionContent(): string
    {
        $content = '';

        if ($this->show_header_profile_picker) {
            $content .= '<div style="float: right; padding-left: 10px;">Ansicht:';
            $url = $this->table->makeUrl('filter[header_profile_id]=\'+this.value+\'');

            $content .= '<select onchange="' . $url . '">';
            $content .= '<option value="0">-- Standard --</option>';
            $content .= HtmlBuilder::options(config::product_list('set_index'), $this->table->getHeaderProfile()->getHeaderProfileId());
            $content .= '</select>';
            $content .= '</div>';
        }

        return $content . parent::renderCaptionContent();
    }

    public function renderFooter(): string
    {
        if (!$this->table->isRenderFooter()) {
            return '';
        }

        $return = '';

        if ($this->isMultiSelect()) {
            $product_list_actions = $this->product_list->getProductListActions();

            if ($product_list_actions) {
                $return .= "<tr>\n";
                $return .= '<th colspan="' . ($this->table->countCols() - 1) . '" class="td_footer">';

                $id = 'product_list_action' . rand(1000, 9999);

                $return .= '
                            <div style="float: right;">
                                <select style="width: 250px;" id="' . $id . '">
                                    <option value="none">-- AKTION --</option>
                                    ' . HtmlBuilder::options($product_list_actions) . '
                                </select>
    
                                <input type="button" value="GO" onclick="wws.product.product_list_action(document.getElementById(\'' . $id . '\').value, \'product_ids[]\', 1)" />
                            </div>
                        ';
                $return .= '</th>';
                $return .= '<th style="text-align: center;"><input type="checkbox" onclick="wws.checkboxSelectAll(this.checked,\'product_ids[]\')" />';
                $return .= '<script type="text/javascript">wws.checkboxSelectMulti(\'product_ids[]\');</script>';
                $return .= '</th>';
                $return .= "</tr>\n";
            }
        }

        $return .= parent::renderFooter();

        return $return;
    }
}
