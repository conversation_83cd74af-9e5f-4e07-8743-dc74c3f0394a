<?php

namespace bqp\table\Renderer;

use bqp\table\field\table_object_field;
use bqp\Utils\HtmlBuilder;
use bqp\table\ExtraRow;
use bqp\table\DataSource\DataSourceExplainable;

/**
 * Class TableObjectRendererHtmlInteractive
 * @package bqp\table\Renderer
 *
 * Das wird der standard renderer vom TableObject. Das was aktuell an render Funktionalität im TableObject implementiert ist, soll hier rein gezogen werden.
 */
class TableObjectRendererHtmlInteractive extends TableObjectRenderer
{

    public function render(): string
    {
        $this->table->eventBeforeRender();

        $return = '';
        $return .= $this->table->getExportDownloadSnippet();

        $attributes = [
            'id' => $this->table->getId(),
            'class' => 'table-responsive',
            'data-url' => $this->table->makeUrl('')
        ];

        $return .= '<div ' . HtmlBuilder::renderAttributes($attributes) . '>';

        $inner_envelop_callback = $this->table->getInnerEnvelopCallback();

        if (is_callable($inner_envelop_callback)) {
            $return .= call_user_func($inner_envelop_callback, $this->renderInner(), $this->table);
        } else {
            $return .= $this->renderInner();
        }

        $extra_javascript = $this->table->getExtraJavascript();
        if ($extra_javascript) {
            $return .= '<script type="text/javascript">' . $extra_javascript . '</script>';
        }
        $return .= '</div>';
        $return .= '<script type="text/javascript">init_table_object(\'' . $this->table->getId() . '\')</script>';

        $highlight_phrases = $this->table->getHighlightPhrases();
        if ($highlight_phrases) {
            $return .= '<script type="text/javascript">highlight_table_object(\'' . $this->table->getId() . '\', ' . json_encode($highlight_phrases) . ')</script>';
        }

        return $return;
    }

    public function renderInner(): string
    {
        $attributes = $this->table->getAttributes();
        unset($attributes['id']);

        $inner = '<table' . HtmlBuilder::renderAttributes($attributes) . '>' . "\n";
        $inner .= $this->renderHeader();
        $inner .= $this->renderBody();
        $inner .= $this->renderRollup();
        $inner .= $this->renderFooter();
        $inner .= $this->renderNavigation();
        $inner .= '</table>' . "\n";

        return $inner;
    }

    public function renderHeader(): string
    {
        $return = '';

        $return .= '<thead>';
        $return .= $this->renderCaption();
        $return .= '<tr>' . "\n";
        foreach ($this->fields as $field) {
            $return .= $this->renderFieldHeader($field);
        }
        $return .= '</tr>' . "\n";

        $return .= '<tr>' . "\n";
        foreach ($this->fields as $field) {
            $return .= $field->renderSubHeader();
        }
        $return .= '</tr>' . "\n";

        $return .= '</thead>';

        return $return;
    }

    public function renderFieldHeader(table_object_field $field): string
    {
        return $field->renderHeader();
    }

    public function renderBody(): string
    {
        $return = '';

        $extra_rows_grouped = $this->table->getExtraRowsGrouped();
        $extra_rows = $this->table->getExtraRows();


        $last_group_header = null;

        /* @var $extra_row ExtraRow */

        $return .= '<tbody>' . "\n";
        foreach ($extra_rows_grouped[ExtraRow::LOCATION_TOP] as $extra_row) {
            $return .= $extra_row->render($this);
        }

        $i = 0;

        foreach ($this->table->getDaten() as $daten) {
            $inbody_header = $this->table->getInbodyHeader();
            if ($inbody_header && $i % $inbody_header === 0 && $i > 0) {
                $return .= $this->renderHeader();
            }

            $this->table->applyDynamicRowParameters($daten);

            $group_header_key = $this->table->getGroupHeaderField();
            if ($group_header_key !== null) {
                $group_header = $daten[$group_header_key];
                if ($last_group_header != $group_header) {
                    $last_group_header = $group_header;
                    $return .= $this->renderGroupHeader($daten);
                }
            }

            foreach ($extra_rows as $extra_row) {
                $daten = $extra_row->dataHandler($daten);
            }

            foreach ($extra_rows_grouped[ExtraRow::LOCATION_BEFORE] as $extra_row) {
                $return .= $extra_row->render($this, $daten);
            }

            $return .= $this->renderRow($daten);


            foreach ($extra_rows_grouped[ExtraRow::LOCATION_AFTER] as $extra_row) {
                $return .= $extra_row->render($this, $daten);
            }

            $i++;
        }

        foreach ($extra_rows_grouped[ExtraRow::LOCATION_BOTTOM] as $extra_row) {
            $return .= $extra_row->render($this);
        }
        $return .= '</tbody>' . "\n";

        return $return;
    }

    public function renderRow(array $row): string
    {
        $cols = [];
        $multi_column = [];
        $max_col_count = 1;

        foreach ($this->fields as $key => $field) {
            $field_content = $field->renderComplete($row);
            $multi_column[$key] = true;
            if (!is_array($field_content)) {
                $field_content = [$field_content];
                $multi_column[$key] = false;
            }
            $cols[$key] = $field_content;
            $max_col_count = max(count($field_content), $max_col_count);
        }

        if ($max_col_count !== 1) {
            foreach ($this->fields as $key => $field) {
                if (count($cols[$key]) < $max_col_count) {
                    if ($multi_column[$key] || $this->table->getMultidimensonalMode() === $this->table::MULTIDIMENSIONAL_MODE_EMPTY) {
                        $cols[$key] = array_merge($cols[$key], array_fill(0, $max_col_count - count($cols[$key]), null));
                    }
                }
            }
        }

        $attributes_array = [];
        $row_formater = $this->table->getRowFormater();
        if ($row_formater) {
            foreach ($row_formater as $formater) {
                $result = call_user_func($formater, $row);
                if ($result) {
                    $attributes_array = array_merge($result, $attributes_array);
                }
            }
        }

        $attributes = HtmlBuilder::renderAttributes($attributes_array);
        $return = '<tr ' . $attributes . '>' . "\n";
        foreach ($this->fields as $key => $field) {
            $rendered_field = $cols[$key][0];
            if ($max_col_count > 1 && count($cols[$key]) !== $max_col_count) {
                $rendered_field = substr_replace($rendered_field, '<td rowspan="' . $max_col_count . '" ', 0, 3);
            }
            $return .= $rendered_field;
        }
        $return .= '</tr>' . "\n";

        for ($i = 1; $i < $max_col_count; $i++) {
            $return .= '<tr>' . "\n";

            foreach ($this->fields as $key => $field) {
                if (array_key_exists($i, $cols[$key])) {
                    if ($cols[$key][$i]) {
                        $return .= $cols[$key][$i];
                    } else {
                        $return .= $field->renderEmpty();
                    }
                }
            }

            $return .= '</tr>' . "\n";
        }

        return $return;
    }

    public function renderGroupHeader(array $row): string
    {
        $return = '';

        $return .= '<tr>' . "\n";
        $return .= '<th colspan="' . count($this->fields) . '">';
        $group_header_key = $this->table->getGroupHeaderField();
        $return .= $group_header_key !== null ? $row[$group_header_key] : '';
        $return .= '</th>';
        $return .= '</tr>' . "\n";

        return $return;
    }


    public function renderCaption(): string
    {
        $content = $this->renderCaptionContent();

        if (!$content) {
            return '';
        }

        return '<caption>' . $content . '</caption>';
    }

    public function renderCaptionContent(): string
    {
        $caption = $this->table->getCaption();
        if (!$caption) {
            return '';
        }

        $csv = '';
        if ($this->table->isExportEnabled()) {
            $csv .= '<a href="' . $this->table->makeUrl('export_as=csv') . '" title="als CSV exportieren"><img src="/res/images/fileicons/csv.png" style="float: right; cursor: pointer; margin-left: 10px;" /></a>';
            $csv .= '<a href="' . $this->table->makeUrl('export_as=xlsx') . '" title="als XLSX exportieren"><img src="/res/images/fileicons/xlsx.png" style="float: right; cursor: pointer; margin-left: 10px;" /></a>';
        }

        $print = '';
        if ($this->table->isPrint()) {
            $print = '<a href="#" onclick="print_table_object(event)"><img src="/res/images/icons/printer.png" style="float: right; cursor: pointer; margin-left: 10px;" /></a>';
        }

        $explain = '';

        $data_source = $this->table->getDataSource();
        if ($this->table->isExplainDataSource() && $data_source instanceof DataSourceExplainable) {
            $explain = '<a href="' . $this->table->makeUrl('explain_datasource=1') . '"><img src="/res/images/icons/application_osx_terminal.png" style="float: right; cursor: pointer; margin-left: 10px;" /></a>';
        }

        $field_config = '';

        if ($this->table->isFieldSelectEnabled()) {
            $url = $this->table->makeUrl('&table_field_select=1');
            $field_config = '<a href="' . $url . '"><img src="/res/images/icons/table_gear.png" style="float: right; cursor: pointer; margin-left: 10px;" /></a>';
        }

        return $caption . $field_config . $explain . $csv . $print . $this->table->getCaptionExtra();
    }


    public function renderRollup(): string
    {
        $data_source = $this->table->getDataSource();
        $rollups = $data_source->getRollupGroups();

        if (!$rollups) {
            return '';
        }

        $is_first = true;

        $return = '';

        foreach ($rollups as $rollup) {
            if ($rollup->getRollupType() === 'interim' && !$this->table->isRenderRollupInterimResult()) {
                continue;
            }

            $c = '';
            if ($is_first) {
                $c = ' rollup';
            }


            $rollup_daten = $rollup->getResult();

            $return .= '<tr class="rollup_complete' . $c . '">' . "\n";
            $i = 0;
            $description = $rollup->getName();
            if ($rollup->getRollupType() === 'complete') {
                $description = '<b>' . $description . '</b>';
            }

            foreach ($this->fields as $field) {
                switch ($rollup->getRollupType()) {
                    case 'complete':
                        $temp = $field->renderRollupComplete($rollup_daten);
                        break;
                    case 'interim':
                    default:
                        $temp = $field->renderRollup($rollup_daten);
                        break;
                }

                if ($temp) {
                    if ($i > 0) {
                        $return .= '<td colspan="' . $i . '">' . $description . '</td>';
                        $i = 0;
                        $description = '';
                    }
                    $return .= $temp;
                } else {
                    $i += $field->countCols();
                }
            }
            if ($i > 0) {
                $return .= '<td colspan="' . $i . '"></td>';
            }
            $return .= '</tr>' . "\n";

            $is_first = false;
        }

        return $return;
    }

    public function renderFooter(): string
    {
        if (!$this->table->isRenderFooter()) {
            return '';
        }

        $footer_content = $this->table->getFooterContent();
        if (!$footer_content) {
            return '';
        }

        $return = '';

        $return .= '<tr>' . "\n";
        $return .= '<th colspan="' . $this->table->countCols() . '" class="td_footer">';
        $return .= $footer_content;
        $return .= '</th>';
        $return .= '</tr>' . "\n";

        return $return;
    }

    public function renderNavigation(): string
    {
        if (!$this->table->isRenderNavigation()) {
            return '';
        }

        $return = '';

        $return .= '<tr>' . "\n";
        $return .= '<th colspan="' . $this->table->countCols() . '" class="td_footer">';

        $navigation = '<div style="text-align: right;">';

        $navigation .= $this->getPageNavigation();

        $entries_per_page_options = $this->table->getEntriesPerPageOptions();
        if (count($entries_per_page_options) > 1) {
            $navigation .= ' Einträge';

            $url = $this->table->makeUrl('filter[entries_per_page]=\'+this.value+\'');

            if (strpos($url, '%')) {
                $url = urldecode($url);
            }

            $navigation .= ' <select onchange="' . $url . '" style="width: 60px;">';
            foreach ($entries_per_page_options as $limit) {
                $s = $limit == $this->table->getEntriesPerPage() ? ' selected="selected"' : '';
                $navigation .= '<option value="' . $limit . '"' . $s . '>' . $limit . '</option>';
            }
            $navigation .= '</select> &nbsp; ';
        }

        $navigation .= '</div>';

        $return .= $navigation;

        $return .= '</th>';
        $return .= '</tr>' . "\n";

        return $return;
    }

    private function getPageNavigation(): string
    {
        $data_source = $this->table->getDataSource();
        $found_rows = $data_source->getTotalRowCount();

        $pages = ceil($found_rows / $this->table->getEntriesPerPage());

        $limit = 11;

        $page_start = 0;
        $page_end = $limit;

        $act_page = $this->table->getPage();

        if ($act_page > $limit / 2) {
            $page_start = $act_page - floor($limit / 2);
            $page_end = $act_page + ceil($limit / 2);
        }

        if ($page_start < 0) {
            $page_end -= $page_start;
            $page_start = 0;
        }

        if ($page_end > $pages) {
            $page_start -= $page_end - $pages;
            $page_end = $pages;
            if ($page_start < 0) {
                $page_start = 0;
            }
        }

        $return = '(' . $found_rows . ') ';

        $return .= '<div class="paging_navigator">';

        $prev_page = $act_page - 1;
        if ($prev_page < 0) {
            $prev_page = 0;
        }

        $return .= '<a href="' . $this->table->makeNavigationUrl(0) . '">&lt;&lt;</a> ';
        $return .= '<a href="' . $this->table->makeNavigationUrl($prev_page) . '">&lt;</a> ';

        for ($page = $page_start; $page < $page_end; $page++) {
            $url = $this->table->makeNavigationUrl($page);

            $class = '';
            if ($page == $act_page) {
                $class = ' class="paging_navigator_aktiv"';
            }

            $return .= '<a href="' . $url . '"' . $class . '>' . ($page + 1) . '</a> ';
        }

        $next_page = $act_page + 1;
        if ($next_page >= $pages - 1) {
            $next_page = $pages - 1;
        }

        $return .= '<a href="' . $this->table->makeNavigationUrl($next_page) . '">&gt;</a> ';
        $return .= '<a href="' . $this->table->makeNavigationUrl($pages - 1) . '">&gt;&gt;</a>';
        $return .= '</div>';

        return $return;
    }
}
