<?php

namespace bqp\table\Renderer;

use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_multi;
use bqp\table\field\table_object_field_numeric;
use bqp\table\field\table_object_field_text;
use bqp\Utils\PhpSpreadsheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\StringValueBinder;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class TableObjectRendererPhpSpreadsheet extends TableObjectRenderer
{
    public function render(): string
    {
        $this->table->eventBeforeRender();

        return $this->renderInTableObjectContext();
    }

    /**
     * @param table_object_field[] $fields
     * @param int[] $coords
     * @return int[]
     */
    private function renderHeader(Worksheet $worksheet, array $fields, array $coords = [1, 1], string $prefix = ''): array
    {
        foreach ($fields as $field) {
            $header = $field->renderCsvHeader();
            if ($field instanceof table_object_field_multi) {
                $coords = $this->renderHeader($worksheet, $field->getFields(), $coords, $header . ' - ');
            } else {
                $worksheet->setCellValue($coords, $prefix . $field->renderCsvHeader());
                $this->styleHeader($worksheet, $coords);
            }
            $coords[0]++;
        }

        $coords[0]--;
        return $coords;
    }

    private function styleHeader(Worksheet $worksheet, array $coords): void
    {
        $style = $worksheet->getStyle($coords);
        $alignment = $style->getAlignment();
        $alignment->setHorizontal('left');
        $alignment->setVertical('center');

        $style->getFont()->setBold(true)->setColor(new Color('FF005992'));
        $style->getBorders()->getBottom()
            ->setBorderStyle(Border::BORDER_MEDIUM)
            ->setColor(new Color('FFD2E3EC'));
        $style->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FFEFF5F8');

        //spaltenbereiten automatisch bestimmen lassen...
        //@todo das ist auch eher grenzwertig. die spalten sind gefühlt mit 1/3 Luft gefüllt und wenn da noch ausreiser in der länge dabei sind wirds hässlich
        // ->selbst mitzählen. header bestimmt min. dann alle längen rein geben und dort die längen mit zählen. wenn da werte über 150 auftauchen, werden nur das max aus dem 95% percentil genommen
        if ($worksheet->getCell($coords)->getValue()) {
            $col_key = Coordinate::stringFromColumnIndex($coords[0]);
            $worksheet->getColumnDimension($col_key)->setAutoSize(true);
        }
    }

    private function renderBodyRow(Worksheet $worksheet, array $fields, array $coords, array $data, StringValueBinder $binder): array
    {
        foreach ($fields as $field) {
            $cell = $worksheet->getCell($coords);

            if ($field instanceof table_object_field_multi) {
                $this->renderBodyRow($worksheet, $field->getFields(), $coords, $data, $binder);
                continue;
            }

            $value = $field->renderCsvValue($data);

            //@todo in der form auch köse... vielleicht in richtung besucher pattern
            //zumindestens sowas wie links und currency hätte ich sinnvoll abgebildet
            $value_binder = null;
            if ($field instanceof table_object_field_numeric) {
                $value = $field->getValue($data);

                $raw_prefix = $field->getPrefix();
                $raw_suffix = $field->getSuffix();

                $decimals = '0';
                if ($field->getDecimals()) {
                    $decimals .= '.' . str_repeat('0', $field->getDecimals());
                }

                $thousands_separator = $field->getDecimalThousandsSeparator();
                //mitigation: PhpSpreadsheet benutzt ein speziellen "Formater" wenn das wie eine Prozentangabe aussieht (PercentageFormatter).
                //Wenn ein tausender trenner gesetzt ist, escapt PhpSpreadsheet das % durch dopplung (%%) und das zerlegt PercentageFormatter.
                //(sprintf, maske wird dyamisch, maske doppelte sich durch das %% -> zwei platzhalter für einen Wert -> fehler)
                if ($thousands_separator && ($raw_suffix === '%' || $raw_suffix === ' %')) {
                    $thousands_separator = '';
                }

                $separator = '';
                if ($thousands_separator) {
                    $separator = '#' . $thousands_separator . '###';
                }

                $prefix = $raw_prefix ? '"' . $raw_prefix . '"' : '';
                $suffix = $raw_suffix ? '"' . $raw_suffix . '"' : '';

                $cell->getStyle()->getNumberFormat()->setFormatCode($prefix . $separator . $decimals . $suffix);
            } elseif ($field instanceof table_object_field_text) {
                $value_binder = $binder;
            }

            $worksheet->setCellValue($coords, $value, $value_binder);


            $align = $field->getAlign();

            if ($align) {
                $cell->getStyle()->getAlignment()->setHorizontal($align);
            }

            $coords[0]++;
        }

        $coords[0]--;
        return $coords;
    }

    public function renderInTableObjectContext(): string
    {
        $spreadsheet = new Spreadsheet();

        $worksheet = $spreadsheet->getActiveSheet();

        [$col_pos, $row_pos] = $this->renderHeader($worksheet, $this->fields);

        $worksheet->setAutoFilter([1, $row_pos, $col_pos, $row_pos]);
        $worksheet->getRowDimension($row_pos)->setRowHeight(22);

        $binder = new StringValueBinder();

        foreach ($this->table->getDaten() as $row) {
            $row_pos++;

            $this->renderBodyRow($worksheet, $this->fields, [1, $row_pos], $row, $binder);
        }

        $writer = new Xlsx($spreadsheet);
        return PhpSpreadsheet::getAsString($writer);
    }
}
