<?php

namespace bqp\table;

use bqp\Utils\HtmlBuilder;

class TableObjectFieldSelectMask
{
    private TableObject $table_object;

    public function __construct(TableObject $table_object)
    {
        $this->table_object = $table_object;
    }

    public function render(): string
    {
        $possible_field_select_entries = [];

        foreach ($this->table_object->getFields() as $field) {
            $possible_field_select_entries[] = [
               'key' => $field->getKey(),
               'label' => $field->getName(),
               'group_label' => $field->getGroupLabel(),
            ];
        }

        $current_field_select = [];

        foreach ($this->table_object->getFieldsForRender() as $field) {
            $current_field_select[] = $field->getKey();
        }

        $current_field_select = json_encode($current_field_select);
        $possible_field_select_entries = json_encode($possible_field_select_entries);

        return '<div
            data-js-module="TableObjectFieldSelectMask"
            data-current-field-select="' . HtmlBuilder::escapeAttribute($current_field_select) . '"
            data-possible-field-select-entries="' . HtmlBuilder::escapeAttribute($possible_field_select_entries) . '"
            data-table-url="' . $this->table_object->makeUrl('filter[field_select]={{field_select}}') . '"
        ></div>';
    }
}
