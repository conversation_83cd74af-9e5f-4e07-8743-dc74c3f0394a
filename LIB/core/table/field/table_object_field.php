<?php

namespace bqp\table\field;

use bqp\Exceptions\DevException;
use bqp\table\TableObject;
use bqp\table\TableObjectParameterTrait;
use bqp\Utils\HtmlBuilder;
use bqp\Utils\StringUtils;

class table_object_field
{
    use TableObjectParameterTrait;

    public const ALIGN_CENTER = 'center';
    public const ALIGN_RIGHT = 'right';
    public const ALIGN_LEFT = 'left';

    /**
     * Identität der Spalte
     */
    protected string $key;

    /**
     * Verknüpft die Spalte mit der Datenquelle. Wenn nicht festgelegt, wird key verwendet.
     * (z.B. in der WWS Datenquelle haben die Spalten als Identität "table.column", die Datenquelle gibt aber nur "column" zurück.)
     */
    protected ?string $data_key = null;


    protected string $name;


    protected $template = null;
    protected $link = null;
    protected $suffix = '';
    protected $prefix = '';

    protected $attributes = [];

    protected $align = '';

    /**
     * @var array
     */
    protected $header_attributes = [];
    protected $order_by = '';

    /**
     * @var TableObject
     */
    protected $table = null;

    protected $rollup = null;
    protected $rollup_complete = null;

    /**
     * Es gibt hier konzeptionell ein Problem. Die SQL DataSource unterstützt mehrdimensionale Daten. Die lassen sich dann mittels table_object_field_multi
     * ausgeben und untersützten auch Rollups. In der Datenquelle und im Rollup werden die Schlüssel geprefixt.
     * SELECT
     *    foo,
     *    [
     *       bar
     *    ] AS sub
     * ..
     *
     * führt zu dem key sub|bar. Das funktioniert in dem Kontext (nötig? unklar), aber wenn ich table_object_field_multi so verwenden will, fällt mir das auf die Füße.
     * Wenn ich das unter Feld foo nenne, und das multi Feld sub, kommt sub|bar raus.
     * @var bool
     */
    public $rollup_hierarchical_keys = true;

    protected $counter = 0;
    protected $id_prefix = 'id';

    protected $required_sql = [];
    protected $help;

    /**
     * @var bool
     */
    protected bool $allow_html_value = false;

    /**
     * legt fest, ob diese Feld standardmäßig in der Tabellenansicht versteckt ist
     */
    protected bool $default_hidden = false;

    /**
     * Erlaubt es die Fleder in Gruppen einzuteilen, wird für die Spaltenauswahl genutzt
     * @var string
     */
    protected string $group_label = 'Sonstiges';
    protected array $metadata = [];

    public function __construct(string $key, string $name)
    {
        $this->setKey($key);

        $this->name = $name;
        $this->header_attributes['rowspan'] = 2;
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function setKey(string $key): self
    {
        $this->key = $key;
        $this->id_prefix = preg_replace('~[^a-z]~i', '', $this->key);

        return $this;
    }

    public function setDataKey(?string $data_key): void
    {
        $this->data_key = $data_key;
    }

    public function getDataKey(): string
    {
        return $this->data_key ?? $this->key;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $value)
    {
        $this->name = $value;

        return $this;
    }

    public function setHelp($help)
    {
        $this->help = $help;

        return $this;
    }

    /**
     * @return TableObject
     */
    public function getTable()
    {
        return $this->table;
    }

    public function renderComplete($daten)
    {
        return $this->render($daten);
    }

    public function render($daten)
    {
        $return = '';

        $return .= '<td' . HtmlBuilder::renderAttributes($this->getAttributesForRender()) . '>';

        $return .= $this->renderValuePre($daten);

        $return .= '</td>';

        return $return;
    }

    public function renderValuePre($daten)
    {
        $this->counter++;
        return $this->renderValue($daten);
    }

    public function renderValue($daten)
    {
        $value = $this->getValue($daten);

        if (!$this->allow_html_value) {
            $value = StringUtils::htmlentities($value);
            $value = nl2br($value);
        }

        $value = $this->prefix . $value . $this->suffix;

        $id = $this->id_prefix . $this->counter;
        $key = $this->getDataKey();
        $field_key = $this->getKey();

        if ($this->template) {
            $value = eval($this->template);
        }

        if ($this->link) {
            $value = eval($this->link);
        }

        return $value;
    }

    public function getValue($daten)
    {
        $value = isset($daten[$this->getDataKey()]) ? $daten[$this->getDataKey()] : '';

        return $value;
    }

    public function renderEmpty()
    {
        $return = '';

        $return .= '<td class="empty"></td>';

        return $return;
    }

    public function renderRollup($daten)
    {
        if (!$this->getRollup()) {
            return false;
        }

        $return = '';

        $return .= '<td' . HtmlBuilder::renderAttributes($this->getAttributesForRender()) . '>';
        $return .= $this->renderRollupValue($daten, 'part');
        $return .= '</td>';

        return $return;
    }

    public function renderRollupComplete($daten)
    {
        if (!$this->getRollupComplete()) {
            return false;
        }

        $return = '';

        $return .= '<td' . HtmlBuilder::renderAttributes($this->getAttributesForRender()) . '>';
        $return .= '<b>' . $this->renderRollupValue($daten, 'complete') . '</b>';
        $return .= '</td>';

        return $return;
    }

    public function renderRollupValue($daten, $typ)
    {
        return $this->prefix . $this->getRollupRawValue($daten, $typ) . $this->suffix;
    }

    public function renderHeader()
    {
        $return = '';

        $return .= '<th' . HtmlBuilder::renderAttributes($this->header_attributes) . '>';

        if ($this->order_by && $this->table->isOrderable()) {
            $order = $this->table->getOrderBy();

            $dir = 'ASC';
            if ($this->order_by == $order['field']) {
                $dir = $order['direction'] === 'ASC' ? 'DESC' : 'ASC';
            }

            $return .= '<a href="' . $this->table->makeUrl('filter[order_by]=' . $this->order_by . '&filter[order_by_dir]=' . $dir) . '">' . $this->getName();

            if ($this->order_by == $order['field']) {
                switch ($order['direction']) {
                    case 'ASC':
                        $return .= ' <img src="/res/images/sort_desc.gif" />';
                        break;
                    case 'DESC':
                        $return .= ' <img src="/res/images/sort_asc.gif" />';
                        break;
                }
            }

            $return .= '</a>';
        } else {
            $return .= $this->getName();
        }

        if ($this->help) {
            $return .= '&nbsp; <a href="#" title="' . htmlentities($this->help) . '">?</a>';
        }

        $return .= '</th>';

        return $return;
    }


    public function renderCsvHeader()
    {
        return strip_tags($this->getName());
    }

    public function renderCsvValue($daten)
    {
        if (!empty($this->formater)) {
            $value = isset($daten[$this->getDataKey()]) ? $daten[$this->getDataKey()] : '';

            return $this->formater->renderValue($value);
        }

        $this->allow_html_value = true;

        $raw_value = $this->renderValue($daten);

        return $this->clearRenderdValueForCsv($raw_value);
    }

    /**
     * bereinitg das Ergebnis von renderValue() so, dass es in einer csv sinnvoll verwendet werden kann
     *
     * @param string $raw_value
     * @return string
     * @todo Das ganze Konzept mit den CSV-Values ist in der Form mist. Die Formatierung der Felder müsste abstrakter
     * gemacht werden. Und direktes HTML nur noch als Sonderfall abgebildet werden.
     *
     */
    protected function clearRenderdValueForCsv(string $raw_value): string
    {
        $value = str_replace('&nbsp;', ' ', $raw_value);
        $value = trim(strip_tags($value));

        if (strlen($value) === 0 && ($pos = strpos($raw_value, 'alt="')) !== false) {
            $pos += 5;
            $value = substr($raw_value, $pos, strpos($raw_value, '"', $pos) - $pos);
        }

        if (strlen($value) === 0 && ($pos = strpos($raw_value, 'title="')) !== false) {
            $pos += 7;
            $value = substr($raw_value, $pos, strpos($raw_value, '"', $pos) - $pos);
        }

        return $value;
    }


    public function renderSubHeader()
    {
        return '';
    }

    public function setOrderable($key)
    {
        if ($key === true) {
            $key = $this->getKey();
        }

        $this->order_by = $key;

        return $this;
    }

    public function getOrderable()
    {
        return $this->order_by;
    }

    public function getHeaderAttributes()
    {
        return $this->header_attributes;
    }

    /**
     * setzt ein templat für die Zelle {{$field_name}} wird gegen entsprechende feld inhalte ersetzt
     * __value__ gegen die eigentliche value mit prefix und suffix
     * __id__ ist eine einzigartige kennung
     *
     * @param string $template
     */
    public function setTemplate($template)
    {
        $this->template = $this->makeEvalFromTemplateString($template);
    }

    public function addLink($template)
    {
        $this->link = $this->makeEvalFromTemplateString($template);
    }

    /**
     * Erzeugt aus einem "Template" ausführbaren PHP Code.
     * {{$test}} wird als $daten['test'] intepretiert
     * {{$test|urlencode}} wird als urlencode($daten['test']) intepretiert
     *
     * @param string $template
     * @return string
     * @todo als eigene klasse auslagern
     */
    protected function makeEvalFromTemplateString($template)
    {
        if (!$template) {
            return '';
        }

        $template = str_replace('\"', '&quot;', $template);
        $template = str_replace('"', '\"', $template);

        $template = preg_replace_callback('~{{\$(.*?)}}~ui', function ($values) {
            if (strpos($values[1], 'this.') === 0) {
                $value = substr($values[1], 5);
                return '".$this->parameters[\'' . $value . '\']."';
            }

            if (strpos($values[1], '|') !== false) {
                list($var, $function) = explode('|', $values[1]);

                return '".' . $function . '($daten[\'' . $var . '\'])."';
            }

            return '".$daten[\'' . $values[1] . '\']."';
        }, $template);

        $template = str_ireplace('__value__', '$value', $template);
        $template = str_ireplace('__id__', '$id', $template);
        $template = str_ireplace('__field_key__', '$key', $template);

        return 'return "' . $template . '";';
    }

    public function setPrefix($prefix, $whitespace = true)
    {
        $this->prefix = $prefix;
        if ($whitespace) {
            $this->prefix .= ' ';
        }
    }

    public function getPrefix(): string
    {
        return $this->prefix;
    }

    public function setSuffix($suffix, $whitespace = true)
    {
        $this->suffix = '';
        if ($whitespace) {
            $this->suffix = ' ';
        }
        $this->suffix .= $suffix;
    }

    public function getSuffix(): string
    {
        return $this->suffix;
    }


    public function setStyle(string $style): void
    {
        $this->attributes['style'] = $style;
    }

    public function addStyle(string $style): void
    {
        if (!isset($this->attributes['style'])) {
            $this->attributes['style'] = '';
        }

        $this->attributes['style'] .= $style;
    }

    public function setHeaderStyle($style)
    {
        $this->header_attributes['style'] = $style;
    }

    public function setHeaderClass($class)
    {
        $this->header_attributes['class'] = $class;
    }

    public function addHeaderClass($class)
    {
        if (empty($this->header_attributes['class'])) {
            $this->header_attributes['class'] = '';
        }
        $this->header_attributes['class'] .= ' ' . $class;
    }

    public function setClass(string $class): void
    {
        $this->attributes['class'] = $class;
    }

    public function addClass(string $class): void
    {
        $this->attributes['class'] = trim($this->getClass() . ' ' . $class);
    }

    public function getClass(): string
    {
        return $this->attributes['class'] ?? '';
    }

    public function setAlign(string $align): void
    {
        $this->align = $align;
    }

    public function getAlign(): ?string
    {
        return $this->align ?: null;
    }


    public function setWidth($width)
    {
        $this->header_attributes['width'] = $width;
    }

    public function countCols()
    {
        return 1;
    }

    public function setHtmlTable(TableObject $table)
    {
        $this->setTable($table);
    }

    public function setTable(TableObject $table)
    {
        $this->table = $table;
    }

    public function eventBeforeRender()
    {
        $this->mergeParameters($this->table->getParameters());
    }

    public function setRollupComplete($method)
    {
        $this->rollup_complete = $method;
    }

    public function getRollupComplete()
    {
        return $this->rollup_complete;
    }

    public function setAllowHtmlValues($value)
    {
        $this->allow_html_value = $value;
    }

    public function getRollupCompleteNew()
    {
        if (method_exists($this, 'getFields')) {
            $rollups = [];

            foreach ($this->getFields() as $field) {
                foreach ($field->getRollupCompleteNew() as $key => $rollup) {
                    if ($this->rollup_hierarchical_keys) {
                        $rollups[$this->getDataKey() . '|' . $key] = $rollup;
                    } else {
                        $rollups[$key] = $rollup;
                    }
                }
            }

            return $rollups;
        }

        if ($this->rollup_complete) {
            return [$this->getDataKey() => $this->rollup_complete];
        }

        return [];
    }

    public function setRollup($method, $complete = true)
    {
        $this->rollup = $method;
        if ($complete) {
            $this->setRollupComplete($method);
        }
    }

    public function getRollup()
    {
        return $this->rollup;
    }

    public function getRollupNew()
    {
        if (method_exists($this, 'getFields')) {
            $rollups = [];

            foreach ($this->getFields() as $field) {
                foreach ($field->getRollupNew() as $key => $rollup) {
                    if ($this->rollup_hierarchical_keys) {
                        $rollups[$this->getDataKey() . '|' . $key] = $rollup;
                    } else {
                        $rollups[$key] = $rollup;
                    }
                }
            }

            return $rollups;
        }

        if ($this->rollup) {
            return [$this->getDataKey() => $this->rollup];
        }

        return [];
    }

    /**
     * hilfs funktion um die rollup value zu bestimmen
     *
     * @depracated die funktion existiert nur wegen PRO_ERTRAG_SUM und steht dem neuen rollup system im weg
     *
     * @param $daten
     * @param $typ
     * @return float|int
     */
    protected function getRollupRawValue($daten, $typ)
    {
        switch ($typ) {
            case 'part':
                $rollup = $this->getRollupNew();
                break;
            case 'complete':
                $rollup = $this->getRollupCompleteNew();
                break;
        }
        $rollup = current($rollup); //legancy function

        if (is_callable($rollup)) {
            return $daten[$this->getDataKey()];
        }

        $rollup = explode('|', $rollup);

        switch ($rollup[0]) {
            case 'PRO_ERTRAG_SUM':
                if (!$daten[$rollup[2]]) {
                    return 0;
                }
                return ($daten[$rollup[1]] / $daten[$rollup[2]]) * 100;
            case 'MIN':
            case 'MAX':
            case 'AVG':
            case 'SUM':
            default:
                return $daten[$this->getDataKey()];
        }
    }

    public function getDatensatzPos()
    {
        return $this->table->getEntriesPerPage() * $this->table->getPage() + $this->counter;
    }

    public function addRequierdSqlFields($fields)
    {
        if (is_string($fields)) {
            $fields = [$fields];
        }

        $this->required_sql = array_merge($this->required_sql, $fields);
    }

    public function getRequierdSqlFields()
    {
        return $this->required_sql;
    }

    /**
     * konvertiert ein table_field in einen anderen Typ
     *
     * @param string table_object_field
     * @return table_object_field
     */
    public function convert($typ)
    {
        if (strpos($typ, '\\') !== false) {
            $to_class = $typ;
        } else {
            $to_class = '\bqp\table\field\table_object_field_' . $typ;
        }

        $new_obj = new $to_class($this->key, $this->name);
        $new_obj->setDataKey($this->data_key);

        if ($this->table) {
            $this->table->convertField($this, $new_obj);
        }

        return $new_obj;
    }

    public function convertByTableConfig(string $field_code): self
    {
        throw new DevException('nope! aktuell nicht implementiert');
//
//        $field = field_manager::getInstance()->getAsTableField($field_code);
//        $this->required_sql = [$field_code]; //@todo macht das sinn? macht die mehtode überhaupt sinn?
//
//        foreach ($this->table->fields as $key => $obj) {
//            if ($obj === $this) {
//                $this->table->fields[$key] = $field;
//            }
//        }
//
//        return $field;
    }

    protected function getAttributesForRender(): array
    {
        $attributes = $this->attributes;

        if ($this->align) {
            $align = 'text-align: ' . $this->align . ';';

            if (isset($attributes['style'])) {
                $attributes['style'] .= ';' . $align;
            } else {
                $attributes['style'] = $align;
            }
        }

        return $attributes;
    }

    public function isDefaultHidden(): bool
    {
        return $this->default_hidden;
    }

    public function setDefaultHidden(bool $default_hidden): void
    {
        $this->default_hidden = $default_hidden;
    }

    public function getGroupLabel(): string
    {
        return $this->group_label;
    }

    public function setGroupLabel(string $group_label): void
    {
        $this->group_label = $group_label;
    }

    public function getMetadata(string $key): mixed
    {
        return $this->metadata[$key] ?? null;
    }

    public function setMetadata(string $key, mixed $value): void
    {
        $this->metadata[$key] = $value;
    }
}
