<?php

namespace bqp\table\field;

class table_object_field_numeric extends table_object_field
{
    protected $decimals = 2;
    protected $decimal_point = ',';
    protected $thousands_separator = ' ';

    /**
     * @var null|TableObjectFieldNumericHighlighter
     */
    protected $highlighter;

    protected $null_to_zero = true;

    public function setFormat($decimals, $decimal_point = ',', $thousands_separator = ' ')
    {
        $this->decimals = $decimals;
        $this->decimal_point = $decimal_point;
        $this->thousands_separator = $thousands_separator;
    }

    public function getDecimals(): int
    {
        return $this->decimals;
    }

    public function getDecimalThousandsSeparator(): string
    {
        return $this->thousands_separator;
    }

    public function renderValue($daten)
    {
        if (!$this->null_to_zero && $daten[$this->getDataKey()] === null) {
            return '';
        }

        $value = $this->prefix . number_format($daten[$this->getDataKey()], $this->decimals, $this->decimal_point, $this->thousands_separator) . $this->suffix;

        $id = $this->id_prefix . $this->counter++;
        $key = $this->getDataKey();
        $field_key = $this->getKey();

        if ($this->highlighter) {
            $raw_value = $daten[$this->getDataKey()];
            $value = '<span' . $this->highlighter->getHighlightHtmlAttributes($raw_value) . '>' . $value . '</span>';
        }

        if ($this->template) {
            $value = eval($this->template);
        }

        if ($this->link) {
            $value = eval($this->link);
        }

        return $value;
    }

    /*public function renderCsvValue($daten) {
        return number_format($daten[$this->getKey()], $this->decimals, ',', '');
    }*/

    public function renderRollupValue($daten, $typ)
    {
        $raw_value = $this->getRollupRawValue($daten, $typ);

        $value = $this->prefix . number_format($raw_value, $this->decimals, $this->decimal_point, $this->thousands_separator) . $this->suffix;

        if ($this->highlighter) {
            $value = '<span' . $this->highlighter->getHighlightHtmlAttributes($raw_value) . '>' . $value . '</span>';
        }

        return $value;
    }

    public function setHighlight(bool $highlight): self
    {
        if ($highlight) {
            $this->setHighlighter(new TableObjectFieldNumericHighlighterDefault());
        } else {
            $this->setHighlighter(null);
        }

        return $this;
    }

    public function setHighlighter(?TableObjectFieldNumericHighlighter $highlighter): void
    {
        $this->highlighter = $highlighter;
    }

    public function setAutoConvert($auto_convert)
    {
        $this->null_to_zero = $auto_convert;

        return $this;
    }

    public function setNullToZero(bool $null_to_zero): void
    {
        $this->null_to_zero = $null_to_zero;
    }
}
