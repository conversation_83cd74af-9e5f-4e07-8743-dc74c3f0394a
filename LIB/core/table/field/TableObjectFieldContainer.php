<?php

namespace bqp\table\field;

use bqp\field_manager;
use bqp\table\Exception\TableObjectFieldAmbiguousException;
use bqp\table\Exception\TableObjectFieldNotExistsException;

/**
 * @todo es sind hier bewusst trigger_errors eingerichtet die auf key vs data_key gehen. Wenn die nicht mehr auslösen, die codepfade clean auf "key" umstellen und die sonderbehandlung für data_key raus
 */
trait TableObjectFieldContainer
{
    /**
     * @var table_object_field[]
     */
    protected array $fields = [];

    public function countCols(): int
    {
        $cols = 0;

        if (method_exists($this, 'getFieldsForRender')) {
            $fields = $this->getFieldsForRender();
        } else {
            $fields = $this->fields;
        }

        foreach ($fields as $field) {
            $cols += $field->countCols();
        }
        return $cols;
    }

    public function countFields(): int
    {
        return $this->countCols();
    }

    /**
     * @see tryGetFieldByKey()
     * @throws TableObjectFieldNotExistsException
     */
    public function getFieldByKey(string $key): table_object_field
    {
        $field = $this->getFieldByKeyOrDataKey($key);

        if ($field) {
            return $field;
        }

        throw new TableObjectFieldNotExistsException('Field not found: ' . $key);
    }

    public function tryGetFieldByKey(string $key): ?table_object_field
    {
        try {
            return $this->getFieldByKeyOrDataKey($key);
        } catch (TableObjectFieldAmbiguousException $e) {
            trigger_error($e->getMessage(), E_USER_WARNING);
            return null;
        }
    }

    private function getFieldByKeyOrDataKey(string $key): ?table_object_field
    {
        $field = $this->findFieldByKey($key, $this->fields);
        if ($field) {
            return $field;
        }

        $matches = $this->findFieldsByDataKey($key, $this->fields);
        if (!$matches) {
            return null;
        }

        if (count($matches) > 1) {
            $matched_keys = array_map(static fn (table_object_field $field): string => $field->getKey(), $matches);

            throw new TableObjectFieldAmbiguousException('Field lookup by data_key "' . $key . '" is ambiguous. Matching keys: ' . implode(', ', $matched_keys));
        }

        $field = $matches[0];

        trigger_error('Deprecated: Field lookup by data_key "' . $key . '" matched field key "' . $field->getKey() . '". Use the field key instead.', E_USER_DEPRECATED);

        return $field;
    }

    /**
     * @param table_object_field[] $fields
     */
    private function findFieldByKey(string $key, array $fields): ?table_object_field
    {
        foreach ($fields as $field) {
            if ($field->getKey() === $key) {
                return $field;
            }

            if ($field instanceof table_object_field_multi) {
                $temp = $this->findFieldByKey($key, $field->getFields());

                if ($temp) {
                    return $temp;
                }
            }
        }

        return null;
    }

    /**
     * @param table_object_field[] $fields
     * @return table_object_field[]
     */
    private function findFieldsByDataKey(string $data_key, array $fields): array
    {
        $matches = [];

        foreach ($fields as $field) {
            if ($field->getDataKey() === $data_key) {
                $matches[] = $field;
            }

            if ($field instanceof table_object_field_multi) {
                $matches = array_merge($matches, $this->findFieldsByDataKey($data_key, $field->getFields()));
            }
        }

        return $matches;
    }


    /**
     * @return table_object_field[]
     */
    public function getFields(): array
    {
        return $this->fields;
    }

    public function addField(table_object_field $field): table_object_field
    {
        $this->fields[] = $field;

        return $field;
    }

    public function addFieldByTableConfig(string $field_code, string $alias): table_object_field
    {
        $field = field_manager::getInstance()->getAsTableField($field_code, $alias);

        $this->addField($field);

        return $field;
    }

    public function removeFieldByKey(string $key): void
    {
        $field = $this->findFieldByKey($key, $this->fields);

        if ($field) {
            foreach ($this->fields as $id => $temp_field) {
                if ($temp_field->getKey() === $key) {
                    unset($this->fields[$id]);
                    return;
                }

                if ($temp_field instanceof table_object_field_multi) {
                    $temp_field->removeFieldByKey($key);
                }
            }

            return;
        }

        try {
            $field = $this->getFieldByKeyOrDataKey($key);
        } catch (TableObjectFieldAmbiguousException $e) {
            trigger_error($e->getMessage(), E_USER_WARNING);
            return;
        }

        if ($field) {
            $this->removeFieldByKey($field->getKey());
        }
    }

    public function hideFieldByKey(string $key): void
    {
        $field = $this->findFieldByKey($key, $this->fields);
        if ($field) {
            foreach ($this->fields as $temp_field) {
                if ($temp_field->getKey() === $key) {
                    $temp_field->setDefaultHidden(true);
                    return;
                }

                if ($temp_field instanceof table_object_field_multi) {
                    $temp_field->hideFieldByKey($key);
                }
            }

            return;
        }

        try {
            $field = $this->getFieldByKeyOrDataKey($key);
        } catch (TableObjectFieldAmbiguousException $e) {
            trigger_error($e->getMessage(), E_USER_WARNING);
            return;
        }

        if ($field) {
            $this->hideFieldByKey($field->getKey());
        }
    }

    public function addFieldBeforeFirst(table_object_field $field): void
    {
        array_unshift($this->fields, $field);
    }

    public function addFieldAfter(table_object_field $new_field, string $key): void
    {
        $fields = [];
        $matched = false;

        foreach ($this->fields as $field) {
            $fields[] = $field;
            if (!$matched && ($field->getKey() === $key || $field->getDataKey() === $key)) {
                if ($field->getKey() !== $key && $field->getDataKey() === $key) {
                    trigger_error('Deprecated: addFieldAfter() matched by data_key "' . $key . '" for field key "' . $field->getKey() . '". Use the field key instead.', E_USER_DEPRECATED);
                }
                $fields[] = $new_field;
                $matched = true;
            }
        }

        $this->fields = $fields;
    }

    public function addFieldBefore(table_object_field $new_field, string $key): void
    {
        $fields = [];
        $matched = false;

        foreach ($this->fields as $id => $field) {
            if (!$matched && ($field->getKey() === $key || $field->getDataKey() === $key)) {
                if ($field->getKey() !== $key && $field->getDataKey() === $key) {
                    trigger_error('Deprecated: addFieldBefore() matched by data_key "' . $key . '" for field key "' . $field->getKey() . '". Use the field key instead.', E_USER_DEPRECATED);
                }
                $fields[] = $new_field;
                $matched = true;
            }
            $fields[] = $field;
        }

        $this->fields = $fields;
    }

    public function addFieldToPos(table_object_field $new_field, int $pos): void
    {
        $fields = [];

        $pos_act = 0;

        if ($pos < 0) {
            $pos = count($this->fields) + $pos;
        }

        foreach ($this->fields as $id => $field) {
            if ($pos_act == $pos) {
                $fields[] = $new_field;
                $pos_act++;
            }
            $fields[] = $field;

            $pos_act++;
        }

        $this->fields = $fields;
    }

    public function convertField($old_field, $new_field): void
    {
        foreach ($this->fields as $key => $obj) {
            if (method_exists($obj, 'convertField')) {
                $obj->convertField($old_field, $new_field);
            } elseif ($obj === $old_field) {
                $this->fields[$key] = $new_field;
            }
        }
    }
}
