<?php

namespace bqp\table\field;

use bqp\table\field\table_object_field;

class table_object_field_pos extends table_object_field
{
    public function __construct($key = 'MAKRO.global.pos', $name = 'Pos')
    {
        if (!$key) {
            $key = rand(1000, 9999);
        }
        parent::__construct($key, $name);
    }

    public function renderValue($daten)
    {
        return $this->getDatensatzPos();
    }

    public function renderCsvValue($daten)
    {
        $this->counter++;
        return $this->getDatensatzPos();
    }
}
