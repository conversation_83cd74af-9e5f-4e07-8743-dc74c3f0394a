<?php

namespace bqp\table\field;

class table_object_field_date extends table_object_field
{
    protected $format = 'date';
    protected $format_output = 'd.m.Y';

    public function setFormat($format)
    {
        $this->format = $format;
    }

    public function setFormatOutput($format)
    {
        $this->format_output = $format;
    }

    public function renderValue($daten)
    {
        $value = $daten[$this->getDataKey()];

        switch ($value) {
            case null:
            case '':
            case '0':
            case '0000-00-00':
            case '0000-00-00 00:00:00':
                $value = '';
                break;
            default:
                switch ($this->format) {
                    case 'date_ym':
                    case 'date_yw':
                    case 'date_yq':
                        $year = substr($value, 0, 4);
                        $month = substr($value, 4, 2);

                        $value = $month . '.' . $year;
                        break;
                    case 'date':
                        $date = new \bqp\Date\DateObj($value);
                        $value = $date->format($this->format_output);
                        break;
                    case 'unixtime':
                    case 'unixtimestamp':
                        $value = date($this->format_output, $value);
                        break;
                }
        }

        return $this->prefix . $value . $this->suffix;
    }

    public function renderCsvValue($daten)
    {
        $value = $daten[$this->getDataKey()];

        switch ($this->format) {
            case 'date_ym':
            case 'date_yw':
            case 'date_yq':
                $year = substr($value, 0, 4);
                $month = substr($value, 4, 2);

                $value = $month . '.' . $year;
                break;
            case 'date':
                $date = strtotime($value);
                $value = date($this->format_output, $date);
                break;
            case 'unixtime':
            case 'unixtimestamp':
                $value = date($this->format_output, $value);
                break;
        }

        return $this->prefix . $value . $this->suffix;
    }
}
