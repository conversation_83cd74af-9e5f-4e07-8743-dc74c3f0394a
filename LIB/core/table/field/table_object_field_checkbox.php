<?php

namespace bqp\table\field;

use bqp\Utils\HtmlBuilder;

class table_object_field_checkbox extends table_object_field
{
    protected $checkbox_name;
    protected $checkbox_id;
    protected $checkbox_value = '1';
    protected $onchange_js = '';
    private $checkbox_all_onchange_js = '';

    protected $checkbox_all = false;
    protected $checkbox_all_show_name = false;
    protected $multi_select = false;

    protected $unique_id = '';

    private bool $disabled = false;

    /**
     * @var callable
     */
    protected $checked_callback = null;

    /**
     * @var callable
     */
    protected $checkbox_condition_callback = null;

    /**
     * Hilfsvariable auf der das orginal Template abgelegt wird (siehe checkbox_condition_callback)
     * @var string
     */
    protected $real_template;

    private $data_attributes = [];

    public function __construct($key = '', $name = '')
    {
        if (!$key) {
            $key = rand(1000, 9999);
        }
        parent::__construct($key, $name);

        $this->unique_id = 'che_' . $key;

        $this->template();
    }

    public function setCheckboxName($name)
    {
        $this->checkbox_name = $name;

        $this->template();
    }

    public function setCheckboxId($id)
    {
        $this->checkbox_id = $id;

        $this->template();
    }

    public function setCheckboxValue($value)
    {
        $this->checkbox_value = $value;

        $this->template();
    }

    public function setJsOnChange($action)
    {
        $this->onchange_js = $action;

        $this->template();
    }

    public function setCheckboxAllJsOnChange($action)
    {
        $this->checkbox_all_onchange_js = $action;
    }

    public function setCheckedCallback(callable $callback)
    {
        $this->checked_callback = $callback;

        $this->template();
    }

    public function setDisabled(bool $disabled = true): void
    {
        $this->disabled = $disabled;

        $this->template();
    }

    public function hightlightTableRowOnChecked(string $class = 'yellow'): void
    {
        $this->setJsOnChange("if(this.checked){jQuery(this).closest('tr').addClass('" . $class . "')} else {jQuery(this).closest('tr').removeClass('" . $class . "')}");
    }

    public function addDataAttribute(string $key, string $value): void
    {
        $this->data_attributes[$key] = $value;

        $this->template();
    }

    /**
     * callback welches festlegt ob für die aktuelle Zeile ein Checkbox angezeigt werden soll (true) oder nicht (false)
     *
     * @param callable $callback
     */
    public function setCheckboxConditionCallback(callable $callback): void
    {
        $this->checkbox_condition_callback = $callback;
    }

    /**
     * im Tabellen Header wird statt des Names eine Checkbox eingefügt mit
     * der alle Checkbox gesetzt werden können
     * aktiviert automatisch multi select (mit shift markieren)
     *
     * @param bool $value
     * @param bool $show_name
     */
    public function setCheckboxAll($value, bool $show_name = false): void
    {
        $this->checkbox_all = $value;
        if ($value) {
            $this->multi_select = true;
        }

        if ($this->checkbox_all) {
            $this->checkbox_all_show_name = $show_name;
        }
    }


    /**
     * mehrer checkboxen mit shift markieren
     *
     * @param boolean $multi_select
     */
    public function setMultiSelect($multi_select)
    {
        $this->multi_select = $multi_select;
    }

    protected function template()
    {
        $attributes = [];
        if ($this->checkbox_id) {
            $attributes['id'] = $this->checkbox_id;
        }
        if ($this->checkbox_value) {
            $attributes['value'] = $this->checkbox_value;
        }
        if ($this->checkbox_name) {
            $attributes['name'] = $this->checkbox_name;
        }
        if ($this->onchange_js) {
            $attributes['onchange'] = $this->onchange_js;
        }
        if ($this->checked_callback) {
            $attributes['checked'] = 'XXXCHECKEDXXX';
        }
        if ($this->disabled) {
            $attributes['disabled'] = 'disabled';
        }

        if ($this->data_attributes) {
            foreach ($this->data_attributes as $key => $value) {
                $attributes['data-' . $key] = $value;
            }
        }

        $attributes['class'] = $this->unique_id;

        $attributes = HtmlBuilder::renderAttributes($attributes);

        $template = '<input type="checkbox" ' . $attributes . ' />';

        $template = trim($this->getPrefix() . ' ' . $template . ' ' . $this->getSuffix());

        $this->setTemplate($template);

        $this->real_template = $this->template;
    }

    public function render($daten)
    {
        if ($this->checkbox_condition_callback) {
            if (call_user_func($this->checkbox_condition_callback, $daten)) {
                $this->template = $this->real_template;
            } else {
                $this->template = '';
            }
        }

        return parent::render($daten);
    }

    public function renderHeader()
    {
        $return = '';

        $return .= '<th' . HtmlBuilder::renderAttributes($this->header_attributes) . '>';
        if ($this->checkbox_all) {
            if ($this->checkbox_all_show_name) {
                $return .= $this->getName() . ' <input type="checkbox" onchange="jQuery(\'.' . $this->unique_id . '\').attr(\'checked\', checked);' . $this->checkbox_all_onchange_js . '">';
            } else {
                $return .= '<input type="checkbox" onchange="jQuery(\'.' . $this->unique_id . '\').attr(\'checked\', checked);' . $this->checkbox_all_onchange_js . '">';
            }
        } else {
            $return .= $this->getName();
        }
        $return .= '</th>';

        return $return;
    }

    public function renderValue($daten)
    {
        $value = parent::renderValue($daten);
        if (!$this->checked_callback) {
            return $value;
        }

        $checked = call_user_func($this->checked_callback, $daten);

        if ($checked) {
            $value = str_replace('checked="XXXCHECKEDXXX"', 'checked="checked"', $value);
        } else {
            $value = str_replace('checked="XXXCHECKEDXXX"', '', $value);
        }

        return $value;
    }

    public function eventBeforeRender()
    {
        parent::eventBeforeRender();

        if ($this->multi_select) {
            $name = $this->checkbox_name;
            $name = preg_replace('~\[.+~', '[', $name);

            $this->table->addExtraJavascript('wws.checkboxSelectMulti(\'' . $name . '\');');
        }
    }
}
