<?php

namespace bqp\table\field;

use bqp\table\TableObject;
use bqp\table\field\table_object_field;

class table_object_field_mapping extends table_object_field
{
    protected $mapping = [];

    protected $default_value = '';

    public function getValue($daten)
    {
        $value = $daten[$this->getDataKey()];

        if (isset($this->mapping[$value])) {
            return $this->mapping[$value];
        }

        return $this->default_value;
    }

    public function setMapping($mapping)
    {
        if (is_callable($mapping)) {
            $this->mapping = call_user_func($mapping);
        } else {
            $this->mapping = $mapping;
        }
    }

    public function setDefaultValue($default_value)
    {
        $this->default_value = $default_value;
    }
}
