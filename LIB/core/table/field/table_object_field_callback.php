<?php

namespace bqp\table\field;

use bqp\table\TableObject;
use bqp\table\field\table_object_field;

class table_object_field_callback extends table_object_field
{
    protected $callback;

    public function setCallback($callback)
    {
        if (is_callable($callback)) {
            $this->callback = $callback;
        } elseif (is_array($callback) and isset($callback[0]['factory'])) {
            $factory_name = $callback[0]['factory'];

            if (method_exists($factory_name, 'getInstance')) {
                $factory = $factory_name::getInstance();
            } else {
                $factory = $factory_name;
            }

            unset($callback[0]['factory']);
            $method_name = $callback[0][0];
            unset($callback[0][0]);
            $instance = call_user_func_array([$factory, $method_name], $callback[0]);

            $this->callback = [$instance, $callback[1]];
        } elseif (is_array($callback) and isset($callback[0]['di'])) {
            $instance = \service_loader::getDiContainer()->get($callback[0]['di']);
            $this->callback = [$instance, $callback[1]];
        } else {
            var_dump($callback);
            throw new \bqp\Exceptions\DevException('not callable callback');
        }

        return $this;
    }


    public function render($daten)
    {
        $temp = call_user_func($this->callback, $daten, $this->getDataKey(), $this);

        if (!is_array($temp)) {
            $temp = ['value' => $temp];
        }

        $return = '';

        $attributes = $this->getAttributesForRender();
        if (isset($temp['style'])) {
            //mit bestenden Style mergen -> das ist aktuell nur wegen dem align hier. Das könnte langfristig nach hinten losgehen.
            //eigentlich müssten die styles pro ruel gemergt werden. Das sprengt hier aus basis von strings aber den Rahmen und wäre aus Basis von strings auch nicht sinnvoll.
            if (!isset($attributes['style'])) {
                $attributes['style'] = '';
            }

            $attributes['style'] .= $temp['style'];
        }

        if (isset($temp['class'])) {
            $attributes['class'] = $temp['class'];
        }

        if (isset($temp['onclick'])) {
            $attributes['onclick'] = $temp['onclick'];
        }

        $return .= '<td' . \bqp\Utils\HtmlBuilder::renderAttributes($attributes) . '>';
        $return .= $this->renderValue($daten, $temp['value']);
        $return .= '</td>';

        return $return;
    }

    public function renderCsvValue($daten)
    {
        $temp = call_user_func($this->callback, $daten, $this->getDataKey(), $this);

        if (!is_array($temp)) {
            $temp = ['value' => $temp];
        }

        $renderd = '';

        if (isset($temp['csv_value'])) {
            $renderd = $this->renderValue($daten, $temp['csv_value']);
        } else {
            $renderd = $this->renderValue($daten, $temp['value']);
        }

        return $this->clearRenderdValueForCsv($renderd);
    }

    public function renderValue($daten, $value = '')
    {
        $id = $this->id_prefix . $this->counter;
        $key = $this->getDataKey();
        $field_key = $this->getKey();

        if ($this->template) {
            $value = eval($this->template);
        }
        if ($this->link) {
            $value = eval($this->link);
        }

        $value = $this->prefix . $value . $this->suffix;

        return $value;
    }
}
