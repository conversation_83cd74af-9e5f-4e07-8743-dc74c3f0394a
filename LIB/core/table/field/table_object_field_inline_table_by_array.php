<?php

namespace bqp\table\field;

class table_object_field_inline_table_by_array extends table_object_field_multi
{
    public function renderComplete($daten)
    {
        $result = [];

        $ext_result = $daten[$this->getDataKey()];

        foreach ($ext_result as $ext_daten) {
            $temp = array_merge($daten, $ext_daten);
            $result[] = $this->render($temp);
        }

        if (!$result) {
            $result[] = $this->renderEmpty();
        }

        return $result;
    }
}
