<?php

namespace wws\buchhaltung\OposAddison;

use bqp\db\db_generic;
use bqp\form\form_element_button;
use bqp\form\form_element_checkbox;
use bqp\form\form_element_hidden;
use bqp\form\form_element_select;
use bqp\form\form_element_textarea;
use bqp\form\wws_form;
use bqp\form\WwsFormAjax;
use bqp\table\ExtraRow;
use bqp\table\field\FieldActionShow;
use bqp\table\field\TableObjectFieldActions;
use bqp\table\Renderer\TableObjectRenderer;
use bqp\table\TableObject;
use bqp\table\TableObjectPersistent;
use persistent_container;

class TableGeneratorOposAccountList
{
    private db_generic $db;
    private OposListAddisonRepository $opos_list_repository;

    /**
     * @var bool
     */
    private $filter_ignore_even_accounts;

    /**
     * @var string
     */
    private $filter_opos_list_version;

    /**
     * @var int
     */
    private $filter_customer_id;

    /**
     * @var float
     */
    private $filter_amount;

    /**
     * @var string
     */
    private $filter_haben_soll;

    /**
     * @var int
     */
    private $filter_zahlungsart;

    /**
     * @var string
     */
    private $filter_year;

    /**
     * @var
     */
    private $filter_opos_status;

    /**
     * @var string
     */
    private $default_order_by = '
        buchhaltung_opos_addison.opos_list_version,
        buchhaltung_opos_addison.customer_id
    ';

    public function __construct(
        db_generic $db,
        OposListAddisonRepository $opos_list_repository
    ) {
        $this->db = $db;
        $this->opos_list_repository = $opos_list_repository;
    }


    public function setFilterOposStatus(array $opos_statuse): void
    {
        $this->filter_opos_status = $opos_statuse;
    }

    public function setFilterYear(string $year): void
    {
        $this->filter_year = $year;
    }

    public function setFilteZahlungsart(int $zahlungsart): void
    {
        $this->filter_zahlungsart = $zahlungsart;
    }

    public function setFilterHabenSoll(string $haben_soll): void
    {
        $this->filter_haben_soll = $haben_soll;
    }

    public function setFilterAmount(float $amount): void
    {
        $this->filter_amount = $amount;
    }

    public function setFilterCustomerId(int $customer_id): void
    {
        $this->filter_customer_id = $customer_id;
    }

    public function setFilterOposListVersion(string $opos_list_version): void
    {
        $this->filter_opos_list_version = $opos_list_version;
    }

    public function setFilterIgnoreEvenAccounts(bool $ignore_even_accounts): void
    {
        $this->filter_ignore_even_accounts = $ignore_even_accounts;
    }

    public function setDefaultOrderBy(string $order_by): void
    {
        $this->default_order_by = $order_by;
    }

    public function getSqlQuery(): string
    {
        $join_orders = false;

        $where = 1;

        if ($this->filter_opos_list_version) {
            $where .= " AND buchhaltung_opos_addison.opos_list_version = '" . $this->db->escape($this->filter_opos_list_version) . "'";
        }

        if ($this->filter_ignore_even_accounts) {
            $where .= " AND buchhaltung_opos_addison.soll != buchhaltung_opos_addison.haben ";
        }

        if ($this->filter_opos_status) {
            $where .= ' AND buchhaltung_opos_addison.opos_status IN (' . $this->db->in($this->filter_opos_status) . ') ';
        }

        if ($this->filter_year) {
            $where .= ' AND FIND_IN_SET(' . $this->filter_year . ', buchhaltung_opos_addison.opos_years) ';
        }

        if ($this->filter_zahlungsart) {
            $join_orders = true;
            $where .= ' AND orders.zahlungs_id = ' . $this->filter_zahlungsart;
        }

        switch ($this->filter_haben_soll) {
            case 'haben':
                $where .= ' AND buchhaltung_opos_addison.haben != 0';
                break;
            case 'soll':
                $where .= ' AND buchhaltung_opos_addison.soll != 0';
                break;
        }

        if ($this->filter_amount) {
            $where .= ' AND (ABS(buchhaltung_opos_addison.haben - ' . $this->filter_amount . ') < 0.05 OR
                             ABS(buchhaltung_opos_addison.soll - ' . $this->filter_amount . ') < 0.05) ';
        }

        if ($this->filter_customer_id) {
            $where .= ' AND buchhaltung_opos_addison.customer_id = ' . $this->filter_customer_id;
        }

        $join = '';
        if ($join_orders) {
            $join = ' INNER JOIN buchhaltung_opos_addison_orders ON (
                    buchhaltung_opos_addison.opos_list_version = buchhaltung_opos_addison_orders.opos_list_version AND
                    buchhaltung_opos_addison.customer_id = buchhaltung_opos_addison_orders.customer_id)
                    INNER JOIN
                    orders ON (buchhaltung_opos_addison_orders.order_id = orders.order_id)';
        }

//        $customer_id_filter = $this->db->query("
//            SELECT
//                buchhaltung_opos_addison.customer_id
//            FROM
//                buchhaltung_opos_addison
//            WHERE
//                buchhaltung_opos_addison.opos_list_version = '2018.2' AND
//                FIND_IN_SET(2018, buchhaltung_opos_addison.opos_years) AND
//                buchhaltung_opos_addison.soll != buchhaltung_opos_addison.haben
//        ")->asSingleArray();
//
//        $where .= ' AND buchhaltung_opos_addison.customer_id IN ('.$this->db->in($customer_id_filter).')';

        $sql = "
            SELECT 
                DISTINCT
                buchhaltung_opos_addison.customer_id,
                customers.shop_id,
                customers.customer_nr,
                buchhaltung_opos_addison.opos_status,
                buchhaltung_opos_addison.opos_list_version,
                buchhaltung_opos_addison_status.opos_status_description,
                buchhaltung_opos_addison.opos_rows_raw,
                buchhaltung_opos_addison.opos_bemerkung,
                buchhaltung_opos_addison.soll,
                buchhaltung_opos_addison.haben,
                buchhaltung_opos_addison.in_bearbeitung
            FROM
                buchhaltung_opos_addison INNER JOIN
                buchhaltung_opos_addison_status ON (buchhaltung_opos_addison.opos_status = buchhaltung_opos_addison_status.opos_status) LEFT JOIN
                customers ON (buchhaltung_opos_addison.customer_id = customers.customer_id)
                $join
            WHERE
                $where
            ORDER BY
                $this->default_order_by
        ";

        return $sql;
    }

    /**
     * @param string|null|persistent_container $persistent_container
     * @return TableObject
     */
    public function buildTableObject($persistent_container): TableObject
    {
        $sql = $this->getSqlQuery();

        $table = new TableObjectPersistent($sql, $persistent_container);
        $table->setCaption('Konten');
        $table->removeFieldByKey('opos_rows_raw');
        $table->removeFieldByKey('opos_status');
        $table->removeFieldByKey('opos_bemerkung');
        $table->removeFieldByKey('customer_id');
        $table->removeFieldByKey('shop_id');

        $field = new TableObjectFieldActions();

        $action = new FieldActionShow('/ax/customer/customer_bank_transactions/transactions/?customer_id={{$customer_id}}', 'Buchungen');
        $action->setTarget('_blank');
        $field->add($action);

        $action = new FieldActionShow('/ax/customer/customer/?customer_id={{$customer_id}}', 'Kunden');
        $action->setTarget('_blank');
        $field->add($action);

        $table->addField($field);

        $opos_form = $this->form_opos();

        $extra = new class($opos_form) extends ExtraRow {
            /**
             * @var OposListAddisonRenderer
             */
            private $opos_list_renderer;

            /**
             * @var wws_form
             */
            private $opos_form;

            public function __construct(wws_form $opos_form)
            {
                $this->opos_list_renderer = new OposListAddisonRenderer();
                $this->opos_form = $opos_form;
            }

            public function render(TableObjectRenderer $renderer, array $row = []): string
            {
                $size = (count($renderer->getTable()->getFieldsForRender()));

                $return = '';
                $return .= '<tr>';
                //$return .= '<td colspan="2"></td>';
                $return .= '<td colspan="' . $size . '">';
                $return .= $this->opos_list_renderer->render($row['opos_rows_raw']);

                $return .= '</td>';
                $return .= '</tr>';

                $return .= '<tr style="border-bottom: 2px solid #000;">';
                //$return .= '<td colspan="2"></td>';
                $return .= '<td colspan="' . $size . '">';
                $this->opos_form->setValueByName('customer_id', $row['customer_id']);
                $this->opos_form->setValueByName('opos_bemerkung', $row['opos_bemerkung']);
                $this->opos_form->setValueByName('opos_status', $row['opos_status']);
                $this->opos_form->setValueByName('in_bearbeitung', $row['in_bearbeitung']);

                $return .= $this->opos_form->render();

                $return .= '</td>';
                $return .= '</tr>';

                return $return;
            }
        };
        $extra->setLocation($extra::LOCATION_AFTER);
        $table->addExtraRow($extra);

        return $table;
    }


    public function form_opos(): wws_form
    {
        $form = new WwsFormAjax();
        $form->setAction('/ax/buchhaltung/addison_opos/list/list/');

        $field = new form_element_hidden();
        $field->setName('customer_id');
        $form->add($field);

        $select = new form_element_select();
        $select->setName('opos_status');
        $select->setLabel('Status');
        $select->setOptionsSimple($this->opos_list_repository->getOposStatusNames());
        $form->add($select);

        $field = new form_element_textarea();
        $field->setName('opos_bemerkung');
        $field->setLabel('Bemerkung');

        $form->add($field);

        $field = new form_element_checkbox();
        $field->setName('in_bearbeitung');
        $field->setLabel('in Bearbeitung');
        $form->add($field);

        $field = new form_element_button();
        $field->setValue('Speichern');
        $field->setName('action_opos');
        $form->add($field);

        return $form;
    }
}
