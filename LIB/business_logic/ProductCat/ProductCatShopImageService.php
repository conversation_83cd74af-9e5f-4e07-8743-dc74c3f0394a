<?php

namespace wws\ProductCat;

use bqp\Exceptions\SecurityException;
use bqp\Utils\StringUtils;

class ProductCatShopImageService
{
    private string $path;

    public function __construct()
    {
        $this->path = \service_loader::getConfigRegistry()->get('system')->getString('res_dir') . '/cat_logos/';
    }

    public function delete(ProductCat $product_cat): void
    {
        $filename = $product_cat->getCatPicture();

        if (str_contains('/', $filename)) {
            throw new SecurityException();
        }

        if ($filename && file_exists($this->path . $filename)) {
            unlink($this->path . $filename);
        }

        $product_cat->setCatPicture('');
        $product_cat->save();
    }

    public function upload(ProductCat $product_cat, string $blob): void
    {
        if ($product_cat->getCatPicture()) {
            $this->delete($product_cat);
        }

        $keyword = StringUtils::limit($product_cat->getCatNameWebshop() ?: $product_cat->getCatName(), 40);

        $filename = StringUtils::slug($keyword) . '.' . rand(10000, 99999) . '.' . $product_cat->getCatId() . '.webp';

        $image = new \bqp\GdImage\GdImage($blob);
        $image->resize()->maxHeight(200)->maxWidth(200)->crop(false)->execute();

        $image->save('webp', $this->path . $filename);

        $product_cat->setCatPicture($filename);
        $product_cat->save();
    }

    public function getUrl(ProductCat $product_cat): ?string
    {
        $file = $product_cat->getCatPicture();

        if (!$file) {
            return null;
        }

        return '/r/cat_logos/' . $file;
    }
}
