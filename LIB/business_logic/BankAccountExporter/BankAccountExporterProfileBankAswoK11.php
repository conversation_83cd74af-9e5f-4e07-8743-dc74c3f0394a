<?php

namespace wws\BankAccountExporter;

use bqp\Utils\StringUtils;

class BankAccountExporterProfileBankAswoK11 extends BankAccountExporterProfileGeneric
{

    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        $record->setAccount($this->getSrcAccount());
        $record->setReceipt('');
        $record->setBookingText(StringUtils::limit($row['text_reference_1'] ?: $row['text_reference_2'], 60));
        $record->setOffsetAccount('-1');

        return $record;
    }


    private function getReferenceMisc(array $row): string
    {
        $reference = '';

        if (trim($row['sender_name'])) {
            $reference .= $row['sender_name'] . ' - ';
        }

        if ($row['text_reference_1']) {
            $reference .= $row['text_reference_1'];
        } else {
            $reference .= $row['text_reference_2'];
        }
        return StringUtils::limit($reference, 60);
    }

    private function getReferenceDebtor(array $row): string
    {
        if ($row['auftnr'] && $row['customer_id']) {
            return $row['auftnr'] . ' ' . $row['customer_id'] . '-KE';
        }

        if (!$row['auftnr'] && $row['customer_id_transaction']) {
            echo 'hallo!';
            if (!empty($row['classificator_extra'])) {
                //lokes like json?
                if ($row['classificator_extra'][0] === '{') {
                    $extra_values = json_decode($row['classificator_extra'], true);

                    $db = \db::getInstance();

                    if (isset($extra_values['order_ids'])) {
                        $auftnrs = $db->query("
                            SELECT
                                orders.auftnr
                            FROM
                                orders
                            WHERE
                                orders.order_id IN (" . $db->in($extra_values['order_ids']) . ")
                        ")->asSingleArray();

                        $auftnr = implode('/', $auftnrs);

                        // max len validation bypass
                        $auftnr = StringUtils::limit($auftnr, 33) . '...';

                        return $auftnr . ' ' . $row['customer_id_transaction'] . '-KE';
                    }
                }
            }
        }

        if ($row['text_reference_1']) {
            $reference = $row['text_reference_1'];
        } else {
            $reference = $row['text_reference_2'];
        }
        //$reference .= ' ' . StringUtils::limit($row['sender_name'], 20);

        return StringUtils::limit($reference, 60);
    }
}
