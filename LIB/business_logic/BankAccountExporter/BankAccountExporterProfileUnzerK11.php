<?php

namespace wws\BankAccountExporter;

use wws\BankAccount\BankTransaction;

class BankAccountExporterProfileUnzerK11 extends BankAccountExporterProfileGeneric
{
    public function fillRecord(BankAccountExportRecord $record, array $row): BankAccountExportRecord
    {
        $record->setReceipt('');
        $record->setAccount($this->src_account);

        switch ($row['transaction_type']) {
            case BankTransaction::TRANSACTION_TYPE_FEE:
            case BankTransaction::TRANSACTION_TYPE_TRANSIT:
            case BankTransaction::TRANSACTION_TYPE_MISC:
                $record->setSkipRecord(true);
                return $record;
            case BankTransaction::TRANSACTION_TYPE_DEBITOR:
                $buchungstext = $row['auftnr'] . ' ' . $row['customer_id'] . '-KE - ' . $row['settlement_reference'];

                $record->setBookingTextIfEmpty($buchungstext);
                $record->setOffsetAccountIfEmpty($row['debtor_account']);
                $record->setReceipt($row['auftnr']);
                return $record;
            default:
                throw new BankAccountExporterProfileException('unbekannter transaction_type');
        }
    }
}
