<?php

namespace wws\Product;

use bqp\Exceptions\DevException;
use bqp\field_manager;
use bqp\table\field\table_object_field;
use bqp\Utils\StringUtils;
use config;

class ProductListHeaderProfile
{
    /**
     * @var table_object_field[]
     */
    protected array $headers = [];
    protected array $sql_fields = [];
    protected ?string $header_profile_id;

    public function __construct($header_profile_id = null)
    {
        $this->header_profile_id = $header_profile_id;
    }

    /**
     * lädt ein vordefiniertes profile
     */
    public static function load(string $header_profile_id): ProductListHeaderProfile
    {
        $profile = new ProductListHeaderProfile($header_profile_id);

        $set = config::getLegacy('product_list')->sets[$header_profile_id];

        if (!$set) {
            return $profile;
        }

        foreach ($set as $field_key) {
            $profile->addHeaderByFieldKey($field_key);
        }

        return $profile;
    }

    public function addHeaderByFieldKey(string $field_key): void
    {
        $this->addHeader(self::createHeaderFieldByKey($field_key));
    }

    /**
     * @param string $field_key
     * @return table_object_field
     *
     *
     * @todo auf \bqp\field_manager umstellen
     *
     */
    public static function createHeaderFieldByKey(string $field_key): table_object_field
    {
        $field = field_manager::getInstance()->getAsTableField($field_key);

        return $field;
    }

    /**
     * @return table_object_field[]
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function addHeader(table_object_field $header, $pos = null)
    {
        if ($pos !== null) {
            $this->headers[$pos] = $header;
        } else {
            $this->headers[] = $header;
        }

        $this->sql_fields = array_merge($this->sql_fields, $header->getRequierdSqlFields());
    }

    public function addHeaderAfter(string $key_pos, table_object_field $new_header): void
    {
        $this->addHeadersAfter($key_pos, [$new_header]);
    }

    /**
     * @param string $key_pos
     * @param table_object_field[] $new_headers
     * @return void
     */
    public function addHeadersAfter(string $key_pos, array $new_headers): void
    {
        $this->sql_fields = [];
        $headers = $this->headers;
        $this->headers = [];

        foreach ($headers as $key => $header) {
            $this->addHeader($header);

            if ($header->getKey() == $key_pos) {
                foreach ($new_headers as $temp) {
                    $this->addHeader($temp);
                }
            }
        }
    }

    public function getSqlFields(): array
    {
        return $this->sql_fields;
    }

    public function getRowFormater()
    {
        $this->sql_fields[] = 'product_shop.park';
        $this->sql_fields[] = 'product.product_status';

        return [ProductRepositoryLegacy::class, 'tableHelper_status_colorize'];
    }

    public function getHeaderProfileId(): ?string
    {
        return $this->header_profile_id;
    }

    public function hasKeyPos(string $key_pos): bool
    {
        foreach ($this->headers as $header) {
            if ($header->getKey() === $key_pos) {
                return true;
            }
        }
        return false;
    }
}
