<?php

namespace wws\Product;

use bqp\Exceptions\DevException;
use bqp\table\DataSource\TableObjectDataSourceWws;
use bqp\table\field\table_object_field;
use bqp\table\Renderer\TableObjectRendererHtmlInteractiveProductList;
use bqp\table\TableObjectPersistent;
use db;
use env;
use manipulation_query;
use wws\preisvergleich\preisvergleich;
use wws\ProductStock\ProductStockConst;

/**
 * Klasse für die Ausgabe von Produktlisten
 */
class ProductList extends TableObjectPersistent
{
    /**
     * @var ProductListHeaderProfile $std_header_profile
     */
    protected ?ProductListHeaderProfile $std_header_profile = null;

    /**
     * @var ProductListHeaderProfile $header_profile
     */
    protected $header_profile = null;


    protected ?array $product_list_actions = null;

    protected array $extra_product_list_actions = [];


    /**
     * fügt den product_filter_list_results.status mit zum ergebnis hinzu
     *
     * @var int|null
     */
    private ?int $filter_list_id = null;


    private int $shop_id;
    private string $lang_id = ProductConst::PRODUCT_LANG_DEFAULT;

    public function __construct($sql, $filter_id = '')
    {
        $this->setFieldSelectEnabled(false); //in der Form nicht kombinierbar wie HeaderProfile arbeiten. Das lässt sich aber zukünftig komplett darauf migrieren.

        if (is_string($sql)) {
            $sql = new TableObjectDataSourceWws(db::getInstance(), $sql);
        }
        $sql->setHeaderGenerator(null);

        $this->setEntriesPerPageDefault(250);

        $this->shop_id = env::getShopId();

        parent::__construct($sql, $filter_id);

        $this->renderer = new TableObjectRendererHtmlInteractiveProductList($this);
    }

    public function eventBeforeRender(): void
    {
        parent::eventBeforeRender();
    }

    public function setFilter($filter): void
    {
        parent::setFilter($filter);

        if (isset($filter['header_profile_id'])) {
            $this->setHeaderProfile($filter['header_profile_id']);
        }
    }


    public function setProductListActions(array $product_list_actions): void
    {
        $this->product_list_actions = $product_list_actions;
    }

    public function addExtraProductListAction(string $url, string $name): void
    {
        $this->extra_product_list_actions[$url] = $name;
    }

    /**
     * erzeugt die header für die Produktliste, muss ggf. vorm hinzufügen weiterer felder aufgerufen werden
     */
    public function buildHeaders(): void
    {
        if ($this->fields) {
            return;
        }

        $formater = $this->getHeaderProfile()->getRowFormater();

        if ($formater) {
            $this->setRowFormater($formater);
        }

        foreach ($this->getHeaderProfile()->getHeaders() as $field) {
            $this->addField($field);
        }

        $this->resolveDependencies();
    }

    protected function loadDaten(): void
    {
        $this->buildHeaders();

        parent::loadDaten();
    }


    // <editor-fold defaultstate="collapsed" desc="Query entsprechend der gewählten Header manipulieren">
    protected function resolveDependencies()
    {
        /* @var $query manipulation_query */
        $query = $this->data_source->getQuery();

        $tables = $query->getTablesSimple();

        $fields = $query->getSelectFieldsBeforeMakro();

        foreach ($this->getHeaderProfile()->getSqlFields() as $sql_field) {
            //prüfen ob $sql_field in $fields
            if ($this->resolveDependencies_inFields($sql_field, $fields)) {
                continue;
            }

            //prüfen ob table in $tables
            $current_tables = $this->getTablesFromSelect($sql_field);

            foreach ($current_tables as $table) {
                if (!in_array($table, $tables)) {
                    $this->resolveDependencies_addTable($query, $table);
                    $tables[] = $table;
                }
            }

            //feld hinzufügen
            $query->addSelectField($sql_field);
        }

        if ($this->filter_list_id) {
            $query->addTable("LEFT JOIN product_filter_list_results AS product_filter_list_results_status ON (product.product_id = product_filter_list_results_status.product_id AND product_filter_list_results_status.filter_list_id = " . (int)$this->filter_list_id . ")");
            $query->addSelectField('product_filter_list_results_status.status', 'product_filter_list_result_status');
        }
    }

    /**
     * Extrahiert aus ein ausschnitt von SELECT die Tabellennamen.
     * "product.brand_id AS brand_id, product_brand.brand_name" => product, product_brand
     * @param string $sql_field
     * @return array
     */
    private function getTablesFromSelect(string $sql_field): array
    {
        //das sah vorher so aus:
        //$table = substr($field, 0, strpos($field, '.'));
        //->das hat überraschend lange funktioniert...

        if (preg_match_all('/([a-z0-9_]+)\.([a-z0-9_]+)/', $sql_field, $matches)) {
            $tables = $matches[1];
            return array_unique($tables);
        }

        return [];
    }

    protected function resolveDependencies_inFields($field, $fields)
    {
        foreach ($fields as $daten) {
            if ($daten['field'] == $field) {
                return true;
            }
        }

        return false;
    }

    protected function resolveDependencies_addTable($query, $table)
    {
        switch ($table) {
            case 'product_brand':
                $query->addTable("INNER JOIN product_brand ON (product.brand_id = product_brand.brand_id)");
                break;
            case 'product_types':
                $query->addTable("INNER JOIN einst_product_type ON (product.product_type = einst_product_type.product_type)");
                break;
            case 'product_shop':
                $query->addTable("INNER JOIN product_shop ON (product.product_id = product_shop.product_id AND product_shop.shop_id = " . $this->shop_id . ")");
                break;
            case 'product_ek':
                $query->addTable("INNER JOIN product_ek ON (product.product_id = product_ek.product_id AND product_ek.ek_fav = 1)");
                break;
            case 'product_lager_meta':
                $query->addTable("INNER JOIN product_lager_meta ON (product.product_id = product_lager_meta.product_id)");
                break;
            case 'product_cache':
                $query->addTable("INNER JOIN product_cache ON (product.product_id = product_cache.product_id)");
                break;
            case 'product_lager_detail_1':
                $query->addTable("LEFT JOIN product_lager_detail AS product_lager_detail_1 ON (product.product_id = product_lager_detail_1.product_id AND product_lager_detail_1.lager_id = " . ProductStockConst::LAGER_ID_LAGER . ")");
                break;
            case 'supplier':
                $query->addTable("INNER JOIN supplier ON (product_ek.supplier_id = supplier.supplier_id)");
                break;
            case 'product_cat':
                $query->addTable("INNER JOIN product_cat ON (product.cat_id = product_cat.cat_id)");
                break;
            case 'product_prices':
                $query->addTable("INNER JOIN product_prices ON (product_shop.product_id = product_prices.product_id AND product_shop.price_group_id = product_prices.price_group_id)");
                break;
            case 'product_preisvergleich':
                //das ist jetzt auf idealo festgenagelt
                $query->addTable("LEFT JOIN product_preisvergleich ON (product_preisvergleich.product_id = product.product_id AND product_preisvergleich.preissuchmaschine = '" . preisvergleich::PSM_IDEALO . "')");
                break;
            case 'product_details':
                $query->addTable("LEFT JOIN product_details ON (product.product_id = product_details.product_id AND product_details.lang = '" . db::getInstance()->escape($this->lang_id) . "')");
                break;
            case 'global':
                break;
            default:
                throw new DevException('Keine JOIN Bedingung für ' . $table . '!');
        }
    }

    // </editor-fold>

    public function setStdHeaderProfile($header_profile)
    {
        if (!($header_profile instanceof ProductListHeaderProfile)) {
            $header_profile = ProductListHeaderProfile::load($header_profile);
        }

        $this->std_header_profile = $header_profile;
    }

    public function setHeaderProfile($header_profile)
    {
        if (!($header_profile instanceof ProductListHeaderProfile)) {
            $header_profile = ProductListHeaderProfile::load($header_profile);
        }

        $this->header_profile = $header_profile;
    }

    public function getHeaderProfile(): ProductListHeaderProfile
    {
        if ($this->header_profile) {
            return $this->header_profile;
        }

        if (!$this->std_header_profile) {
            $this->std_header_profile = ProductListHeaderProfile::load(ProductConst::PRODUCT_LIST_HEADER_NORMAL);
        }

        return $this->std_header_profile;
    }

    public function tryGetFieldByKey(string $key): ?table_object_field
    {
        $field = parent::tryGetFieldByKey($key);
        if ($field) {
            return $field;
        }

        $matches = [];

        foreach ($this->getHeaderProfile()->getHeaders() as $header) {
            if ($header->getDataKey() === $key) {
                $matches[] = $header;
            }
        }

        if (!$matches) {
            return null;
        }

        if (count($matches) > 1) {
            trigger_error('Field lookup by data_key "' . $key . '" is ambiguous in ProductList.', E_USER_WARNING);
            return null;
        }

        $field = $matches[0];

        trigger_error('Deprecated: Field lookup by data_key "' . $key . '" matched field key "' . $field->getKey() . '" in ProductList. Use the field key instead.', E_USER_DEPRECATED);

        return $field;
    }

    public function setShowHeaderProfilePicker($status): void
    {
        $this->renderer->setShowHeaderProfilePicker($status);
    }

    /**
     * In das Ergebnis wird der Status der $filter_list_id als "product_filter_list_result_status" geladen.
     * Wird u.U. mit MAKRO.product.status_symbols ausgewertet.
     *
     * Es erfolgt keine Filterung anhand der Filterliste!
     *
     * @param int $filter_list_id
     */
    public function setFilterListId(int $filter_list_id): void
    {
        $this->filter_list_id = $filter_list_id;
    }

    public function setLangId(string $lang_id): void
    {
        $this->lang_id = $lang_id;
    }

    public function getProductListActions(): ?array
    {
        //@todo mehrfach aufruf!

        if ($this->product_list_actions === null) {
            $this->product_list_actions = ProductRepositoryLegacy::getProductListActions();
        }

        if ($this->extra_product_list_actions) {
            $this->product_list_actions = array_merge($this->product_list_actions, $this->extra_product_list_actions);
            $this->extra_product_list_actions = [];
        }

        return $this->product_list_actions;
    }
}
