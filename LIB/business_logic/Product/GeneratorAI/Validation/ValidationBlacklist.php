<?php

namespace wws\Product\GeneratorAI\Validation;

class ValidationBlacklist extends Validation
{
    // fallback blacklist, wenn keine parameter angegeben, wird diese verwendet
    public static array $blacklist = [
        'Herstellergarantie',
        '<PERSON><PERSON><PERSON>',
        'Motorgarantie',
        'Produktgarantie',
        'Tüv',
        'FCKW',
        'Inbus',
        'Provato',
        'CE-geprüft',
        'CE-Kennzeichen',
        'CE-Kennzeichnung',
        'CE-Zertifizierung',
        'Prüfzeichen: CE',
        'CE-Prüfzeichen',
        'Display',
        'Ceran',
        'axentia'
    ];

    public function validate(string $result, array $params = []): array
    {
        $issues = [];

        $blacklist = $params ?: self::$blacklist;
        if (preg_match_all('/\b(' . implode('|', $blacklist) . ')\b/i', $result, $matches)) {
            $found_words = $matches[1];
            $issues[] = '* VERBOTENE BEGRIFFE: "' . implode('", "', $found_words) . '" müssen entfernt werden';
        }

        return $issues;
    }
}
