<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomShopProductMetaTitle extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst einen optimierten Meta-Titel für den Onlineshop "smartgoods.de" aus Radebeul bei Dresden erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für SEO Meta Title (smartgoods.de)
- Aufbau: Keyword zuerst (+ Modell-/Herstellernummer) > Produktdetails/USPs > Shopname am Ende ("| smartgoods.de")
- STRIKTE Zeichenbegrenzung: MAXIMAL 60 Zeichen - DIE ANTWORT DARF NICHT LÄNGER SEIN!
- <PERSON><PERSON> W<PERSON>ungen / kein Keyword-Stuffing

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- DIE 60-ZEICHEN-GRENZE IST ABSOLUT UND DARF NICHT ÜBERSCHRITTEN WERDEN!
- Erstelle den effektivsten Meta-Titel innerhalb der Zeichenbegrenzung

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN UND EXAKT 60 ZEICHEN ODER WENIGER!

Gib nur den optimierten Meta-Titel zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Meta-Titel optimiert. Ein bereits erstellter Meta-Titel hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [50, 60, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
    }
}
