<?php

namespace wws\Product\GeneratorAI\Prompts;

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptK11CatMetaTitle extends Prompt
{
    public function __construct()
    {
        parent::__construct('Du bist Seo Spezialist. Erstelle mir Meta Title für folgendes Thema {keyword}. Beachte dabei folgenden Aufbau und Regeln: 

Meta Title ist auf 60 Zeichen begrenzt. Nutze am besten die volle Zeichenbegrenzung aus. 
Der Aufbau des Meta Titles ist wie folgt: {keyword} kaufen – {USP}
Wähle die USPs aus einer Liste aus. Entscheide selbst was am besten passt. 

{USP}: alle Marken, schneller Versand, günstige Preise, riesige Auswahl, schnelle Lieferung, exklusive Produkte, große Produktauswahl, schnelle Reparatur, geprüfte Teile

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
        ');

        $this->setModel(OpenRouterApi::MODEL_GROK_4);
        $this->setTemperature(.5);

        $this->addValidation(ValidationBlacklist::class);
        $this->addValidation(ValidationLength::class, [0, 60, 'chars']);
    }
}
