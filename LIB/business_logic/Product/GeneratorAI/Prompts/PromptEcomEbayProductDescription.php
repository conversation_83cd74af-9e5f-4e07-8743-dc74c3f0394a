<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomEbayProductDescription extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst eine optimierte Produktbeschreibung für eBay erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für optimierte Produktbeschreibung (eBay HTML)
- HTML MUSS mit <h1>PRODUKTNAME</h1> beginnen
- Deine Antwort drarf nicht mit ```html beginnen und mit ``` enden. Reines pures HTML, was sofort in die Datenbank geschrieben werden kann.
- Struktur: Kurze Einleitung > Bullet-Points > Detaillierte Beschreibung (2–4 Absätze) > Technische Daten (Tabelle/Liste) > Lieferumfang > Zustand & Hinweise
- SEO-Elemente: Keywords smart einbinden, Überschriften zur Gliederung, lesefreundliche Formatierung, wichtige Begriffe sparsam fett hervorheben

# WICHTIG - VERBOTENE BEGRIFFE UND INHALTE
- Deine Antwort drarf nicht mit ```html beginnen und mit ``` enden. Reines pures HTML, was sofort in die Datenbank geschrieben werden kann.
- VERBOTENE BEGRIFFE: Die folgenden Begriffe dürfen NIEMALS in JEGLICHER FORM verwendet werden:
* Garantie oder Wörter, die "garantie" enthalten ("garantieren", "Garantieleistung" usw.)
* Tüv
* Ceran
* FCKW
* Inbus
* Provato
* Jegliche CE-Kennzeichnungen (CE-geprüft, CE-Kennzeichen usw.)
* Display
- VERBOTENE INHALTE:
* KEINE Shop-Namen, Firmennamen oder Website-URLs erwähnen
* KEINE Aufforderungen zum Bestellen, Kaufen oder Kontaktieren ("bestellen", "kaufen", "jetzt bestellen", "bei uns bestellen", etc.)
* KEINE Call-to-Action Elemente
* KEINE Kontaktinformationen oder Hinweise auf externe Shops
- Überprüfe den gesamten Text, um sicherzustellen, dass KEINER dieser Begriffe oder Inhalte enthalten ist!
- Formatiere die Beschreibung als HTML mit einfacher Formatierung
- Erstelle die effektivste Beschreibung für eBay

# WICHTIG: 
- DIE ANTWORT MUSS AUF DEUTSCH SEIN!
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '

Gib nur die HTML-formatierte eBay-Beschreibung zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Produktbeschreibungen für eBay optimiert. Eine bereits erstellte eBay Produktbeschreibung hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [0, 5000, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
    }
}
