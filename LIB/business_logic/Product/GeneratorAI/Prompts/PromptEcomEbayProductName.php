<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomEbayProductName extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst einen optimierten Produkttitel für eBay erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für optimierten Produkttitel (eBay)
- Struktur: Marke + Produkttyp + Haupt-Keyword + Eigenschaften (Farbe, Größe, Material, Modell)
- STRIKTE Zeichenbegrenzung: MAXIMAL 75 Zeichen - DIE ANTWORT DARF NICHT LÄNGER SEIN!
- eBay-SEO Grundregeln: Haupt-Keyword ganz vorn, keine <PERSON>, Synonyme/Varianten einbauen, wichtige Merkmale nennen, korrekte Rechtschreibung

# Wichtig - VERBOTENE BEGRIFFE
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- Stelle sicher, dass die korrekte Marke verwendet wird. Smartgoods darf nicht als Marke verwendet werden, wenn es sich um ein Eigenprodukt handelt. Andernfalls immer die Hersteller-Marke verwenden.
- KEINE Shop-Namen oder Firmennamen erwähnen
- KEINE Aufforderungen zum Bestellen oder Kaufen
- DIE 75-ZEICHEN-GRENZE IST ABSOLUT UND DARF NICHT ÜBERSCHRITTEN WERDEN!
- Erstelle den effektivsten Titel für eBay innerhalb der Zeichenbegrenzung

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN UND EXAKT 75 ZEICHEN ODER WENIGER!

Gib nur den optimierten eBay-Titel zurück, ohne zusätzliche Erklärungen oder Text.');
        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Produkttitel für eBay optimiert. Ein bereits erstellter eBay Produkttitel hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [0, 75, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
    }

}
