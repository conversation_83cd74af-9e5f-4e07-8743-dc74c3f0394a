<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationContains;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomShopProductName extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst einen optimierten Produkttitel für den Onlineshop "smartgoods.de" aus Dresden erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für SEO-optimierten Produktnamen (Website)
- Aufbau (Reihenfolge): Marke > Produktart > Modellbezeichnung / Hersteller-Art.-Nr. > Haupt-Keyword > wichtigste Eigenschaften (z. B. Farbe, Größe, Material, Modelljahr)
- Nach der Hersteller-Art.-Nr. alle Haupt-Keywords & Eigenschaften immer mit " | " trennen
- Wenn relevant, mit der Farbe enden
- Regeln: Wichtigstes Keyword so früh wie möglich, klare Struktur, relevante Details, optimale Zeichenlänge (max. 120 Zeichen; die wichtigsten Infos in die ersten 70), Lesbarkeit für Menschen & Suchmaschinen
- Keine Wiederholungen, kein Keyword-Stuffing
- Titel muss informativ, schlüssig, keyword-relevant und gut lesbar sein

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- Stelle sicher, dass die korrekte Marke verwendet wird. Smartgoods darf nicht als Marke verwendet werden, wenn es sich um ein Eigenprodukt handelt. Andernfalls immer die Hersteller-Marke verwenden.
- Zeichenlimits strikt einhalten
- Stelle sicher, dass alle Inhalte einzigartig sind und nicht vom Hersteller kopiert werden
- Erstelle den vollständigsten und besten Titel am Markt
- Recherche ist ausdrücklich erwünscht

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN!

Gib nur den optimierten Produktnamen zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->addValidation(ValidationLength::class, [0, 120, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
        $this->addValidation(ValidationContains::class, [' | ']);

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Produkttitel optimiert. Ein bereits erstellter Produkttitel hat Probleme, die du beheben musst.');
        $this->setCorrectionRules('Stelle sicher, dass du die Trennzeichen " | " zwischen Haupt-Keywords & Eigenschaften nicht entfernst');
    }
}
