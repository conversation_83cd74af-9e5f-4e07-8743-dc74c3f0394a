<?php

namespace wws\Product\GeneratorAI\Prompts;

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptK11ShopProductMetaDescription extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist SEO-Spezialist. Erstelle eine Meta Description für folgendes Produkt.

# Produktinformationen
{PRODUCT_INFO}

# Aufbau und Regeln
- Meta Description ist auf 155 Zeichen begrenzt - nutze die volle Zeichenbegrenzung aus
- Aufbau: Produktname + Produktnummer(n) + Mark<PERSON>(n) + "günstig online kaufen" ✓ {USP} ➜ {Call-to-Action}
- Wähle USP und Call-to-Action passend zum Produkt aus den Listen unten

# USPs (wähle 1-2 passende):
alle Marken, schneller Versand, günstige Preise, riesige Auswahl, schnelle Lieferung, exklusive Produkte, große Produktauswahl, schnelle Reparatur, geprüfte Teile, Top Qualität

# Call-to-Action (wähle einen passenden):
Jetzt kaufen!, Jetzt online bestellen!, jetzt bestellen!, jetzt zuschlagen!, jetzt entdecken!, jetzt direkt bestellen!, jetzt sofort bestellen!

# Verbotsliste - NICHT verwenden:
Herstellergarantie, Garantie, Motorgarantie, Produktgarantie, Tüv, FCKW, Inbus, Provato, CE-geprüft, CE-Kennzeichen, CE-Kennzeichnung, CE-Zertifizierung, Prüfzeichen: CE, CE-Prüfzeichen, Display, Ceran

# WICHTIG
- DIE 155-ZEICHEN-GRENZE DARF NICHT ÜBERSCHRITTEN WERDEN!
- DIE ANTWORT MUSS AUF DEUTSCH SEIN!
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '

Gib nur die Meta Description zurück, ohne zusätzliche Erklärungen.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Meta-Beschreibungen optimiert. Eine bereits erstellte Meta-Beschreibung hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [140, 155, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
        $this->setModel(OpenRouterApi::MODEL_GROK_4);
        $this->setTemperature(.5);
    }
}
