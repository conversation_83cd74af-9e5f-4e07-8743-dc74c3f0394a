<?php

namespace wws\Product\GeneratorAI\Prompts;

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;

class PromptK11ShopProductName extends Prompt
{
    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst einen optimierten Produkttitel für den Onlineshop "ersatzteilshop.de" erstellen.

# Produktinformationen
{PRODUCT_INFO}

# Ausgabe-Anforderungen für SEO-optimierten Produktnamen (Website)
- Aufbau (Reihenfolge): Produktart > Marke > Modellbezeichnung / Hersteller-Art.-Nr. > in welcher Gerätegruppe verbaut
- Erwähne im Produktnamen nicht ob Alternativteil oder Originalteil
- Regeln: klare Struktur, optimale Zeichenlänge (max. 120 Zeichen; die wichtigsten Infos in die ersten 70), Lesbarkeit für Menschen & Suchmaschinen
- <PERSON><PERSON>, kein Keyword-Stuffing
- Titel muss informativ, schlüssig, keyword-relevant und gut lesbar sein

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- Zeichenlimits strikt einhalten
- Stelle sicher, dass alle Inhalte einzigartig sind und nicht vom Hersteller kopiert werden
- Erstelle den vollständigsten und besten Titel am Markt
- Recherche ist ausdrücklich erwünscht

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN!

Beispiele:
„Milchschlauch für Aufschäumdüse SIEMENS 12004554 für Kaffeemaschine"
„Abdeckung für Filter wie LG ADQ75493305 für Kühlschrank"
„Anlaufvorrichtung wie BOSCH 00617926 für Kühlschrank"
„Türdichtung Whirlpool 480132100607 für Kühlschrank Gefrierschrank"
„Ofentür wie AEG 140071069276 für Backofen"

Gib nur den optimierten Produktnamen zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->addValidation(ValidationBlacklist::class);
        $this->setModel(OpenRouterApi::MODEL_GROK_4);
        $this->setTemperature(.5);
    }
}
