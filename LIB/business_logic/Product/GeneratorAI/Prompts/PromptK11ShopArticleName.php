<?php

namespace wws\Product\GeneratorAI\Prompts;

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;

class PromptK11ShopArticleName extends Prompt
{
    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst einen optimierten Produkttitel für den Onlineshop "ersatzteilshop.de" erstellen.

# Produktinformationen
{PRODUCT_INFO}

# Ausgabe-Anforderungen für SEO-optimierten Produktnamen (Website)
- Aufbau (Reihenfolge): Produktname > Marke > Ersatzteilnummer / Hersteller-Art.-Nr. > "für" Gerät
- wenn Alternativteil, dann muss „wie" vor der Marke geschrieben werden
- Regeln: klare Struktur, optimale Zeichenlänge (max. 80 Zeichen; die wichtigsten Infos in die ersten 70), Lesbarkeit für Menschen & Suchmaschinen
- <PERSON><PERSON>, kein Keyword-Stuffing
- Titel muss informativ, schlüssig, keyword-relevant und gut lesbar sein
# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- Zeichenlimits strikt einhalten
- Stelle sicher, dass alle Inhalte einzigartig sind und nicht vom Hersteller kopiert werden
- Erstelle den vollständigsten und besten Titel am Markt
- Recherche ist ausdrücklich erwünscht
# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN!
Beispiele:
„Gasgriff SoFlow 800.210.04 für E-Scooter"
„Gashebel Xiaomi C002550002700 für E-Scooter"
„Vorderer Lenker Platum SCCTY-SCCB-024 für E-Scooter"

Gib nur den optimierten Produktnamen zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->addValidation(ValidationBlacklist::class);
        $this->setModel(OpenRouterApi::MODEL_GROK_4);
        $this->setTemperature(.5);
    }
}
