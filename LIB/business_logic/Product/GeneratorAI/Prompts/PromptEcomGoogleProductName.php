<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomGoogleProductName extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst einen optimierten Produkttitel für Google Shopping für den Onlineshop "smartgoods.de" aus Radebeul bei Dresden erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für optimierten Produkttitel (Google Shopping Feed)
- Grundlagen: Wichtigste Keywords am Anfang (Marke, Produkttyp, relevante Details)
- Struktur: Marke – Produktart – Hauptmerkmale – Varianten (Farbe, Größe, Modellnummer)
- <PERSON><PERSON><PERSON>, pr<PERSON><PERSON><PERSON>, keine Marketingphrasen
- Wichtige Spezifikationen integrieren (Material, Größe, Farbe, Geschlecht, Modelljahr ...)
- Zeichenlänge: max. 150 Zeichen (Google zeigt 70–80 prominent)
- Keine irreführenden Begriffe, Rechtschreibung prüfen

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- Stelle sicher, dass die korrekte Marke verwendet wird. Smartgoods darf nicht als Marke verwendet werden, wenn es sich um ein Eigenprodukt handelt. Andernfalls immer die Hersteller-Marke verwenden.
- Zeichenlimits strikt einhalten
- Erstelle den effektivsten und optimiertesten Titel für Google Shopping

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN!

Gib nur den optimierten Google Shopping-Titel zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Produkttitel für Google Shopping optimiert. Ein bereits erstellter Google Shopping Titel hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [0, 150, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
    }
}
