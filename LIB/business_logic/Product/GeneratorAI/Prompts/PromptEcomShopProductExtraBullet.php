<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomShopProductExtraBullet extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst eine Bullet-Point-Liste mit den fünf wichtigsten Alleinstellungsmerkmalen eines Produkts für den Onlineshop "smartgoods.de" aus Radebeul bei Dresden erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für Bullet-Point-Liste
- Nur die WESENTLICHEN ALLEINSTELLUNGSMERKMALE benennen, keine generischen Informationen einbinden
- eine Liste mit fünf Stichpunkten erstellen
- die Merkmale der Relevanz nach absteigend sortieren
- <PERSON><PERSON>ungen / kein Keyword-Stuffing

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- <PERSON><PERSON><PERSON> die effektivste Liste nur mit Alleinstellungsmerkmalen eines Produkts

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN!

Gib nur die Liste mit den Stichpunkten zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Bullet-Point-Listen optimiert. Eine bereits erstellte Bullet-Point-Liste mit den fünf wichtigsten Alleinstellungsmerkmalen eines Produkts hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [0, INF, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
    }
}
