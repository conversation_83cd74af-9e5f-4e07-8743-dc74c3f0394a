<?php

namespace wws\Product\GeneratorAI\Prompts;

use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptEcomShopProductMetaDescription extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist ein SEO-Experte und sollst eine optimierte Meta-Beschreibung für den Onlineshop "smartgoods.de" aus Radebeul bei Dresden erstellen.

# Produktinformationen
{product_info}

# Ausgabe-Anforderungen für SEO Meta Description (smartgoods.de)
- Aufbau: Produktname + Artikelnummer > Relevante Merkmale (max. 1–2 kurze) > Call-to-Action > Shop-Vorteile
- STRIKTE Zeichenbegrenzung: MAXIMAL 155 Zeichen - DIE ANTWORT DARF NICHT LÄNGER SEIN!

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
- DIE 155-ZEICHEN-GRENZE IST ABSOLUT UND DARF NICHT ÜBERSCHRITTEN WERDEN!
- Erstelle die effektivste Meta-Beschreibung innerhalb der Zeichenbegrenzung

# WICHTIG: DIE ANTWORT MUSS AUF DEUTSCH SEIN UND EXAKT 155 ZEICHEN ODER WENIGER!

Gib nur die optimierte Meta-Beschreibung zurück, ohne zusätzliche Erklärungen oder Text.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Meta-Beschreibungen optimiert. Eine bereits erstellte Meta-Beschreibung hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [140, 155, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
    }
}
