<?php

namespace wws\Product\GeneratorAI\Prompts;

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\Traits\DropsTemperature;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptK11ShopProductMetaTitle extends Prompt
{
    use DropsTemperature;

    public function __construct()
    {
        parent::__construct('Du bist SEO-Spezialist. Erstelle einen Meta Title für folgendes Produkt.

# Produktinformationen
{PRODUCT_INFO}

# Aufbau und Regeln
- Meta Title ist auf 60 Zeichen begrenzt - nutze die volle Zeichenbegrenzung aus
- Aufbau: Produktname + Produktnummer(n) + Mark<PERSON>(n) + "kaufen"
- Nutze "kaufen" nur wenn es die Zeichenlänge zulässt
- Keine <PERSON> / kein Keyword-Stuffing

# Verbotsliste - NICHT verwenden:
Herstellergarantie, <PERSON><PERSON><PERSON>, Motorgarantie, Produktgarantie, Tüv, FCKW, Inbus, Provato, CE-geprüft, CE-Kennzeichen, CE-Kennzeichnung, CE-Zertifizierung, Prüfzeichen: CE, CE-Prüfzeichen, Display, Ceran

# WICHTIG
- DIE 60-ZEICHEN-GRENZE DARF NICHT ÜBERSCHRITTEN WERDEN!
- DIE ANTWORT MUSS AUF DEUTSCH SEIN!
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '

Gib nur den Meta Title zurück, ohne zusätzliche Erklärungen.');

        $this->setCorrectionPrompt('Du bist ein SEO-Experte, der Meta-Titel optimiert. Ein bereits erstellter Meta-Titel hat Probleme, die du beheben musst.');

        $this->addValidation(ValidationLength::class, [50, 60, 'chars']);
        $this->addValidation(ValidationBlacklist::class);
        $this->setModel(OpenRouterApi::MODEL_GROK_4);
        $this->setTemperature(.5);
    }
}
