<?php

namespace wws\Product\GeneratorAI\Prompts;

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\Validation\ValidationBlacklist;
use wws\Product\GeneratorAI\Validation\ValidationLength;

class PromptK11CatMetaDescription extends Prompt
{
    public function __construct()
    {
        parent::__construct('Du bist Seo Spezialist. Erstelle mir Meta Description für folgendes Thema {keyword}. Beachte dabei folgenden Aufbau und Regeln: 
 
Die Meta Description ist auf 155 Zeichen beschränkt. Nutze am besten die volle Zeichenbegrenzung aus. 
Der Aufbau der Meta Description ist wie folgt: Entdecken Sie hochwertige {keyword} für alle Marken & Modelle. ✓ {USPs}. ➜ {Call-to-Action}
Wähle die USPs und Call-to-Action aus einer Liste aus. Entscheide selbst was am besten passt. 

{USP}: alle Marken, schneller Versand, g<PERSON><PERSON><PERSON> Preise, riesige Auswahl, schnelle Lieferung, exklusive Produkte, große Produktauswahl, schnelle Reparatur, geprüfte Teile
{Call-to-Action}: Jetzt kaufen!, Jetzt online bestellen!, jetzt bestellen!, jetzt zuschlagen!, jetzt entdecken!, jetzt direkt bestellen!, jetzt sofort bestellen!

# Wichtig
- Folgende Begriffe NICHT verwenden: ' . join(', ', ValidationBlacklist::$blacklist) . '
        ');

        $this->setModel(OpenRouterApi::MODEL_GROK_4);
        $this->setTemperature(.5);

        $this->addValidation(ValidationBlacklist::class);
        $this->addValidation(ValidationLength::class, [0, 155, 'chars']);
    }
}
