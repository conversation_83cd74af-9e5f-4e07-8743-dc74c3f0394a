<?php

use bqp\Exceptions\FatalException;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Utils\ArrayUtils;
use wws\Customer\CustomerRepository;
use wws\Nummernkreis\NummernkreisShop;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderDocument;
use wws\Order\OrderRepository;

class order_repository
{
    public static function getOverdueSqlFilter(): string
    {
        return "
            orders.order_aktiv = 1 AND
            order_item.typ = '" . OrderConst::WARENKORB_TYP_PRODUKT . "' AND
            order_item.versand_status = 0 AND
            order_item.status NOT IN (" . OrderConst::STATUS_AUFTRAGSANNAHME . ", " . OrderConst::STATUS_EBAY_ZAHLUNGSABWICKLUNG . ", " . OrderConst::STATUS_WARTE_ZAHLUNG . ", " . OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST . ", " . OrderConst::STATUS_ZUSTELLUNG_BEENDET . ", " . OrderConst::STATUS_PARK . ", " . OrderConst::STATUS_STORNO . ") AND
            order_item.status < " . OrderConst::STATUS_STORNO . " AND
            order_item.quantity_open > 0 AND
            orders.order_type != '" . OrderConst::ORDER_TYPE_OFFER . "' AND
            (
                (
                    orders.zahlungs_id IN (" . OrderConst::PAYMENT_VORKASSE . ", " . OrderConst::PAYMENT_FINANZIERUNG . ", " . OrderConst::PAYMENT_FINANZIERUNG_COMMERZ . ", " . OrderConst::PAYMENT_BARZAHLEN . ") AND
                    orders.zahlungs_status = " . OrderConst::PAYMENT_STATUS_LEGACY_1 . "
                ) OR (
                    orders.zahlungs_id NOT IN (" . OrderConst::PAYMENT_VORKASSE . ", " . OrderConst::PAYMENT_FINANZIERUNG . ", " . OrderConst::PAYMENT_FINANZIERUNG_COMMERZ . ", " . OrderConst::PAYMENT_BARZAHLEN . ")
                )
            )
        ";
    }

    public static function getTaxStatusNames(): array
    {
        return [
            OrderConst::TAX_STATUS_NORMAL => 'normale Besteuerung',
            OrderConst::TAX_STATUS_IG_LIEFERUNG => 'Steuerfreie IG-Lieferung',
            OrderConst::TAX_STATUS_IG_AUSFUHRLIEFERUNG => 'Steuerfreie Ausfuhrlieferung',
            OrderConst::TAX_STATUS_SOLAR_STEUERFREI => 'Steuerfreie Lieferung nach UStG §12 Abs. 3 / Photovoltaik',
            OrderConst::TAX_STATUS_SCHADENSERSATZ => 'Schadensersatz',
        ];
    }

    public static function getTaxStatusInternalName(int $tax_status): string
    {
        $names = self::getTaxStatusNames();

        if (!array_key_exists($tax_status, $names)) {
            throw new FatalException('unknown tax status: ' . $tax_status);
        }

        return $names[$tax_status];
    }

    public static function getOrderTypes(): array
    {
        return [
            OrderConst::ORDER_TYPE_NORMAL => 'Bestellung',
            OrderConst::ORDER_TYPE_SERVICE => 'Service',
            OrderConst::ORDER_TYPE_EK_BESTELLUNG => 'EK-Bestellung',
            OrderConst::ORDER_TYPE_OFFER => 'Angebot',
            OrderConst::ORDER_TYPE_RETURN => 'Rückgabe/Warenbewegung'
        ];
    }

    /**
     * Gibt die Bestellarten zurück, die für die normalen Statistiken relevant sind.
     * @return array
     */
    public static function getOrderTypesForStats(): array
    {
        return [OrderConst::ORDER_TYPE_NORMAL, OrderConst::ORDER_TYPE_SERVICE];
    }

    /**
     * Gibt die Bestellarten zurück, die für die Bestandskalkulation relevant sind.
     * @return array
     */
    public static function getOrderTypesForInventory(): array
    {
        return [OrderConst::ORDER_TYPE_NORMAL, OrderConst::ORDER_TYPE_SERVICE, OrderConst::ORDER_TYPE_RETURN];
    }

    public static function getOrderTypeName(string $order_type): string
    {
        $types = self::getOrderTypes();

        return $types[$order_type];
    }

    public static function getOrderOriginNames(): array
    {
        static $origins = null;

        if ($origins === null) {
            $origins = db::getInstance()->query("
                SELECT
                    einst_order_origin.order_origin_id,
                    einst_order_origin.order_origin_name
                FROM
                    einst_order_origin
                ORDER BY
                    einst_order_origin.pos
            ")->asSingleArray('order_origin_id');
        }

        return $origins;
    }

    public static function getOrderOriginNamesForStats(): array
    {
        static $origins = null;

        if ($origins === null) {
            $origins = db::getInstance()->query("
                SELECT
                    einst_order_origin.order_origin_id,
                    einst_order_origin.order_origin_name
                FROM
                    einst_order_origin
                WHERE   
                    einst_order_origin.show_in_stats = 1
                ORDER BY                
                    einst_order_origin.pos
            ")->asSingleArray('order_origin_id');
        }

        return $origins;
    }

    public static function getOrderOriginName(string $order_origin_id): string
    {
        return self::getOrderOriginNames()[$order_origin_id];
    }

    public static function tableHelperOrderOrigin(array $row, string $field): string
    {
        $origins = self::getOrderOriginNames();

        return $row[$field] ? $origins[$row[$field]] : '';
    }

    public static function getAuftnrRegexp(): string
    {
        return '((?:(?:W|WE)[0-9]{6,7}-[0-9])|(?:(?:W|WE)[0-9]{6,7}))';
    }

    public static function getAuftnrRegexpTolerant(): string
    {
        return '((?:(?:W|WE)[0-9]{6,7}-[0-9])|(?:(?:W|WE)[0-9]{6,7}|(?:(?:W|WE) [0-9]{6,7})|(?:(?:W|WE)\.[0-9]{6,7})))';
    }

    public static function getCustomerNrRegexp(): string
    {
        if (env::isK11()) {
            return '(?P<customer_nr>(?P<customer_id>[0-9]{6,7})(?P<customer_suffix>-KE))';
        }

        return '(?P<customer_nr>(?P<customer_id>[0-9]{6,7})(?P<customer_suffix>-K))';
    }

    public static function getCustomerNrRegexpTolerant(): string
    {
        if (env::isK11()) {
            return '(?:^|\s|K|KE|KD|KDNR|KD NRKD.|KD.NR)(|\s|-)(?P<customer_id>[0-9]{5,7})(?:$|K|KE|-K|-KE| KE| K| -K| -KE|- K|- KE|\s)';
        }

        return '(?:^|\s|K|KD|KDNR|KD NRKD.|KD.NR)(|\s|-)(?P<customer_id>[123456][0-9]{5,6})(?:$|K|-K| K| -K|- K|\s)';
    }

    public static function looksLikeAuftnr(string $value): bool
    {
        return preg_match('~^' . self::getAuftnrRegexp() . '$~', $value);
    }

    public static function searchAuftnrCustomerIdInText(string $text): array
    {
        $return = [
            'customer_id' => '',
            'customer_nr' => '',
            'auftnr' => ''
        ];

        if (preg_match('~' . self::getAuftnrRegexp() . '~', $text, $temp)) {
            $return['auftnr'] = $temp[1];
        }

        if (preg_match('~' . self::getCustomerNrRegexp() . '~', $text, $temp)) {
            $return['customer_id'] = $temp['customer_id'];
            $return['customer_nr'] = $temp['customer_nr'];
        }

        return $return;
    }


    /**
     * Prüft ob eine $customer_id in das Nummernschema passt, nicht ob die $customer_id wirklich existiert
     *
     * @param int $customer_id
     * @return bool
     */
    public static function isPossibleValidCustomerId(int $customer_id): bool
    {
        if (env::isK11()) {
            return ($customer_id > 100000 && $customer_id < 700000) || ($customer_id > 1000000 && $customer_id < 5000000);
        }

        return ($customer_id > 100000 && $customer_id < 700000) || ($customer_id > 1000000 && $customer_id < 2000000);
    }

    /**
     * @param string $text
     * @return array{customer_id: string, customer_nr: string, auftnr: string}
     * @throws FatalException
     */
    public static function searchAuftnrCustomerIdInTextTolerant(string $text): array
    {
        $return = [
            'customer_id' => '',
            'customer_nr' => '',
            'auftnr' => ''
        ];

        if (preg_match('~' . self::getAuftnrRegexpTolerant() . '~i', $text, $temp)) {
            $return['auftnr'] = strtoupper(str_replace('.', '', str_replace(' ', '', $temp[1])));
        }

        if (preg_match('~' . self::getCustomerNrRegexp() . '~i', $text, $temp)) {
            $return['customer_id'] = $temp['customer_id'];
            $return['customer_nr'] = strtoupper($temp['customer_nr']);
        } elseif (preg_match('~' . self::getCustomerNrRegexpTolerant() . '~iu', $text, $temp)) {
            $customer_id = $temp['customer_id'];

            try {
                $customer_nr = CustomerRepository::getCustomerNr($customer_id);
            } catch (SmartDataEntityNotFoundException $e) {
                $customer_nr = null;
            }

            if ($customer_nr) {
                $return['customer_id'] = $customer_id;
                $return['customer_nr'] = $customer_nr;
            }
        }

        return $return;
    }

    public static function searchAuftnrCustomerIdInTextTolerantAutoFill(string $text): array
    {
        $daten = self::searchAuftnrCustomerIdInTextTolerant($text);

        if ($daten['auftnr'] && !$daten['customer_id']) {
            $daten['customer_id'] = self::getCustomerIdByAuftnr($daten['auftnr']);
        }

        if ($daten['customer_id'] && !$daten['customer_nr']) {
            try {
                $daten['customer_nr'] = CustomerRepository::getCustomerNr($daten['customer_id']);
            } catch (SmartDataEntityNotFoundException $e) {
                $daten['customer_nr'] = null;
                $daten['customer_id'] = null;
            }
        }

        return $daten;
    }

    public static function linkAuftnrCustomerIdInText(string $text): string
    {
        $result = self::searchAuftnrCustomerIdInText($text);

        if ($result['customer_nr']) {
            $text = str_ireplace($result['customer_nr'], '<a href="/ax/customer/default/?customer_id=' . $result['customer_id'] . '" target="_blank">' . $result['customer_nr'] . '</a>', $text);
        }

        if ($result['auftnr']) {
            $text = str_ireplace($result['auftnr'], '<a href="/ax/customer/default/?auftnr=' . $result['auftnr'] . '" target="_blank">' . $result['auftnr'] . '</a>', $text);
        }

        return $text;
    }


    /**
     * erzeugt eine neue Auftragsnummer
     * @param int $shop_id
     * @return string Auftragsnummer
     */
    public static function createAuftnr(int $shop_id): string
    {
        $nk = new NummernkreisShop('auftnr', $shop_id);
        return $nk->getNumber();
    }


    public static function tableHelper_status($daten, $field)
    {
        return self::getStatusAsHtml($daten[$field]);
    }

    public static function getStatusNames(): array
    {
        static $statuse = null;

        if ($statuse === null) {
            $statuse = db::getInstance()->query("
                SELECT
                    einst_status.status_id,
                    einst_status.status_name
                FROM
                    einst_status
            ")->asSingleArray('status_id');
        }

        return $statuse;
    }

    public static function getStatuseFull(): array
    {
        static $statuse = null;

        if ($statuse === null) {
            $statuse = db::getInstance()->query("
                SELECT
                    einst_status.status_id,
                    einst_status.status_name,
                    einst_status.status_color,
                    einst_status.shippable
                FROM
                    einst_status
            ")->asArray('status_id');
        }

        return $statuse;
    }

    public static function getStatusAsHtml(int $status_id): string
    {
        $statuse = self::getStatuseFull();

        $status = $statuse[$status_id];
        return '<b style="color: ' . $status['status_color'] . '">' . $status['status_name'] . '</b>';
    }

    /**
     * gibt alle versendbaren status_ids zurück
     * @return array status_ids
     */
    public static function getShippableStatuse(): array
    {
        $statuse = self::getStatuseFull();

        $return = [];

        foreach ($statuse as $daten) {
            if ($daten['shippable'] == 0) {
                continue;
            }

            $return[] = $daten['status_id'];
        }

        return $return;
    }

    public static function getOrderIdByOrderItemId($order_item_id)
    {
        return db::getInstance()->fieldQuery("
            SELECT
                order_item.order_id
            FROM
                order_item
            WHERE
                order_item.order_item_id = '" . (int)$order_item_id . "'
        ");
    }

    /**
     *
     * @param int $order_item_id
     * @return Order
     */
    public static function getOrderByOrderItemId($order_item_id)
    {
        return service_loader::get(OrderRepository::class)->loadCached(self::getOrderIdByOrderItemId($order_item_id));
    }

    public static function getOrderIdByAuftnr($auftnr): ?int
    {
        $db = db::getInstance();

        $order_id = $db->fieldQuery("
            SELECT
                order_item.order_id
            FROM
                order_item
            WHERE
                order_item.auftnr = '" . $db->escape($auftnr) . "'
        ");

        if ($order_id) {
            return (int)$order_id;
        }

        return null;
    }

    public static function getAuftnrByOrderItemId($order_item_id)
    {
        return db::getInstance()->fieldQuery("
            SELECT
                order_item.auftnr
            FROM
                order_item
            WHERE
                order_item.order_item_id = '" . (int)$order_item_id . "'
        ");
    }


    public static function getOrderIdByShopReferenz($shop_referenz, $order_origin_id = null)
    {
        $db = db::getInstance();

        $sql = "SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.shop_referenz = '" . $db->escape($shop_referenz) . "'";

        if ($order_origin_id) {
            $sql .= " AND orders.order_origin_id = '" . $db->escape($order_origin_id) . "'";
        }

        return $db->fieldQuery($sql);
    }

    public static function getOrderByShopReferenz($shop_referenz, $order_origin_id = null): ?Order
    {
        $order_id = self::getOrderIdByShopReferenz($shop_referenz, $order_origin_id);

        if ($order_id) {
            return new Order($order_id);
        }

        return null;
    }

    public static function getOrderIdByPaymentReferenz(string $payment_referenz, int $zahlungs_id): ?int
    {
        $db = db::getInstance();

        $order_id = $db->fieldQuery("
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.payment_referenz = '" . $db->escape($payment_referenz) . "' AND
                orders.zahlungs_id = $zahlungs_id
        ");

        if ($order_id) {
            return (int)$order_id;
        }

        return null;
    }

    public static function getOrderIdByPaymentReferenz2(string $payment_referenz_2, int $zahlungs_id): ?int
    {
        $db = db::getInstance();

        $order_id = $db->fieldQuery("
            SELECT
                orders.order_id
            FROM
                orders
            WHERE
                orders.payment_referenz_2 = '" . $db->escape($payment_referenz_2) . "' AND
                orders.zahlungs_id = $zahlungs_id
        ");

        if ($order_id) {
            return (int)$order_id;
        }

        return null;
    }

    public static function getCustomerIdByAuftnr(string $auftnr): ?int
    {
        $db = db::getInstance();

        $customer_id = $db->fieldQuery("
            SELECT
                orders.customer_id
            FROM
                orders
            WHERE
                orders.auftnr LIKE '" . $db->escape($auftnr) . "'
        ");

        return $customer_id;
    }

    public static function getCustomerIdByOrderId(int $order_id): ?int
    {
        $customer_id = db::getInstance()->fieldQuery("
            SELECT
                orders.customer_id
            FROM
                orders
            WHERE
                orders.order_id = '" . $order_id . "'
        ");

        return $customer_id;
    }


    /**
     *
     * @param string $auftnr
     * @return Order
     */
    public static function getOrderByAuftnr(string $auftnr): Order
    {
        $order_id = self::getOrderIdByAuftnr($auftnr);

        if (!$order_id) {
            throw new SmartDataEntityNotFoundException('Auftrag nicht gefunden! Laden über Auftragsnummer. (' . $auftnr . ')');
        }

        return new Order($order_id);
    }

    public static function getAuftnrByOrderId(int $order_id): string
    {
        return db::getInstance()->fieldQuery("
            SELECT
                order_item.auftnr
            FROM
                order_item
            WHERE
                order_item.order_id = '" . (int)$order_id . "'
        ");
    }


    public static function getBaseAuftnr(string $auftnr): string
    {
        $pos = strpos($auftnr, '-');
        if ($pos) {
            return substr($auftnr, 0, $pos);
        }

        return $auftnr;
    }


    public static function getOrderIdByWarenausgangLieferscheinId(int $lieferschein_id): ?int
    {
        return db::getInstance()->fieldQuery("
            SELECT
                warenausgang_lieferschein.order_id
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.lieferschein_id = '" . (int)$lieferschein_id . "'
        ");
    }


    public static function hasInvoice(int $order_id): bool
    {
        $rechnungs_id = db::getInstance()->fieldQuery("
            SELECT
                buchhaltung_rechnung.rechnungs_id
            FROM
                buchhaltung_rechnung
            WHERE
                buchhaltung_rechnung.order_id = '$order_id'
        ");

        return (bool)$rechnungs_id;
    }


    public static function getOrderDocumentTypes(): array
    {
        return [
            OrderDocument::TYPE_CONFIRMATION => 'Auftragsbestätigung',
            OrderDocument::TYPE_OFFER => 'Angebot',
            OrderDocument::TYPE_PRE_CONFIRMATION => 'Vorabbestätigung',
            OrderDocument::TYPE_ORDER => 'Auftrag',
        ];
    }

    public static function getOrderDocumentTypeName($document_type)
    {
        return self::getOrderDocumentTypes()[$document_type];
    }

    public static function getRechnungsIdByOrderId($order_id)
    {
        return db::getInstance()->fieldQuery("
            SELECT
                orders.rechnungs_id
            FROM
                orders
            WHERE
                orders.order_id = '" . (int)$order_id . "'
        ");
    }

    public static function getRechnungsIdsByOrderId($order_id)
    {
        return db::getInstance()->query("
            SELECT
                buchhaltung_rechnung.rechnungs_id
            FROM
                buchhaltung_rechnung
            WHERE
                buchhaltung_rechnung.order_id = '" . (int)$order_id . "'
        ")->asSingleArray();
    }

    /**
     * gibt die Stornogründe als Array zurück
     * @return array $storno_reasons
     */
    public static function getStornoReasons(): array
    {
        static $reasons = null;

        if ($reasons === null) {
            $reasons = db::getInstance()->query("
                    SELECT
                        einst_storno_reasons.storno_reason_id,
                        einst_storno_reasons.storno_reason,
                        einst_storno_reasons.storno_party
                    FROM
                        einst_storno_reasons
                    ORDER BY
                        einst_storno_reasons.storno_reason
            ")->asArray('storno_reason_id');
        }

        return $reasons;
    }

    /**
     * gibt die Stornogründe als assoziatives Array zurück
     * storno_reason_id => storno_reason
     * @return array
     */
    public static function getStornoReasonsNames(): array
    {
        return ArrayUtils::extract(self::getStornoReasons(), 'storno_reason', 'storno_reason_id');
    }

    /**
     * gibt für den übergeben storno grund die qulle an (we/customer)
     * @param int $storno_reason_id
     * @return string
     */
    public static function getStornoPartyByReasonId(int $storno_reason_id): string
    {
        $reasons = self::getStornoReasons();

        return $reasons[$storno_reason_id]['storno_party'];
    }

    public static function getStornoReason(int $storno_reason_id): string
    {
        $reasons = self::getStornoReasons();

        return $reasons[$storno_reason_id]['storno_reason'];
    }

    public static function getGelangensbestaetigungStatusNames(): array
    {
        return [
            1 => 'erhalten',
            2 => 'nicht erhalten',
            3 => 'unbekannt'
        ];
    }

    public static function getGelangensbestaetigungStatusName(int $status): string
    {
        return self::getGelangensbestaetigungStatusNames()[$status];
    }
}
