const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const SpritesmithPlugin = require('webpack-spritesmith');
require('file-loader');

var public_path = "public/";
//var public_path = "public_demo/";

module.exports = {
	entry: [
		'./asset_src/script.js',
		'./asset_src/style.scss'
	],
	output: {
		path: path.resolve(__dirname, public_path+'assets/'),
		filename: 'dist.js'
	},
	module: {
	 	rules: [{
	 		test: /\.styl$/,
	 		loaders: [
				'style-loader',
				'css-loader',
				'stylus-loader'
			]
	 	},{
			test: /\.css$/,
			use: [
				MiniCssExtractPlugin.loader,
				"css-loader",
				"postcss-loader"
			]
		},{
	 		test: /\.png|.jpg|.svg|.eot|.ttf|.woff|.woff2$/,
	 		loaders: [
				'file-loader?name=[name].[ext]&publicPath=/assets/'
			]
	 	},{
			test: /\.scss$/,
			use: [
				MiniCssExtractPlugin.loader,
				"css-loader",
				"postcss-loader",
				{
					loader: "sass-loader",
					options: {
						implementation: require("sass"),
					},
				}
				],
		}]
	},
	plugins: [
		new MiniCssExtractPlugin({
			filename: "m.css",
			chunkFilename: "m.css"
		})
	]
};
