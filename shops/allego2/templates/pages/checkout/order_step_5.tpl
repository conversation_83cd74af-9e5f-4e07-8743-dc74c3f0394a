{{extends file='frame_full.tpl'}}

{{block name='breadcrumbs'}}
{{/block}}

{{block name='page_content'}}
    {{if !empty($info_message)}}
        {{$info_message}}
    {{/if}}

    {{block name='checkout_summary'}}
        <div class="section">
            <section class="checkout-completion" aria-labelledby="checkout-completion-title">
                <div class="image-banner -checkout-header" aria-hidden="true">
                    <div class="viewport">
                        <div class="content">
                            <img class="icon" src="{{$_cfg.grafik_url}}icons/checkmark-success.svg" alt="" width="64" height="64" />
                            <h1 class="heading">Ihre Bestellung <br class="mobile-break"/>war erfolgreich</h1>
                        </div>
                    </div>
                </div>
                <div class="_space-4 _space-desktop-5"></div>
                <div class="address-form">
                    <div class="column">
                        <div class="_card">
                            <div class="head">
                                <h2 class="title">Was jetzt?</h2>
                            </div>
                            <div class="inner">
                                <p>Ihre Bestellung wird umgehend bearbeitet:</p>
                                <br>
                                <ul>
                                    <li>
                                        <p>Zunächst erhalten Sie eine <strong>Bestellbestätigung per E-Mail</strong>.</p>
                                    </li>
                                    <br>
                                    <li>
                                        <p>Bei einer Bestellung per <strong>Vorkasse</strong> finden Sie die <strong>Bankverbindung</strong> in Ihrer Bestellbestätigungs­mail.</p>
                                    </li>
                                    <br>
                                    <li>
                                        <p>Die <strong>Lieferzeit</strong> der bestellten Ware beginnt <strong>ab dem Zahlungseingang</strong>.</p>
                                    </li>
                                    <br>
                                    <li>
                                        <p>Die Lieferzeit der gesamten Bestellung ist <strong>abhängig</strong> von dem Produkt mit der <strong>längsten Lieferzeit</strong>.</p>
                                    </li>
                                    <br>
                                    <li>
                                        <p>Nach Versand der Ware erhalten Sie eine <strong>Versandbestätigung</strong>.</p>
                                    </li>
                                    <br>
                                </ul>
                                <p>Der Versand erfolgt direkt von unserem Lager oder einem unserer Partner.</p>
                            </div>
                        </div>
                    </div>
                    {{if !$warenkorb->isRegistrated()}}
                        <div class="column">
                            <div class="_card">
                                <div class="head">
                                    <h2 class="title">Kundenkonto erstellen</h2>
                                </div>
                                <div class="inner">
                                    <div class="u-list-checks">
                                        <div class="benefit-item">
                                            <img src="{{$_cfg.grafik_url}}icons/checkmark.svg" alt="" width="24" height="24" />
                                            <span>Schneller bestellen</span>
                                        </div>
                                        <div class="benefit-item">
                                            <img src="{{$_cfg.grafik_url}}icons/checkmark.svg" alt="" width="24" height="24" />
                                            <span>Bestellübersicht</span>
                                        </div>
                                        <div class="benefit-item">
                                            <img src="{{$_cfg.grafik_url}}icons/checkmark.svg" alt="" width="24" height="24" />
                                            <span>Status und Paketverfolgung</span>
                                        </div>
                                    </div>

                                    <form id="register_form" class="register-form" novalidate>
                                        <div class="error-message"></div>

                                        <div class="form-field">
                                            <input type="password" id="password" name="password" class="input" placeholder="Passwort*" required minlength="5" aria-label="Passwort" autocomplete="new-password" />
                                        </div>

                                        <div class="form-field">
                                            <input type="password" id="password_repeat" name="password_repeat" class="input" placeholder="Passwort wiederholen*" required minlength="5" aria-label="Passwort wiederholen" autocomplete="new-password" />
                                        </div>

                                        <button type="button" class="primary-button hover-underline" onclick="checkout.step_success.register()" aria-label="Registrieren">
                                            <span class="label">Registrieren</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    {{/if}}

                    <script type="text/javascript">
                        checkout_init.push('step5');
                        _gaq.push(['_trackPageview', '/virt_trichter1/schritt5.html']);
                    </script>
            </section>
        </div>
        <div class="_space-6 _space-desktop-8"></div>
    {{/block}}

    {{block name='checkout_goodies'}}
        {{if !empty($goodies)}}
            <div class="section">
                <h2 class="section-title">Kennen Sie schon unsere neusten GOODIES?</h2>
                <div class="_space-4 _space-desktop-5"></div>
                {{include file='components/goodie/goodie-list.tpl' layout='grid' show_more_button=true goodies=$goodies}}
            </div>
            <div class="_space-6 _space-desktop-8"></div>
        {{/if}}
    {{/block}}

    {{block name='checkout_categories'}}
        <div class="section">
            <section class="category-cards-grid" id="category-cards-grid" aria-label="Unterkategorien">
                <h2 class="section-title">Top Kategorien entdecken</h2>
                <div class="_space-4 _space-desktop-5"></div>
                <div class="grid" role="list">
                    <a class="card" role="listitem" href="/kochen-backen/">
                        <img class="image" src="/r/cat_logos/13.jpg" alt="Kochen &amp; Backen" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Kochen &amp; Backen</span>
                    </a>
                    <a class="card" role="listitem" href="/kuehlen-gefrieren/">
                        <img class="image" src="/r/cat_logos/553.jpg" alt="Kühlen &amp; Gefrieren" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Kühlen &amp; Gefrieren</span>
                    </a>
                    <a class="card" role="listitem" href="/geschirrspueler/">
                        <img class="image" src="/r/cat_logos/23.jpg" alt="Geschirrspüler" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Geschirrspüler</span>
                    </a>
                    <a class="card" role="listitem" href="/waschen-trocknen/">
                        <img class="image" src="/r/cat_logos/552.jpg" alt="Waschen &amp; Trocknen" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Waschen &amp; Trocknen</span>
                    </a>
                    <a class="card" role="listitem" href="/kaffeegenuss/">
                        <img class="image" src="/r/cat_logos/556.jpg" alt="Kaffeegenuss" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Kaffeegenuss</span>
                    </a>
                    <a class="card" role="listitem" href="/kuechenkleingeraete/">
                        <img class="image" src="/r/cat_logos/50.jpg" alt="Küchenkleingeräte" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Küchenkleingeräte</span>
                    </a>
                    <a class="card" role="listitem" href="/klima-heizung/">
                        <img class="image" src="/r/cat_logos/29.jpg" alt="Klima &amp; Heizung" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Klima &amp; Heizung</span>
                    </a>
                    <a class="card" role="listitem" href="/bodenpflege/">
                        <img class="image" src="/r/cat_logos/37.jpg" alt="Bodenpflege" width="50" height="50" loading="lazy" decoding="async">
                        <span class="name">Bodenpflege</span>
                    </a>
                </div>
            </section>
        </div>
    {{/block}}

{{/block}}



