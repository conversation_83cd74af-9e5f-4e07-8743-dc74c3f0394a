<?php

use bqp\extern\SpbGarantGarantie\SpbGarantGarantie;
use wws\Product\ProductConst;
use wws\Product\ProductExtraServices;
use wws\Product\ProductRepository;
use wws\Shop\Catalog\ShopCatalog;
use wws\Shop\Catalog\ShopCatalogErsatzteilDevices;
use wws\Shop\Catalog\ShopCatalogGoodie;
use wws\Shop\Controller\ShopController;
use wws\Shop\Model\ShopProductFullAllego;
use wws\Shop\Model\ShopProductHeadAllego;
use wws\Shop\ProductFeaturePresets\FeaturePresetService;
use wws\Shop\ProductRating\Interfaces\ProvidesProductRatingData;
use wws\Shop\ProductRating\ProductRatingWriteService;
use wws\Shop\ShopProductCrosssell;
use wws\Shop\ShopResourceClient;
use wws\ShopResource\Entities\ShopResourceMappingType;

class controller_product extends ShopController
{
    use wws\Shop\Controller\TraitControllerProductBase;

    /**
     * @var ShopCatalog
     */
    protected $catalog;

    /**
     * @var ShopCatalogErsatzteilDevices
     */
    protected $device_catalog;

    protected ShopCatalogGoodie $catalog_goodie;

    protected $display_config = [
        'alternativen' => true,
        'zubehoer' => true, //über config::product_templates
        'serie' => true,

        'show_promo_products' => false, //über config::product_templates
        'show_ratings' => false,
        'show_extra_services' => true,
        'show_features' => true,
        'show_sets' => true,
        'show_crosssell' => true,
        'show_alternatives' => true,
        'show_verpackungsentsorgung' => true
    ];

    /**
     * @var FeaturePresetService
     */
    private $feature_preset_service;

    /** @var ProvidesProductRatingData */
    private $product_rating_service;

    /** @var ProductRatingWriteService */
    private $product_rating_write_service;

    public function inject(
        ShopCatalog $catalog,
        ShopCatalogErsatzteilDevices $device_catalog,
        ShopCatalogGoodie $catalog_goodie,
    ): void {
        $this->catalog = $catalog;
        $this->catalog_goodie = $catalog_goodie;
        $this->device_catalog = $device_catalog;
    }


    public function setFeaturePresetService(FeaturePresetService $feature_preset_service): void
    {
        $this->feature_preset_service = $feature_preset_service;
    }

    public function view_product()
    {
        $product_id = (int)$this->shop_request['product_id'];
        $request_cat_id = 0;
        $feature_preset_id = 0;

        $is_extra_service = false;
        $is_features = false;

        $show = preg_replace('~[^a-z]~', '', isset($_GET['show']) ? $_GET['show'] : '');
        $this->tpl->assign('show', $show);

        $loader = $this->catalog->getProductLoader();
        $product = $loader->getProduct($product_id);

        if (!$product) {
            return $this->view_product_not_found();
        }

        if (isset($_REQUEST['cat_id'])) {
            if (substr($_REQUEST['cat_id'], 0, 2) === 'FP') {
                $feature_preset_id = (int)substr($_REQUEST['cat_id'], 2);
            } else {
                $request_cat_id = (int)$_REQUEST['cat_id'];
            }
        }

        //@todo unklar
        $show = preg_replace('~[^a-z]~', '', isset($_GET['show']) ? $_GET['show'] : '');
        $this->tpl->assign('show', $show);


        $template_config = $this->getTemplateConfig($product->getShopTemplate());

        $this->tpl->setCanonicalUrl($product->getProductShopUrl());

        $this->tracker->setPageType('product');
        $this->tracker->addProductView($product);

        $root_cat_id = $this->profile->getMenu()->getRootCatId($product->getCatId());

        $cat_id = null;
        $cat_to_breadcrumbs = true;

        if ($feature_preset_id) {
            $feature_preset = $this->feature_preset_service->getFeaturePreset($feature_preset_id);
            $this->breadcrumbs->addBrotkrummen_feature_preset($feature_preset);

            $cat_to_breadcrumbs = false;

            $cat_id = $product->getCatId();
        } elseif ($request_cat_id) {
            $cat_id = $request_cat_id;
        } else {
            $cat_id = $product->getCatId();
        }

        $cat_id = $this->profile->getMenu()->getNearestVisibleCatId($cat_id);

        if ($cat_id) {
            if ($cat_to_breadcrumbs) {
                $this->breadcrumbs->fillByCatId($cat_id);
            }
            $this->funktion->sideBar($root_cat_id, $cat_id);
            $this->tracker->setCatId($cat_id);
        }

        $this->breadcrumbs->add($product->getProductName(), url('/' . $product->getProductShopUrl()));

        $this->createMetaTags($product);

        //set
        if ($this->display_config['show_sets']) { //@todo -> das ist nur für ersatzteilshop -> set kopf muss unbedingt geladen werden wenn das ein set kopf ist
            $set_product_id = null;
            if (isset($_REQUEST['set_product_id']) && (int)$_REQUEST['set_product_id']) {
                $set_product_id = (int)$_REQUEST['set_product_id'];
            }

            $set_result = $loader->getSetData($product, $set_product_id);

            //@todo -> das ist buggy -> wird nur geladen wenn $set_product_id leer ist
            if ($set_result['product_serie_meta']) { //produkt ist teil einer serie -> anderen serien artikel mit anzeigen
                $this->tpl->assign('product_serie', $set_result['product_serie']);
                $this->tpl->assign('product_serie_meta', $set_result['product_serie_meta']);
                $this->breadcrumbs->add($set_result['set_daten']['product_name'], $set_result['set_daten']['product_shop_url'], -1);
            }

            if ($set_result['set_product_id']) {
                $product->setSetProductId($set_result['set_product_id']);

                if ($product->getShopTemplate() != $set_result['set_shop_template']) {
                    $template_config = $this->getTemplateConfig($set_result['set_shop_template']);
                }

                $this->tpl->assign('vk_brutto_is_ab', $set_result['vk_brutto_is_ab']);

                if ($set_result['variations']) {
                    $this->tpl->assign('variations', $set_result['variations']);
                }

                if ($set_result['serien_products']) {
                    $this->tpl->assign('serien_products', $set_result['serien_products']);

                    $this->display_config['alternativen'] = false;
                }
            }
        }

        //Bilder
        $this->tpl->assign('product_pictures', $loader->getProductPictures($product));

        //extras services
        if ($this->display_config['show_extra_services']) {
            $product_extra_service_data = [];

            $extra_services = ProductExtraServices::getExtraServicesExtended($product->getExtraServices(), true);
            foreach ($extra_services as $extra_service_id => $extra_service) {
                if (trim($extra_service['service_dependencies'])) {
                    $extra_service['service_dependencies'] = explode(',', $extra_service['service_dependencies']);

                    foreach ($extra_service['service_dependencies'] as $i => $dependency_id) {
                        if (!isset($extra_services[$dependency_id])) {
                            unset($extra_service['service_dependencies'][$i]);
                        }
                    }
                } else {
                    $extra_service['service_dependencies'] = [];
                }

                switch ($extra_service['service_handler']) {
                    case 'wertgarantie':
                        $garantie = new SpbGarantGarantie(config::getLegacy('garantie_spb_garant'));
                        $prices = $garantie->calcPricesAll($extra_service['service_handler_extra'], $product->getVkBrutto());

                        $product_extra_service_data[$extra_service_id]['service_vk_brutto_multi'] = $prices;
                        break;
                }

                $extra_service['service_beschreibung_points'] = implode('<br>', $extra_service['service_beschreibung_points']);

                $extra_services[$extra_service_id] = $extra_service;
            }

            uasort($extra_services, function (array $service1, array $service2) {
                if (!$service1['service_dependencies']) {
                    return -1;
                }

                if (!$service2['service_dependencies']) {
                    return 1;
                }

                if (in_array($service2['service_id'], $service1['service_dependencies'])) {
                    return 1;
                }

                if (in_array($service1['service_id'], $service2['service_dependencies'])) {
                    return -1;
                }

                return 0;
            });

            $this->tpl->assign('extra_services', $extra_services);
            $this->tpl->assign('product_extra_service_data', $product_extra_service_data);

            // edge case for display of Trageservice apart from its dependencies
            $this->tpl->assign('notice_service_ids', [14, 40, 41, 55]);
        }

        //features
        if ($this->display_config['show_features']) {
            $features = $loader->getProductFeatures($product);

            $this->tpl->assign('features', $features);
            $is_features = (bool)$features;

            $this->tpl->assign('features_unbound', $loader->getProductFeaturesUnbound($product));
        }

        //associations
        if ($this->display_config['show_crosssell']) {
            $crosssell_products = $loader->getAssociations($product, [ProductConst::ASSOCIATION_TYPE_ID_ZUBEHOER]);
            $this->tpl->assign('zubehoer', $crosssell_products);
        } else {
            $crosssell_products = [];
        }

        if ($this->display_config['show_alternatives']) {
            $alternative_products = $loader->getAssociations($product, [ProductConst::ASSOCIATION_TYPE_ID_ALTERNATIVE]);

            $this->tpl->assign('alternativen', $alternative_products);
        } else {
            $alternative_products = [];
        }

        $crosssell_utils = service_loader::get(ShopProductCrosssell::class);
        $related_combined = $crosssell_utils->mergeProductsPreferAlternatives($alternative_products, $crosssell_products);

        $visible_limit = 6;
        $this->tpl->assign('related_combined', $related_combined);
        $this->tpl->assign('related_visible_limit', $visible_limit);
        $this->tpl->assign('related_has_more', count($related_combined) > $visible_limit);
        $this->tpl->assign('related_remaining_count', max(0, count($related_combined) - $visible_limit));

        if ($product->getProductType() === ProductConst::PRODUCT_TYPE_XET) {
            $this->loadSeoDevices($product->getProductId());
            $this->loadOrderCodes($product->getProductId());
        }

        $this->tpl->assign('product_ratings', []);
        $this->tpl->assign('more_product_ratings_available', false);

        $cart_item = $this->warenkorb->searchItemByProductId($product_id);
        $current_cart_quantity = $cart_item ? $cart_item->getAnzahl() : 0;
        $this->tpl->assign('current_cart_quantity', $current_cart_quantity);

        if ($product->getAverageProductRating() > 0) {
            $product_ratings_collection = $this->product_rating_service->getProductRatingsCollectionByProductId(
                $product_id,
                0,
                100
            );

            $this->tpl->assign('product_ratings', $product_ratings_collection->getProductRatings());
            $this->tpl->assign('more_product_ratings_available', $product_ratings_collection->getMoreProductRatingsAvailable());
        }

        if ($this->display_config['show_ratings'] && isset($product['ratings'])) {
            $ratings = $loader->getProductRatings($product);
            $this->tpl->assign('ratings', $ratings);
        }


        //$this->loadShopResources($product->getProductId());

        //show_verpackungsentsorgung
        if ($product->getPaketfaehig() != -1) {
            $this->display_config['show_verpackungsentsorgung'] = false;
        }

        $this->loadLoadBee($product);

        $this->tpl->assign('product', $product);

        $this->tpl->assign('goodies', $this->catalog_goodie->getTopGoodiesForProduct($product->getProductId()));

        $this->tpl->addLdJson($this->getOfferSchemaData($product));

        $this->tpl->assign('mitigate_missing_h1', true);
        if (str_contains($product->getDescription(), '</h1>')) {
            $this->tpl->assign('mitigate_missing_h1', false);
        }

        $this->tpl->assign('display_config', $this->display_config);
        return $this->tpl->display($template_config['template_file']);
    }

    public function view_product_not_found()
    {
        $this->breadcrumbs->add('Fehler', '');

        error_log('Product does not exist: ' . $_SERVER['REQUEST_URI']);
        header("HTTP/1.1 404 Not Found");
        $this->tpl->displayStd('seiten/product_not_found.tpl');
    }

    public function loadShopResources($product_id)
    {
        $resource_client = new ShopResourceClient();

        $payloads = $resource_client->getResourcePayloads(ShopResourceMappingType::VIDEO_PRODUCT_DESCRIPTION_TOP, $product_id);

        if ($payloads) {
            $this->tpl->assign('product_top_video', $payloads[0]);

            $this->tpl->assign('product_videos', $payloads);
        }
    }

//    public function loadDevicesDb(\shop\model\shop_product_full_allego $product)
//    {
//        if ($product->getProductType() !== ProductConst::PRODUCT_TYPE_XET) {
//            return;
//        }
//
//        $device_reader = $this->profile->getShopDataService()->getDeviceReader();
//
//        $devices = $device_reader->getDevicesForProductId($product->getProductId());
//        if ($devices) {
//            $this->tpl->assign('devices', $devices);
//        }
//
//
//        $passendwie = new ShopCatalogPassendwie();
//        $passendwie->setProductId($product->getProductId());
//        $passendwie_result = $passendwie->execute();
//
//        $this->tpl->assign('passendwie_result', $passendwie_result);
//    }

    public function loadOrderCodes(int $product_id): void
    {
        $order_codes = service_loader::get(ProductRepository::class)
            ->getOrderCodes($product_id);

        $this->tpl->assign('order_codes', $order_codes);
    }

    /**
     * -initalisiert die Geräteliste mit einer geseedeten auswahl an Geräten
     * -initialisiert passendwie
     *
     * @param ShopProductFullAllego $product
     */
    public function loadSeoDevices(int $product_id): void
    {
        $this->device_catalog->setLimit(750);
        $this->device_catalog->setOffset(0);
        $this->device_catalog->setProductIdFilter($product_id);
        $this->device_catalog->setSeoModeForProductIdFilter(true);
        $this->device_catalog->setProductIdHighlight($product_id);
        $this->device_catalog->setOrder('brand');

        $device_result = $this->device_catalog->execute();

        $this->tpl->assign('device_result', $device_result);
    }

    private function loadLoadBee(ShopProductHeadAllego $product): void
    {
        $this->tpl->assign('loadbee_integration_code', '');

        if ($product->getProductType() == 'XET') {
            return;
        }

        if ($product->getEan()) {
            $this->tpl->assign('loadbee_integration_code', '
                <div id="product_description_loadbee">
                    <div id="product_description_loadbee_content" class="product_description_loadbee_content_hidden"></div>
                    <div class="center"><input type="button" class="button" value="vollständige Beschreibung anzeigen" id="product_description_button"></div>
                </div>
                <div class="loadbeeTabContent" data-loadbee-apikey="5XMRBEueCR2LgdA3tzePe7Gp5fwKLVPG" data-loadbee-gtin="' . $product->getEan() . '" data-loadbee-locale="de_DE"></div>
                <script async src="https://cdn.loadbee.com/js/loadbee_integration.js"></script>
            ');
        }
    }

    public function view_device_list(int $product_id, string $phrase): void
    {
        $phrase = strtolower($phrase);
        //$phrase = preg_replace('~[^a-z0-9]~', '', $phrase);
        if ($phrase === '') {
            $this->loadSeoDevices($product_id);
        } else {
            $this->device_catalog->setMaxLimit(20);
            $this->device_catalog->setLimit(20);
            $this->device_catalog->setOffset(0);
            //$this->device_catalog->setProductIdFilter($product_id);
            $this->device_catalog->setProductIdHighlight($product_id);
            $this->device_catalog->setOrder('brand');
            $this->device_catalog->setModelSearchPhrase($phrase);

            $device_result = $this->device_catalog->execute();

            $this->tpl->assign('device_result', $device_result);
        }

        $this->tpl->display('pages/product/sections/product-tab-device-list.tpl');
    }

    public function view_ajax_shipment(): void
    {
        $product_id = (int)$_GET['product_id'];
        if (!$product_id) {
            return;
        }

        $vk_brutto = 0;

        $shipment_extended = $this->shop_shipping_service->getExtendedVersandByProductId($product_id, $vk_brutto);

        $this->getTpl()->assign('shipment_extended', $shipment_extended);
        $this->getTpl()->display('pages/product/sections/part_shipment_extended.tpl');
    }

    /**
     * @param ProvidesProductRatingData $product_rating_service
     */
    public function setProductRatingService(ProvidesProductRatingData $product_rating_service): void
    {
        $this->product_rating_service = $product_rating_service;
    }

    /**
     * Ohne Typdeklaration, da Methode über Ajax aufgerufen wird und alles mögliche kommen kann.
     *
     * @throws SmartyException
     */
    public function view_get_further_product_ratings($product_id, $start)
    {
        $product_id = (int)$product_id;
        $start = (int)$start;

        $result = [
            'rendered_product_ratings' => [],
            'more_product_ratings_available' => false,
        ];

        $product_ratings_collection = $this->product_rating_service->getProductRatingsCollectionByProductId(
            $product_id,
            $start,
            4
        );

        $result['more_product_ratings_available'] = $product_ratings_collection->getMoreProductRatingsAvailable();
        foreach ($product_ratings_collection->getProductRatings() as $product_rating) {
            $this->tpl->assign('product_rating', $product_rating);
            $result['rendered_product_ratings'][] = $this->tpl->fetch('product/rating/product.tpl');
        }
        $this->tpl->jsonDisplay($result);
        exit;
    }

    public function setProductRatingWriteService(ProductRatingWriteService $product_rating_write_service): void
    {
        $this->product_rating_write_service = $product_rating_write_service;
    }
}
