<?php

use bqp\Date\DateObj;
use wws\Product\Envkv\EnvkvProductState;
use wws\Product\ProductConst;
use wws\ProductCsvExport\ProductCsvExport;
use wws\ProductCsvExport\ProductCsvExportFilterOnlyProducts;
use wws\ProductCsvExport\ProductCsvExportUtils;
use wws\Supplier\SupplierRepository;

include('bootstrap.php');
longRun(600, 512);
ProductCsvExportUtils::checkAuth('14K21');

$tracking_id = 'idealo';

$export = new ProductCsvExport(1);
ProductCsvExportUtils::configureExportByTrackingCode($tracking_id, $export);
$export->setQuotes(ProductCsvExport::QUOTES_AUTO);
$export->setFilter('csv_settings');
$export->addFilter(new ProductCsvExportFilterOnlyProducts());
$export->addDependency('lager_meta_full');

$export->addFilterList(9); //österreich filterliste -> versand_at_yn
$export->addFilterList(10); //idealo direkt service

//sonderbehandlung für le creuset und wesco produkte -> dort wollen wir den original produktnamen und nicht den lokalisierten (lange KI Namen)
$export->addAdditionalDataProvider('product_name_clear', function (array $product_ids, ProductCsvExport $export_obj, $db) {
    if (!$product_ids) {
        return [];
    }

    return $db->query("
        SELECT
            product.product_id,
            product.product_name
        FROM
            product
        WHERE
            product.product_id IN (" . $db->in($product_ids) . ") AND
            product.brand_id IN (" . ProductConst::BRAND_ID_LECREUSET . ", " . ProductConst::BRAND_ID_WESCO . ")
    ")->asSingleArray('product_id');
});

$prices = $db->query("
	SELECT
		product_extra_services.service_id,
		product_extra_services.service_vk_brutto
	FROM
		product_extra_services
	WHERE
		product_extra_services.service_id IN (" . $db->in([
        ProductConst::EXTRA_SERVICE_ID_TRAGESERVICE_L,
        ProductConst::EXTRA_SERVICE_ID_TRAGESERVICE_XXL,
        ProductConst::EXTRA_SERVICE_ID_ALTGERAETEENTSORGUNG
    ]) . ")
")->asSingleArray('service_id');

$service_config = [
    'two_man_service' => [
        ProductConst::EXTRA_SERVICE_ID_TRAGESERVICE_L => $prices[ProductConst::EXTRA_SERVICE_ID_TRAGESERVICE_L],
        ProductConst::EXTRA_SERVICE_ID_TRAGESERVICE_XXL => $prices[ProductConst::EXTRA_SERVICE_ID_TRAGESERVICE_XXL]
    ],
    'disposal_service' => [
        ProductConst::EXTRA_SERVICE_ID_ALTGERAETEENTSORGUNG => $prices[ProductConst::EXTRA_SERVICE_ID_ALTGERAETEENTSORGUNG]
    ]
];

$gros_lager_mapping = SupplierRepository::getSuppliersLagerIds();

$export->setRowCallback(function ($daten) use ($service_config, $gros_lager_mapping) {
    $daten['checkout_approved'] = 0;
    $daten['checkout_LimitPerPeriod'] = '';
    $daten['checkout_fulfillmentType'] = '';
    $daten['checkout_two_man_service'] = '';
    $daten['checkout_disposal_service'] = '';

    if (!$daten['filter_list_10']) {
        return $daten;
    }

    if (!$daten['paketfaehig']) {
        return $daten;
    }

    $daten['checkout_approved'] = 1;

    //idealo "bestand"... das ist ein etwas komisches konzept von Idealo. Idealo will kein Bestand, sondern die Infromation
    //wieviele Einheitn pro 24 Stunden verkauft werden können. Das ist relativ problemeaitsch... wir nehmen jetzt erstmal den
    //Bestand des Lieferanten und deckeln den auf 5. Damit verschenken wir zwar, wenn der Bestand an der grnze ist, aber egal.
    //Ansonsten müsste ich hier mitrechnen wieviele Bestellungen wir in den letzten 24h über Idealo Direktkauf hatten.
    $bestand = $daten['lager_frei_1'];

    if (isset($gros_lager_mapping[$daten['supplier_id']])) {
        $bestand = $daten['lager_frei_' . $gros_lager_mapping[$daten['supplier_id']]];

        if ($daten['lager_frei_1'] < 0) { //wenn hauptlager auf negativ ist, muss das mit auf den freien bestand der lieferanten angerechnet werden
            $bestand += $daten['lager_frei_1'];
        }
    }

    if ($bestand > 20) {
        $max = 10;
    } elseif ($bestand > 5) {
        $max = 5;
    } else {
        //auch wenn frei < 0 sein sollte
        $max = 2;
    }

    $daten['checkout_LimitPerPeriod'] = $max;
    $daten['checkout_fulfillmentType'] = $daten['paketfaehig'] > 0 ? 'Paketdienst' : 'Spedition';

    //	if($daten['extra_services']) {
    //		$temp = explode(',', $daten['extra_services']);
//
    //		foreach($service_config['two_man_service'] AS $service_id => $fee) {
    //			if(in_array($service_id, $temp)) {
    //				$daten['checkout_two_man_service'] = $fee;
    //			}
    //		}
//
    //		foreach($service_config['disposal_service'] AS $service_id => $fee) {
    //			if(in_array($service_id, $temp)) {
    //				$daten['checkout_disposal_service'] = $fee;
    //			}
    //		}
    //	}

    return $daten;
});


$export->setFields(
    [
        'product_id',
        'product_name_virt_envkv' => [
            'head' => 'product_name_virt_envkv',
            'field' => '__function',
            'function' => function (array $daten, ProductCsvExport $obj) {
                //sonderbehandlung wegen le creuset und wesco
                if ($daten['product_name_clear']) {
                    return $daten['product_name_clear'];
                }

                //das ist die bestehende logik aus ProductCsvExport
                $product_name = $daten['product_name'];
                $service = $obj->getExtraServiceHighlightText($daten, 'inkl');
                if ($service) {
                    $product_name = $service . ', ' . $product_name;
                }

                if ($daten['product_name_envkv_suffix']) {
                    $product_name .= ', ' . $daten['product_name_envkv_suffix'];
                }

                return $product_name;
            }
        ],
        ['head' => 'hersteller_name', 'field' => 'brand_name'],
        'ean',
        ['head' => 'hersteller_art_nr', 'field' => 'mpn'],
        'vk_brutto',
        'kategorie',
        'deeplink',
        'lieferzeit_text' => [
            'head' => 'lieferzeit_text',
            'field' => '__function',
            'dependencies' => 'lager',
            'function' => function ($daten, ProductCsvExport $obj) {
                if ($daten['lager_frei_1'] > 0 && time() < 1356123600) {
                    return 'Sofort lieferbar, Expressversand bis Weihnachten möglich';
                }

                $lieferzeit = strip_tags($obj->lieferbaranzeige[$daten['lieferbaranzeige']]['lieferbar_text']);

                if ($daten['lager_frei_1'] > 0) {
                    $lieferzeit .= ' - Expressversand möglich';
                } else {
                    $lieferzeit .= ' - kostenlose Abholung in 01445 Radebeul möglich';
                }

                return $lieferzeit;
            }
        ],
        'versand_vorkasse',
        'versand_nachnahme',
        'versand_paypal',
        'versand_notice' => [
            'head' => 'versand_extra',
            'field' => '__function',
            'function' => function ($daten, ProductCsvExport $obj) {
                if ($daten['kategorie'] == 'Klimageräte' && $daten['expressable'] && DateObj::init('2015-07-03 18:00:00')->isFuture()) {
                    return 'Expressversand mit Samstagszustellung möglich';
                }

                if ($daten['vk_brutto'] > 100 && time() < 1323667439) {
                    return 'AKTION: 0% Finanzierung bis zum 11.12.2011';
                }

                $text = $obj->getExtraServiceHighlightText($daten, 'gratis');
                if ($text) {
                    return $text;
                }

                if ($daten['vk_brutto'] >= 250) {
                    return '0% Finanzierung möglich';
                }

                if ($obj->isVerbingungMoeglich($daten)) {
                    return 'Lieferung bis zum Aufstellungsort möglich';
                }

                return 'Kostenlose Abholung in 01445 Radebeul möglich';
            }
        ],
        'versand_sofortueberweisung',
        'bild_400x400',
        'versand_rechnung',
        'versand_kreditkarte',
        'versand_at_yn' => [
            'head' => 'versand_at_yn',
            'field' => '__function',
            'function' => function ($daten) {
                return $daten['versand_eu'] && $daten['filter_list_9'] ? 1 : 0;
            }
        ],
        'versand_at_vorkasse',
        'versand_at_paypal',
        'checkout_approved',
        'checkout_LimitPerPeriod',
        'checkout_fulfillmentType',
        'checkout_two_man_service',
        'checkout_disposal_service',
        'deeplink_at' => [
            'head' => 'deeplink_at',
            'field' => '__function',
            'function' => function ($daten) use ($export) {
                return $export->getShopUrl() . '/' . $daten['product_shop_url'] . '?t=idealoat';
            }
        ],
        ['head' => 'EEC_efficiencyClass', 'field' => 'eek'],
        ['head' => 'EEC_spectrum', 'field' => 'eek_skala'],
        ['head' => 'EEC_labelUrl', 'field' => 'eek_label'],
        ['head' => 'EEC_dataSheetUrl', 'field' => 'eek_datenblatt'],
        [
            'head' => 'EEC_version',
            'field' => '__function',
            'function' => function ($daten) {
                if (!$daten['eek']) {
                    return '';
                }
                return $daten['eek_scale_id'] === EnvkvProductState::ENVKV_SCALE_A_G_2020 ? '1' : '0';
            }
        ]
    ]
);

$export->setSendHeader(true);
$export->execute();
