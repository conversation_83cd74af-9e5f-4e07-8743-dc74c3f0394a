.page-404 {
  > .image-banner {
    > .viewport {
      @include smiley-banner-mask(
        $size-mobile: 700px,
        $size-desktop: 700px,
        $rotation-mobile: 45deg,
        $rotation-desktop: 45deg,
        $gradient: linear-gradient(135deg, #e9e9e9 0%, #f1f1f1 100%),
        $opacity: 1,
        $pos-x-mobile: 50%,
        $pos-y-mobile: 50%,
        $pos-x-desktop: 45%,
        $pos-y-desktop: 100%
      );

      height: var(--module-size);

      @include down-md {
        height: unset;
      }

      background: var(--color-base);
      border-radius: var(--radius-2);
      box-shadow: var(--shadow-1);

      > .grid {
        z-index: 1;
        display: grid;
        grid-template-columns: 1fr 50%;
        height: 100%;

        @include down-md {
          grid-template-columns: 1fr;
          margin-bottom: 20px;
        }

        > .image {
          background: url(/assets/404.png) no-repeat center center;
          background-size: contain;
          margin: 25px;

          @include down-md {
            height: 250px;
            margin: 0;
          }
        }

        > .content {
          display: grid;
          align-content: center;
          justify-items: center;
          width: 55%;
          justify-self: end;
          padding-inline: var(--space-4);
          color: var(--color-black);
          gap: var(--space-4);

          @include down-md {
            width: 100%;
          }

          > .title {
            margin: 0;

            > .label {
              font-size: var(--font-size-h1);
              font-weight: var(--font-weight-bold);
              line-height: 1.1;
              letter-spacing: -0.01em;
            }
          }

          > .heading-2 {
            margin: 0;
          }

          > span:not(.heading-2) {
            font-size: var(--font-size-text1);
            font-weight: var(--font-weight-medium);
            line-height: var(--font-lineheight-text1);
            color: var(--color-text);
          }

          > .action.primary {
            @include button-primary();
            width: 100%;
            max-width: var(--button-width);
          }
        }
      }
    }
  }
}

