
.checkout-completion {
  .image-banner.-checkout-header {
    margin-bottom: var(--space-4);

    > .viewport {
      @include smiley-banner-mask(
        $size-mobile: 300px,
        $size-desktop: 1200px,
        $rotation-mobile: 45deg,
        $rotation-desktop: 25deg,
        $gradient: green,
        $opacity: 0.45,
        $pos-x-mobile: 50%,
        $pos-y-mobile: 50%,
        $pos-x-desktop: 50%,
        $pos-y-desktop: 50%,
      );
      background-color: var(--color-base);
      box-shadow: var(--shadow-1);
      border-radius: var(--radius-2);
      height: 112px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    > .viewport > .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: var(--space-4);
      position: relative;
      z-index: 1;

      > .icon {
        width: 64px;
        height: 64px;
        flex-shrink: 0;
      }

      > .heading {
        font-family: var(--font-sans);
        font-size: 28px;
        font-weight: var(--font-weight-bold);
        line-height: 1.2;
        color: var(--color-text);
        margin: 0;
        width: max-content;

        > .mobile-break {
          display: block;

          @include up-md {
            display: none;
          }
        }
      }
    }
  }

  .address-form {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }
  @include up-lg {
    .address-form {
      flex-direction: row;
      align-items: flex-start;
      gap: var(--space-5);
    }
  }
  .column {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
    flex: 1 1 0;
  }
  .u-list-checks {
    margin-bottom: var(--space-6);
  }
  .u-list-checks .benefit-item img {
    width: var(--icon-size);
    height: var(--icon-size);
    flex-shrink: 0;
  }
  .register-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
  }
  .register-form .error-message {
    display: none;
    color: var(--color-error);
    background: var(--color-error-box);
    padding: var(--space-3);
    border-radius: var(--radius-1);
  }
  .register-form .error-message:not(:empty) {
    display: block;
  }
  .register-form .success-message {
    color: var(--color-confirm);
    font-weight: var(--font-weight-medium);
  }
  ._card .head {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 24px;
  }
  ._card .head .title {
    text-wrap: pretty;
    padding-right: 40px;
  }
}

.section .section-title {
  text-align: center;
}

.section .goodies-grid .more-goodies-btn {
  grid-column: 1 / -1;
  justify-self: center;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0;
}

.section .category-cards-grid {
  display: block;
}

.section .category-cards-grid .grid {
  @include tiles-grid-utility(
    $mobile-cols: 2,
    $desktop-cols: 4
  );
}

.section .category-cards-grid .grid .card {
  box-sizing: border-box;
  @include surface-card(var(--space-4));
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  color: var(--color-text);
  text-decoration: none;
}

.section .category-cards-grid .grid .card:hover .name {
  @media (hover: hover) and (pointer: fine) {
    text-decoration: underline;
  }
}

.section .category-cards-grid .grid .card:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

.section .category-cards-grid .grid .card .image {
  display: block;
  width: var(--icon-size-xl);
  height: var(--icon-size-xl);
  aspect-ratio: 1;
  object-fit: contain;
  flex-shrink: 0;
}

@include up-lg {
  .section .category-cards-grid .grid .card .image {
    width: var(--icon-size-xxl);
    height: var(--icon-size-xxl);
  }
}

.section .category-cards-grid .grid .card .name {
  display: block;
  color: var(--color-text);
  text-align: center;
  font-family: var(--font-sans);
  font-size: var(--font-size-text1);
  font-style: normal;
  font-weight: var(--font-weight-medium);
  line-height: var(--font-lineheight-text1);
  text-wrap: pretty;
  overflow-wrap: anywhere;
  word-break: break-word;
  hyphens: auto;
}

.section .category-cards-grid .grid a.card:last-of-type:nth-of-type(odd) {
  grid-column: 1 / -1;
}

.section .category-cards-grid .grid .card.-extra,
.section .category-cards-grid .grid .action {
  display: none;
}
