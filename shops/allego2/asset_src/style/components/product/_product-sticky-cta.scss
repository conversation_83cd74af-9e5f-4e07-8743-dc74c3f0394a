.product-sticky-cta {
  @include surface-card(var(--space-4));
  box-shadow: var(--shadow-elevated);
  border-radius: var(--card-radius) var(--card-radius) 0 0;
  position: fixed;
  bottom: 0;
  z-index: 100;
  display: none;
  flex-direction: column;
  gap: var(--space-4);
  left: var(--page-padding-x);
  right: var(--page-padding-x);
  transform: scale(0.95);
  transition: transform 0.2s ease;

  @include up-lg {
    display: none !important;
  }

  &.show {
    display: flex;
    transform: scale(1);
  }

  > .price {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    width: 100%;

    > .amount {
      font-size: var(--font-size-h2);
      font-weight: var(--font-weight-bold);
      line-height: var(--font-lineheight-h2);
      color: var(--color-highlight);
      letter-spacing: -0.02em;
      white-space: nowrap;
    }
  }

  > .actions > .qty {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    height: var(--button-height);
    padding-inline: var(--space-5);
    border-radius: var(--button-radius);
    border: 2px solid var(--color-border-strong);
    background-color: var(--color-base);
    box-shadow: var(--shadow-1);

    > .button {
      all: unset;
      box-sizing: border-box;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-h3);
      font-weight: var(--font-weight-bold);
      color: var(--color-text);
      line-height: var(--font-lineheight-h3);
      touch-action: manipulation;

      &:focus-visible {
        @include focus-ring();
      }
    }

    > .input {
      all: unset;
      box-sizing: border-box;
      width: auto;
      -webkit-appearance: none;
      -moz-appearance: textfield;
      appearance: textfield;
      min-width: var(--qty-input-min-width);
      max-width: var(--qty-input-max-width);
      text-align: center;
      font-size: var(--font-size-h3);
      font-weight: var(--font-weight-bold);
      color: var(--color-text);
      line-height: var(--font-lineheight-h3);
      background: transparent;

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &:focus-visible {
        @include focus-ring();
      }
    }
  }
}
