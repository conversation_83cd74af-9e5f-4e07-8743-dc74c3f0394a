// 1. Design tokens
@import "style/abstracts/props";
@import "style/abstracts/mixins";

@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

// 2. Base (reset & typography)
@import "style/base/reset";
@import "style/base/typography";

// 3. Layout
@import "style/layout/layout";
@import "style/layout/sidebar_layout";

// 3.5 Utilities
@import "style/utilities/helpers";
@import "style/utilities/card-grid";
@import "style/utilities/energy-label";
@import "style/utilities/surface";
@import "style/utilities/form";
@import "style/utilities/more-goodies-action";
@import "style/utilities/buttons";
@import "style/utilities/button-bar";
@import "style/utilities/page-header";
@import "style/utilities/tiles-grid";
@import "style/utilities/tables";

// 4. Components
@import "style/components/accordion";
@import "style/components/financing";
@import "style/components/header/header";
@import "style/components/header/header-menu";
@import "style/components/header/search-bar";
@import "style/components/layout/cart-widget";
@import "style/components/layout/footer";
@import "style/components/layout/back-to-top";
@import "style/components/layout/image-banner";
@import "style/components/layout/breadcrumb-nav";
@import "style/components/layout/category-sidebar";
@import "style/components/cart/cart-item-card";
@import "style/components/cart/cart-item-services";
@import "style/components/product/product-base";
@import "style/components/product/product-card";
@import "style/components/product/cart-product-card";
@import "style/components/product/product-box";
@import "style/components/sticky-cta";
@import "style/components/product/product-sticky-cta";
@import "style/components/product/product-listing";
@import "style/components/listing/listing-filter";
@import "style/components/search/search";
@import "style/components/goodie/goodie-card";
@import "style/components/goodie/goodie-list";
@import "style/components/device-search";
@import "style/components/pagination";
@import "style/components/toast-notification";
@import "style/components/customer-information";
@import "style/components/filter/filter";
@import "style/components/basket-modal-window";
@import "style/components/benefits";

// 5. Pages
@import "style/page/category";
@import "style/page/homepage";
@import "style/page/404";
@import "style/page/goodies";
@import "style/page/product";
@import "style/page/cart";
@import "style/page/checkout";
@import "style/page/checkout/completion";
@import "style/page/customer";
@import "style/page/search";
@import "style/page/service";
@import "style/page/homepage_top_categories";
@import "style/page/device";

@import "style/ui/modal-window";
@import "style/ui/autocomplete";

// 6. Legacy styling shims kept for JS-powered modals
@import "style/legacy/loadbee";

/* */
